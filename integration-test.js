#!/usr/bin/env node

/**
 * Integration Testing Script for Butler Application
 * Tests frontend-backend integration and new personalization features
 */

import http from 'http';
import { performance } from 'perf_hooks';

const BACKEND_URL = 'http://localhost:3001';
const FRONTEND_URL = 'http://localhost:3000';

class IntegrationTester {
  constructor() {
    this.testResults = [];
    this.errors = [];
  }

  async makeRequest(url, options = {}) {
    return new Promise((resolve, reject) => {
      const startTime = performance.now();
      
      const req = http.get(url, options, (res) => {
        let data = '';
        res.on('data', chunk => data += chunk);
        res.on('end', () => {
          const endTime = performance.now();
          const responseTime = endTime - startTime;
          
          try {
            const jsonData = res.headers['content-type']?.includes('application/json') 
              ? JSON.parse(data) 
              : data;
            
            resolve({
              statusCode: res.statusCode,
              responseTime,
              data: jsonData,
              headers: res.headers,
              success: res.statusCode >= 200 && res.statusCode < 300
            });
          } catch (parseError) {
            resolve({
              statusCode: res.statusCode,
              responseTime,
              data,
              headers: res.headers,
              success: res.statusCode >= 200 && res.statusCode < 300,
              parseError: parseError.message
            });
          }
        });
      });

      req.on('error', (error) => {
        const endTime = performance.now();
        reject({
          error: error.message,
          responseTime: endTime - startTime,
          success: false
        });
      });

      req.setTimeout(10000, () => {
        req.destroy();
        reject({
          error: 'Request timeout',
          responseTime: 10000,
          success: false
        });
      });
    });
  }

  async testEndpoint(name, url, expectedStatus = 200, validator = null) {
    console.log(`🧪 Testing ${name}...`);
    
    try {
      const result = await this.makeRequest(url);
      
      const test = {
        name,
        url,
        success: result.success && result.statusCode === expectedStatus,
        statusCode: result.statusCode,
        responseTime: result.responseTime,
        error: null,
        validation: null
      };

      if (validator && result.success) {
        try {
          const validationResult = validator(result.data);
          test.validation = validationResult;
          if (!validationResult.valid) {
            test.success = false;
            test.error = `Validation failed: ${validationResult.message}`;
          }
        } catch (validationError) {
          test.success = false;
          test.error = `Validation error: ${validationError.message}`;
        }
      }

      if (!result.success) {
        test.error = `HTTP ${result.statusCode}`;
      }

      this.testResults.push(test);
      
      if (test.success) {
        console.log(`  ✅ ${name} - ${result.responseTime.toFixed(2)}ms`);
      } else {
        console.log(`  ❌ ${name} - ${test.error}`);
      }

      return test;
    } catch (error) {
      const test = {
        name,
        url,
        success: false,
        statusCode: null,
        responseTime: error.responseTime || 0,
        error: error.error || error.message,
        validation: null
      };
      
      this.testResults.push(test);
      console.log(`  ❌ ${name} - ${test.error}`);
      return test;
    }
  }

  // Validation functions
  validateOutletsResponse(data) {
    if (!data || !data.success) {
      return { valid: false, message: 'Response not successful' };
    }
    if (!Array.isArray(data.data)) {
      return { valid: false, message: 'Data is not an array' };
    }
    if (data.data.length === 0) {
      return { valid: false, message: 'No outlets returned' };
    }
    
    const outlet = data.data[0];
    const requiredFields = ['_id', 'name', 'address', 'city'];
    for (const field of requiredFields) {
      if (!outlet[field]) {
        return { valid: false, message: `Missing required field: ${field}` };
      }
    }
    
    return { valid: true, message: 'Valid outlets response' };
  }

  validateDishesResponse(data) {
    if (!data || !data.success) {
      return { valid: false, message: 'Response not successful' };
    }
    if (!Array.isArray(data.data)) {
      return { valid: false, message: 'Data is not an array' };
    }
    if (data.data.length === 0) {
      return { valid: false, message: 'No dishes returned' };
    }
    
    const dish = data.data[0];
    const requiredFields = ['_id', 'name', 'price', 'category'];
    for (const field of requiredFields) {
      if (!dish[field]) {
        return { valid: false, message: `Missing required field: ${field}` };
      }
    }
    
    return { valid: true, message: 'Valid dishes response' };
  }

  validatePersonalizedResponse(data) {
    if (!data || !data.success) {
      return { valid: false, message: 'Response not successful' };
    }
    if (!data.data) {
      return { valid: false, message: 'No data in response' };
    }
    
    const responseData = data.data;
    if (typeof responseData.isPersonalized !== 'boolean') {
      return { valid: false, message: 'Missing isPersonalized field' };
    }
    if (typeof responseData.profileCompleteness !== 'number') {
      return { valid: false, message: 'Missing profileCompleteness field' };
    }
    
    return { valid: true, message: 'Valid personalized response' };
  }

  async runBackendTests() {
    console.log('\n🔧 BACKEND API TESTS');
    console.log('='.repeat(40));

    const tests = [
      {
        name: 'Health Check',
        url: `${BACKEND_URL}/health`,
        validator: null
      },
      {
        name: 'Get Outlets',
        url: `${BACKEND_URL}/api/v1/user/outlets?limit=5`,
        validator: this.validateOutletsResponse
      },
      {
        name: 'Intelligent Outlet Recommendations',
        url: `${BACKEND_URL}/api/v1/user/intelligent-outlets?city=Mumbai&limit=5`,
        validator: (data) => {
          if (!data || !data.success) {
            return { valid: false, message: 'Response not successful' };
          }
          if (!data.data || !data.data.recommendations) {
            return { valid: false, message: 'Missing recommendations data' };
          }
          return { valid: true, message: 'Valid intelligent outlets response' };
        }
      },
      {
        name: 'Get Dishes',
        url: `${BACKEND_URL}/api/v1/user/dishes?foodChainId=6899f5527403b47ea1df69a2&outletId=6899fa5358dbcc8f5ced8116`,
        validator: this.validateDishesResponse
      },
      {
        name: 'Personalized Menu',
        url: `${BACKEND_URL}/api/v1/user/personalized-menu?foodChainId=6899f5527403b47ea1df69a2`,
        validator: this.validatePersonalizedResponse
      },
      {
        name: 'User Taste Profile',
        url: `${BACKEND_URL}/api/v1/user/taste-profile`,
        expectedStatus: 400, // Should fail without userId
        validator: null
      }
    ];

    for (const test of tests) {
      await this.testEndpoint(
        test.name,
        test.url,
        test.expectedStatus || 200,
        test.validator
      );
    }
  }

  async runFrontendTests() {
    console.log('\n🌐 FRONTEND TESTS');
    console.log('='.repeat(40));

    const tests = [
      {
        name: 'Frontend Home Page',
        url: `${FRONTEND_URL}/`,
        validator: (data) => {
          if (typeof data !== 'string') {
            return { valid: false, message: 'Response is not HTML string' };
          }
          if (!data.includes('<!DOCTYPE html>') && !data.includes('<html')) {
            return { valid: false, message: 'Response is not valid HTML' };
          }
          return { valid: true, message: 'Valid HTML response' };
        }
      },
      {
        name: 'Outlets Page',
        url: `${FRONTEND_URL}/outlets`,
        validator: (data) => {
          if (typeof data !== 'string') {
            return { valid: false, message: 'Response is not HTML string' };
          }
          return { valid: true, message: 'Outlets page accessible' };
        }
      }
    ];

    for (const test of tests) {
      await this.testEndpoint(
        test.name,
        test.url,
        200,
        test.validator
      );
    }
  }

  async runIntegrationTests() {
    console.log('\n🔗 INTEGRATION TESTS');
    console.log('='.repeat(40));

    // Test the flow: Get outlets -> Get dishes -> Get personalized menu
    try {
      console.log('🧪 Testing complete user flow...');
      
      // Step 1: Get outlets
      const outletsResult = await this.makeRequest(`${BACKEND_URL}/api/v1/user/outlets?limit=1`);
      if (!outletsResult.success || !outletsResult.data.data.length) {
        throw new Error('Failed to get outlets');
      }
      
      const outlet = outletsResult.data.data[0];
      const outletId = outlet._id;
      const foodChainId = outlet.foodChain._id;
      
      console.log(`  ✅ Got outlet: ${outlet.name}`);
      
      // Step 2: Get dishes for this outlet
      const dishesResult = await this.makeRequest(
        `${BACKEND_URL}/api/v1/user/dishes?foodChainId=${foodChainId}&outletId=${outletId}`
      );
      if (!dishesResult.success || !dishesResult.data.data.length) {
        throw new Error('Failed to get dishes');
      }
      
      console.log(`  ✅ Got ${dishesResult.data.data.length} dishes`);
      
      // Step 3: Get personalized menu
      const personalizedResult = await this.makeRequest(
        `${BACKEND_URL}/api/v1/user/personalized-menu?foodChainId=${foodChainId}`
      );
      if (!personalizedResult.success) {
        throw new Error('Failed to get personalized menu');
      }
      
      console.log(`  ✅ Got personalized menu (completeness: ${personalizedResult.data.data.profileCompleteness}%)`);
      
      this.testResults.push({
        name: 'Complete User Flow',
        success: true,
        responseTime: outletsResult.responseTime + dishesResult.responseTime + personalizedResult.responseTime,
        error: null
      });
      
    } catch (error) {
      console.log(`  ❌ Integration flow failed: ${error.message}`);
      this.testResults.push({
        name: 'Complete User Flow',
        success: false,
        responseTime: 0,
        error: error.message
      });
    }
  }

  generateReport() {
    console.log('\n' + '='.repeat(60));
    console.log('📊 INTEGRATION TEST REPORT');
    console.log('='.repeat(60));

    const totalTests = this.testResults.length;
    const passedTests = this.testResults.filter(t => t.success).length;
    const failedTests = totalTests - passedTests;
    const successRate = ((passedTests / totalTests) * 100).toFixed(1);

    console.log(`\n📈 SUMMARY:`);
    console.log(`   Total Tests: ${totalTests}`);
    console.log(`   Passed: ${passedTests}`);
    console.log(`   Failed: ${failedTests}`);
    console.log(`   Success Rate: ${successRate}%`);

    const avgResponseTime = this.testResults
      .filter(t => t.success && t.responseTime)
      .reduce((sum, t) => sum + t.responseTime, 0) / passedTests;
    
    console.log(`   Average Response Time: ${avgResponseTime.toFixed(2)}ms`);

    if (failedTests > 0) {
      console.log(`\n❌ FAILED TESTS:`);
      this.testResults
        .filter(t => !t.success)
        .forEach(t => {
          console.log(`   • ${t.name}: ${t.error}`);
        });
    }

    console.log(`\n🎯 RECOMMENDATIONS:`);
    if (successRate < 90) {
      console.log(`   • Critical: Success rate is below 90% (${successRate}%)`);
    }
    if (avgResponseTime > 1000) {
      console.log(`   • Warning: Average response time is high (${avgResponseTime.toFixed(0)}ms)`);
    }
    if (passedTests === totalTests) {
      console.log(`   • ✅ All tests passed! System is functioning correctly.`);
    }
    
    console.log(`   • Monitor response times under load`);
    console.log(`   • Implement proper error handling for failed requests`);
    console.log(`   • Consider adding retry mechanisms for critical operations`);
  }

  async runAllTests() {
    console.log('🚀 Starting Butler Application Integration Tests');
    console.log('='.repeat(60));

    await this.runBackendTests();
    await this.runFrontendTests();
    await this.runIntegrationTests();
    
    this.generateReport();
  }
}

// Run the tests
async function main() {
  const tester = new IntegrationTester();
  
  try {
    await tester.runAllTests();
  } catch (error) {
    console.error('❌ Test execution failed:', error);
  }
}

if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export default IntegrationTester;
