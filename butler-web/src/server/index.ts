import axios from "axios";

export const server = axios.create({
  baseURL: `${process.env.NEXT_PUBLIC_BASE_URL}/api`,
  headers: {
    "Content-Type": "application/json",
  },
});

// Add request interceptor to automatically attach tokens
server.interceptors.request.use(
  (config) => {
    // Check if Authorization header is already set
    if (!config.headers.Authorization) {
      // Determine which token to use based on the request URL
      let token = null;

      if (config.url?.includes('/super-admin/')) {
        token = localStorage.getItem("super-auth-token");
      } else if (config.url?.includes('/admin/')) {
        token = localStorage.getItem("auth-token");
      } else if (config.url?.includes('/user/')) {
        token = localStorage.getItem("user-token");
      }

      // Attach token if found
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
    }

    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Add response interceptor
server.interceptors.response.use(
  (response) => response,
  (error) => {
    // Handle both 401 (unauthorized/expired token) and 403 (forbidden) status codes
    if (error.response?.status === 401 || error.response?.status === 403) {
      // Clear all auth tokens
      localStorage.removeItem("auth-token");
      localStorage.removeItem("super-auth-token");
      localStorage.removeItem("user-token");
      localStorage.removeItem("chainId");
      localStorage.removeItem("userId");

      // Clear cookies
      document.cookie =
        "auth-token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT";
      document.cookie =
        "super-auth-token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT";
      document.cookie =
        "user-token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT";

      // Check which type of user was authenticated and redirect to appropriate login
      const path = window.location.pathname;
      if (path.startsWith("/super-admin")) {
        window.location.href = "/super-admin-login";
      } else if (path.startsWith("/admin")) {
        window.location.href = "/login";
      }
      //  else {
      //   // For regular users, redirect to home page (which should show login options)
      //   window.location.href = "/";
      // }
    }
    return Promise.reject(error);
  }
);

export const getAdminToken = () => {
  const token = localStorage.getItem("auth-token");
  return token;
};

export const getSuperAdminToken = () => {
  const token = localStorage.getItem("super-auth-token");
  return token;
};

export const getAdminFoodChainId = () => {
  const chainId = localStorage.getItem("chainId");
  return chainId;
};

export const getUserToken = () => {
  const token = localStorage.getItem("user-token");
  return token;
};

export const getUserId = () => {
  const userId = localStorage.getItem("userId");
  return userId;
};
