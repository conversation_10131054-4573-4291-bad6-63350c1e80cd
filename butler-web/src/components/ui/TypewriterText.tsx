"use client";

import React, { useState, useEffect, useRef } from "react";

interface StreamingTextProps {
  text: string;
  className?: string;
  isStreaming?: boolean; // Whether the text is still being streamed from backend
}

// Helper function to strip HTML tags and get plain text
const stripHtml = (html: string): string => {
  if (typeof window === 'undefined') return html;
  const div = document.createElement('div');
  div.innerHTML = html;
  return div.textContent || div.innerText || '';
};

// Helper function to get HTML up to a certain character position
const getHtmlUpToPosition = (html: string, position: number): string => {
  if (typeof window === 'undefined') return html;

  const div = document.createElement('div');
  div.innerHTML = html;

  let currentPos = 0;
  let result = '';

  const traverse = (node: Node): boolean => {
    if (currentPos >= position) return false;

    if (node.nodeType === Node.TEXT_NODE) {
      const text = node.textContent || '';
      const remainingChars = position - currentPos;

      if (remainingChars >= text.length) {
        result += text;
        currentPos += text.length;
      } else {
        result += text.slice(0, remainingChars);
        currentPos = position;
        return false;
      }
    } else if (node.nodeType === Node.ELEMENT_NODE) {
      const element = node as Element;
      const tagName = element.tagName.toLowerCase();

      // Add opening tag
      result += `<${tagName}`;

      // Add attributes
      for (let i = 0; i < element.attributes.length; i++) {
        const attr = element.attributes[i];
        result += ` ${attr.name}="${attr.value}"`;
      }
      result += '>';

      // Process children
      for (let i = 0; i < node.childNodes.length; i++) {
        if (!traverse(node.childNodes[i])) break;
      }

      // Add closing tag
      result += `</${tagName}>`;
    }

    return true;
  };

  for (let i = 0; i < div.childNodes.length; i++) {
    if (!traverse(div.childNodes[i])) break;
  }

  return result;
};

const StreamingText: React.FC<StreamingTextProps> = ({
  text,
  className = "",
  isStreaming = false,
}) => {
  const [displayedText, setDisplayedText] = useState("");
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isTyping, setIsTyping] = useState(false);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const previousTextRef = useRef("");
  const plainTextRef = useRef("");

  // Get plain text for character counting
  useEffect(() => {
    plainTextRef.current = stripHtml(text);
  }, [text]);

  useEffect(() => {
    const plainText = plainTextRef.current;

    // If text hasn't changed significantly, don't restart animation
    if (text === previousTextRef.current) {
      return;
    }

    // If new text is shorter (shouldn't happen in streaming), reset
    if (plainText.length < currentIndex) {
      setCurrentIndex(0);
      setDisplayedText("");
    }

    // Clear any existing interval
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }

    // Start typing animation if we have new content to show
    if (currentIndex < plainText.length) {
      setIsTyping(true);

      intervalRef.current = setInterval(() => {
        setCurrentIndex((prevIndex) => {
          const newIndex = prevIndex + 1;

          if (newIndex >= plainText.length) {
            setIsTyping(false);
            if (intervalRef.current) {
              clearInterval(intervalRef.current);
              intervalRef.current = null;
            }
            return plainText.length;
          }

          return newIndex;
        });
      }, 30); // 30ms per character for smooth typing
    }

    previousTextRef.current = text;

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [text, currentIndex]);

  // Update displayed text based on current index
  useEffect(() => {
    const plainText = plainTextRef.current;
    if (currentIndex <= plainText.length) {
      setDisplayedText(getHtmlUpToPosition(text, currentIndex));
    }
  }, [text, currentIndex]);

  // Speed up when streaming stops
  useEffect(() => {
    const plainText = plainTextRef.current;
    if (!isStreaming && currentIndex < plainText.length && isTyping) {
      // Speed up completion when streaming stops
      if (intervalRef.current) {
        clearInterval(intervalRef.current);

        intervalRef.current = setInterval(() => {
          setCurrentIndex((prevIndex) => {
            const newIndex = prevIndex + 3; // Show 3 characters at once

            if (newIndex >= plainText.length) {
              setIsTyping(false);
              if (intervalRef.current) {
                clearInterval(intervalRef.current);
                intervalRef.current = null;
              }
              return plainText.length;
            }

            return newIndex;
          });
        }, 10); // Faster interval
      }
    }
  }, [isStreaming, currentIndex, isTyping]);

  const plainText = plainTextRef.current;

  return (
    <div className={`${className} typewriter-container`}>
      <span dangerouslySetInnerHTML={{ __html: displayedText }} />
      {isTyping && currentIndex < plainText.length && (
        <span className="typewriter-cursor animate-pulse">|</span>
      )}
    </div>
  );
};

export default StreamingText;
