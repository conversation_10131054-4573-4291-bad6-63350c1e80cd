"use client";
import { useGlobalOrderNotifications } from "@/hooks/useGlobalOrderNotifications";

/**
 * Global Order Notifications Component
 * 
 * This component should be included in the admin layout to provide
 * order notifications across all admin pages (except the orders page itself
 * to avoid duplicate notifications).
 * 
 * Features:
 * - Toast notifications for new orders, order updates, payment updates
 * - Sound notifications for important events
 * - Browser notifications (with permission)
 * - Action buttons to navigate to orders page
 * - Automatic exclusion on orders page to prevent duplicates
 */
export default function GlobalOrderNotifications() {
  // This hook handles all the notification logic
  useGlobalOrderNotifications();

  // This component doesn't render anything visible
  // All notifications are handled through the toast system
  return null;
}
