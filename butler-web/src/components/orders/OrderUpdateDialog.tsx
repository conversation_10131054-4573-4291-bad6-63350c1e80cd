"use client";

import { useState, useEffect, useCallback } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  Di<PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { toast } from "sonner";
import {
  Loader2,
  Plus,
  Minus,
  ShoppingCart,
  Edit3,
  Tag,
  // X,
  ChevronDown,
  ChevronUp,
  Utensils,
  CheckCircle,
  ShoppingBag,
  ChefHat,
  Gift,
  Calculator,
} from "lucide-react";
import { updateOrderItems } from "@/server/user";
import { getOutletMenu } from "@/server/user";

interface OrderItem {
  dishId?: {
    _id: string;
    name: string;
    price: number;
    enabledAddOns?: Array<{
      _id: string;
      name: string;
      price: number;
      type?: string;
    }>;
  } | null;
  dishName?: string; // Stored dish name for deleted dishes
  quantity: number;
  price: number;
  // Optional add-ons applied to this item
  addOns?: Array<{
    addOnId?: string;
    _id?: string;
    name: string;
    price: number;
    quantity?: number;
  }>;
  isServed?: boolean;
  servedQuantity?: number;
}

interface Order {
  _id: string;
  items: OrderItem[];
  totalAmount: number;
  finalAmount: number;
  paymentStatus: string;
  status: string;
  couponCode?: string;
  couponDiscount?: number;
  discountType?: string;
  discountValue: number;
  appliedOffers?: Array<{
    offerId: string;
    offerName: string;
    offerType: string;
    discount: number;
    discountType: string;
    freeItems?: Array<{
      dishId: string;
      dishName: string;
      quantity: number;
      price: number;
    }>;
  }>;
  offerDiscount?: number;
}

interface Dish {
  _id: string;
  name: string;
  price: number;
  category: string;
  description?: string;
  image?: string;
  isAvailable: boolean;
  enabledAddOns?: Array<{
    _id: string;
    name: string;
    price: number;
    type?: string;
  }>;
}

interface OrderUpdateDialogProps {
  order: Order;
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

export default function OrderUpdateDialog({
  order,
  isOpen,
  onClose,
  onSuccess,
}: OrderUpdateDialogProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [dishes, setDishes] = useState<Dish[]>([]);
  const [updatedItems, setUpdatedItems] = useState<OrderItem[]>([]);
  const [couponCode, setCouponCode] = useState("");
  const [appliedCoupon, setAppliedCoupon] = useState<{
    code: string;
    discount: number;
    discountType: string;
    discountValue: number;
  } | null>(null);
  const [appliedOffers, setAppliedOffers] = useState<
    Array<{
      offerId: string;
      offerName: string;
      offerType: string;
      discount: number;
      discountType: string;
      freeItems?: Array<{
        dishId: string;
        dishName: string;
        quantity: number;
        price: number;
      }>;
    }>
  >([]);
  const [couponLoading, setCouponLoading] = useState(false);
  const [isAddDishesExpanded, setIsAddDishesExpanded] = useState(false);

  type AddOnSel = {
    addOnId?: string;
    _id?: string;
    name: string;
    price: number;
    quantity?: number;
  };

  const revalidateCoupon = useCallback(async (couponCode: string, amount: number) => {
    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_BASE_URL}/api/v1/user/coupons/validate`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${localStorage.getItem("user-token")}`,
          },
          body: JSON.stringify({
            code: couponCode,
            outletId: localStorage.getItem("outletId") || "",
            amount: amount,
          }),
        }
      );

      const result = await response.json();

      if (result.success) {
        setAppliedCoupon({
          code: couponCode.toUpperCase(),
          discount: result.data.discount,
          discountType: result.data.coupon.discountType,
          discountValue: result.data.coupon.discountValue,
        });
      } else {
        // Coupon is no longer valid, remove it
        setAppliedCoupon(null);
        toast.error(`Coupon removed: ${result.message}`);
      }
    } catch (error) {
      console.error("Error revalidating coupon:", error);
      // Remove coupon on error to be safe
      setAppliedCoupon(null);
      toast.error("Coupon removed due to validation error");
    } 
  }, []);

  useEffect(() => {
    if (isOpen) {
      setUpdatedItems([...order.items]);
      setCouponCode("");

      // Set coupon from order data
      setAppliedCoupon(
        order.couponCode && order.couponDiscount
          ? {
              code: order.couponCode,
              discount: order.couponDiscount,
              discountType: order.discountType || "fixed", // We'll get this from validation
              discountValue: order.discountValue,
            }
          : null
      );

      // Initialize applied offers from order
      setAppliedOffers(order.appliedOffers || []);
      fetchDishes();
    }
  }, [isOpen, order]);

  const fetchDishes = async () => {
    const outletId = localStorage.getItem("outletId") || "";
    const foodChainId = localStorage.getItem("chainId") || "";
    try {
      const response = await getOutletMenu(foodChainId, outletId);
      if (response.success) {
        setDishes(response.data.filter((dish: Dish) => dish.isAvailable));
      }
    } catch (error) {
      console.error("Error fetching dishes:", error);
      toast.error("Failed to load dishes");
    }
  };

  const addDishToOrder = (dish: Dish) => {
    const existingItemIndex = updatedItems.findIndex(
      (item) => item.dishId?._id === dish._id
    );

    if (existingItemIndex >= 0) {
      const newItems = [...updatedItems];
      newItems[existingItemIndex].quantity += 1;
      setUpdatedItems(newItems);
    } else {
      const newItem: OrderItem = {
        dishId: {
          _id: dish._id,
          name: dish.name,
          price: dish.price,
        },
        dishName: dish.name,
        quantity: 1,
        price: dish.price,
        isServed: false,
      };
      setUpdatedItems([...updatedItems, newItem]);
    }
  };

  const updateItemQuantity = (itemIndex: number, newQuantity: number) => {
    const item = updatedItems[itemIndex];
    const servedQuantity = item.servedQuantity || 0;

    // If dish has served quantities, don't allow reducing quantity below served amount
    if (newQuantity < servedQuantity) {
      toast.error(
        `Cannot reduce quantity below served amount (${servedQuantity}). Current served: ${servedQuantity}, minimum allowed: ${servedQuantity}`
      );
      return;
    }

    if (newQuantity <= 0) {
      // Don't allow removing dishes with served quantities
      if (servedQuantity > 0) {
        toast.error(
          `Cannot remove dishes that have been served. This dish has ${servedQuantity} served items.`
        );
        return;
      }
      const newItems = updatedItems.filter((_, index) => index !== itemIndex);
      setUpdatedItems(newItems);
    } else {
      const newItems = [...updatedItems];
      newItems[itemIndex].quantity = newQuantity;
      setUpdatedItems(newItems);
    }
  };

  const handleApplyCoupon = async () => {
    if (!couponCode.trim()) {
      toast.error("Please enter a coupon code");
      return;
    }

    const subtotal = calculateSubtotal();

    setCouponLoading(true);
    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_BASE_URL}/api/v1/user/coupons/validate`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${localStorage.getItem("user-token")}`,
          },
          body: JSON.stringify({
            code: couponCode,
            outletId: localStorage.getItem("outletId") || "",
            amount: subtotal,
          }),
        }
      );

      const result = await response.json();

      if (result.success) {
        setAppliedCoupon({
          code: couponCode.toUpperCase(),
          discount: result.data.discount,
          discountType: result.data.coupon.discountType,
          discountValue: result.data.coupon.discountValue,
        });
        toast.success("Coupon applied successfully!");
        setCouponCode("");
      } else {
        toast.error(result.message || "Failed to apply coupon");
      }
    } catch (error) {
      console.error("Error applying coupon:", error);
      toast.error("Failed to apply coupon");
    } finally {
      setCouponLoading(false);
    }
  };

  // const removeCoupon = () => {
  //   setAppliedCoupon(null);
  //   setCouponExplicitlyRemoved(true);
  //   toast.success("Coupon removed");
  // };

  const calculateSubtotal = useCallback(() => {
    return updatedItems.reduce((sum, item) => {
      const addOns = (item.addOns || []) as AddOnSel[];
      const addOnsTotal = addOns.reduce(
        (s, ao) => s + (Number(ao.price) || 0) * (Number(ao.quantity) || 1),
        0
      );
      return sum + (item.price * item.quantity) + addOnsTotal;
    }, 0);
  }, [updatedItems]);

  // Estimate offer discount based on current subtotal (for real-time updates)
  const estimateOfferDiscount = () => {
    const subtotal = calculateSubtotal();
    let estimatedDiscount = 0;

    appliedOffers.forEach((offer) => {
      if (offer.discountType === "percentage") {
        // For percentage offers, recalculate based on current subtotal
        const originalOfferPercentage =
          (offer.discount / order.totalAmount) * 100;
        estimatedDiscount += (subtotal * originalOfferPercentage) / 100;
      } else {
        // For fixed offers, use the original amount (may not be accurate but better than nothing)
        estimatedDiscount += offer.discount;
      }
    });

    return estimatedDiscount;
  };

  // Estimate coupon discount based on current subtotal (for real-time updates)
  const estimateCouponDiscount = () => {
    if (!appliedCoupon) return 0;

    // Fallback to calculation if no validated discount is available
    const subtotal = calculateSubtotal();
    const offerDiscount = estimateOfferDiscount();
    const amountAfterOffers = Math.max(0, subtotal - offerDiscount);
    if (appliedCoupon.discountType === "percentage") {
      return amountAfterOffers * (appliedCoupon.discountValue / 100);
    } else {
      // For fixed coupons, ensure it doesn't exceed amount after offers
      return Math.min(appliedCoupon.discountValue || 0, amountAfterOffers);
    }
  };

  const calculateOfferDiscount = () => {
    return estimateOfferDiscount();
  };

  const calculateTotal = () => {
    const subtotal = calculateSubtotal();
    const offerDiscount = estimateOfferDiscount();
    const couponDiscount = estimateCouponDiscount();

    // Apply offers first, then coupons (matching backend logic)
    const amountAfterOffers = Math.max(0, subtotal - offerDiscount);
    return Math.max(0, amountAfterOffers - couponDiscount);
  };

  // Add-on helpers for items in this dialog
  const toggleItemAddOn = (
    itemIndex: number,
    addOn: { _id: string; name: string; price: number }
  ) => {
    setUpdatedItems((prev) => {
      const copy = [...prev];
      const target = { ...copy[itemIndex] } as OrderItem & {
        addOns?: AddOnSel[];
      };
      const list: AddOnSel[] = Array.isArray(target.addOns)
        ? [...target.addOns]
        : [];
      const idx = list.findIndex((s) => (s.addOnId || s._id) === addOn._id);
      if (idx >= 0) list.splice(idx, 1);
      else
        list.push({
          addOnId: addOn._id,
          name: addOn.name,
          price: addOn.price,
          quantity: 1,
        });
      target.addOns = list;
      copy[itemIndex] = target;
      return copy;
    });
  };

  const changeItemAddOnQty = (
    itemIndex: number,
    addOnId: string,
    qty: number
  ) => {
    setUpdatedItems((prev) => {
      const copy = [...prev];
      const target = { ...copy[itemIndex] } as OrderItem & {
        addOns?: AddOnSel[];
      };
      const list: AddOnSel[] = (
        (target.addOns as AddOnSel[] | undefined) || []
      ).map((ao) =>
        (ao.addOnId || ao._id) === addOnId
          ? { ...ao, quantity: Math.max(1, qty) }
          : ao
      );
      target.addOns = list;
      copy[itemIndex] = target;
      return copy;
    });
  };
  const handleUpdateOrder = async () => {
    if (updatedItems.length === 0) {
      toast.error("Please add at least one item to the order");
      return;
    }
revalidateCoupon(appliedCoupon?.code || "", calculateSubtotal());
    setIsLoading(true);
    try {
      const itemsToUpdate = updatedItems
        .filter((item) => item.dishId?._id) // Only include items with valid dishId
        .map((item) => ({
          dishId: item.dishId!._id,
          quantity: item.quantity,
          price: item.price,
          dishName: item.dishName,
          addOns: (item.addOns || []) as AddOnSel[],
        }));

      // Pass only offer IDs for recalculation, not old discount amounts
      const offerIdsForRecalculation = appliedOffers.map((offer) => ({
        offerId: offer.offerId,
        offerName: offer.offerName,
        offerType: offer.offerType,
        // Don't pass old discount amount - let backend recalculate
      }));

      // Handle coupon removal explicitly
      // If appliedCoupon is null but order originally had a coupon, send empty string to remove it
      let couponCodeToSend;
      if (appliedCoupon) {
        couponCodeToSend = appliedCoupon.code;
      } else if (order.couponCode) {
        // User removed the coupon, send empty string to signal removal
        couponCodeToSend = "";
      } else {
        // No coupon originally, send undefined
        couponCodeToSend = undefined;
      }

      const response = await updateOrderItems(
        order._id,
        itemsToUpdate,
        couponCodeToSend,
        undefined, // Let backend recalculate the discount
        offerIdsForRecalculation // Pass offer IDs for fresh calculation
      );

      if (response.success) {
        // Update local state with recalculated offers and coupons from backend
        if (response.data?.appliedOffers) {
          setAppliedOffers(response.data.appliedOffers);
        }
        if (response.data?.couponCode && response.data?.couponDiscount) {
          setAppliedCoupon({
            code: response.data.couponCode,
            discount: response.data.couponDiscount,
            discountType: response.data.discountType ||"fixed",
            discountValue: response.data.couponDiscount,
          });
        } else if (!response.data?.couponCode) {
          setAppliedCoupon(null);
        }

        toast.success("Order updated successfully!");
        onSuccess();
        onClose();
      } else {
        toast.error("Failed to update order");
      }
    } catch (error) {
      console.error("Error updating order:", error);
      toast.error("Failed to update order");
    } finally {
      setIsLoading(false);
    }
  };

  const availableDishes = dishes.filter(
    (dish) => !updatedItems.some((item) => item.dishId?._id === dish._id)
  );

  const handleOpenChange = (open: boolean) => {
    if (!open) {
      onClose();
    }
  };

  return (
  <Dialog open={isOpen} onOpenChange={handleOpenChange}>
      <DialogContent className="max-w-5xl max-h-[95vh] overflow-hidden flex flex-col w-[95vw] sm:w-full">
        <DialogHeader className="pb-4 border-b border-gray-100">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-blue-50 rounded-lg">
              <Edit3 className="h-5 w-5 text-blue-600" />
            </div>
            <div>
              <DialogTitle className="text-xl font-semibold">
                Update Order #{order._id.slice(-6)}
              </DialogTitle>
              <DialogDescription className="text-gray-500 mt-1">
                Add or remove items from your order. Changes can only be made before payment.
              </DialogDescription>
            </div>
          </div>
        </DialogHeader>

        <div className="flex-1 overflow-y-auto">
            
            {/* Left Column - Add More Items */}
            <div className="xl:col-span-1">
              <div className="bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl border border-green-100 overflow-hidden">
                <button
                  onClick={() => setIsAddDishesExpanded(!isAddDishesExpanded)}
                  className="w-full flex items-center justify-between p-5 text-left hover:bg-green-100 transition-colors"
                >
                  <div className="flex items-center gap-3">
                    <Plus className="h-5 w-5 text-green-600" />
                    <h3 className="text-lg font-semibold text-green-900">Add More Items</h3>
                  </div>
                  {isAddDishesExpanded ? (
                    <ChevronUp className="h-5 w-5 text-green-600" />
                  ) : (
                    <ChevronDown className="h-5 w-5 text-green-600" />
                  )}
                </button>

                {isAddDishesExpanded && (
                  <div className="p-5 pt-0 border-t border-green-200">
                    <div className="space-y-3 max-h-80 overflow-y-auto">
                      {availableDishes.length > 0 ? (
                        availableDishes.map((dish) => (
                          <div
                            key={dish._id}
                            className="bg-white rounded-lg border border-green-200 p-3 hover:shadow-md transition-all hover:border-green-300"
                          >
                            <div className="flex justify-between items-start gap-3">
                              <div className="flex-1 min-w-0">
                                <div className="flex items-center gap-2 mb-1">
                                  <Utensils className="h-4 w-4 text-orange-500" />
                                  <h4 className="font-medium text-sm text-gray-900 truncate">
                                    {dish.name}
                                  </h4>
                                </div>
                                <p className="text-xs text-gray-500 line-clamp-2 mb-2">
                                  {dish.description}
                                </p>
                                <div className="flex items-center gap-2">
                                  <span className="bg-green-100 text-green-700 px-2 py-1 rounded-full text-xs font-medium">
                                    ₹{dish.price}
                                  </span>
                                </div>
                              </div>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => addDishToOrder(dish)}
                                disabled={isLoading}
                                className="shrink-0 hover:bg-green-50 hover:border-green-300 hover:text-green-700"
                              >
                                <Plus className="h-4 w-4" />
                              </Button>
                            </div>
                          </div>
                        ))
                      ) : (
                        <div className="text-center py-6">
                          <CheckCircle className="h-12 w-12 text-green-300 mx-auto mb-3" />
                          <p className="text-sm text-green-600">
                            All available dishes are already in your order
                          </p>
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Right Column - Current Order Items */}
            <div className="xl:col-span-2">
              <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-5 border border-blue-100">
                <div className="flex items-center gap-3 mb-4">
                  <ShoppingBag className="h-5 w-5 text-blue-600" />
                  <h3 className="text-lg font-semibold text-blue-900">Current Order</h3>
                  <span className="bg-blue-100 text-blue-700 px-2 py-1 rounded-full text-xs font-medium">
                    {updatedItems.length} items
                  </span>
                </div>
                
                <div className="space-y-3 max-h-96 overflow-y-auto">
                  {updatedItems.map((item, index) => (
                    <div key={index} className="bg-white rounded-lg border border-blue-200 p-4 hover:shadow-sm transition-shadow">
                      <div className="flex flex-col gap-4">
                        
                        {/* Item Header */}
                        <div className="flex justify-between items-start">
                          <div className="flex-1">
                            <div className="flex items-center gap-2 mb-1">
                              <ChefHat className="h-4 w-4 text-orange-500" />
                              <h4 className="font-semibold text-gray-900">
                                {item.dishId?.name || item.dishName || "Deleted Dish"}
                              </h4>
                              {item.isServed && (
                                <Badge variant="secondary" className="text-xs bg-green-100 text-green-700 border-green-200">
                                  Served
                                </Badge>
                              )}
                            </div>
                            <p className="text-sm text-gray-600 mb-2">
                              ₹{(
                                item.price +
                                ((item.addOns || []) as Array<{
                                  price: number;
                                  quantity?: number;
                                }>).reduce(
                                  (s, ao) =>
                                    s + (Number(ao.price) || 0) * (Number(ao.quantity) || 1),
                                  0
                                )
                              ).toFixed(2)} each
                            </p>
                          </div>
                          
                          {/* Quantity Controls */}
                          <div className="flex items-center gap-3">
                            <div className="flex items-center bg-gray-50 border rounded-lg">
                              <Button
                                variant="ghost"
                                size="sm"
                                className="h-9 w-9 hover:bg-red-50 hover:text-red-600"
                                onClick={() => updateItemQuantity(index, item.quantity - 1)}
                                disabled={
                                  isLoading ||
                                  item.quantity <= (item.servedQuantity || 0)
                                }
                                title={
                                  item.quantity <= (item.servedQuantity || 0)
                                    ? `Cannot reduce quantity below served amount (${
                                        item.servedQuantity || 0
                                      })`
                                    : "Decrease quantity"
                                }
                              >
                                <Minus className="h-4 w-4" />
                              </Button>
                              <span className="w-12 text-center font-semibold text-gray-900">
                                {item.quantity}
                              </span>
                              <Button
                                variant="ghost"
                                size="sm"
                                className="h-9 w-9 hover:bg-green-50 hover:text-green-600"
                                onClick={() => updateItemQuantity(index, item.quantity + 1)}
                                disabled={isLoading}
                              >
                                <Plus className="h-4 w-4" />
                              </Button>
                            </div>
                            <div className="text-right">
                              <div className="bg-blue-50 px-3 py-1 rounded-lg border border-blue-200">
                                <span className="font-bold text-blue-700">
                                  ₹{(
                                    item.price * item.quantity +
                                    ((item.addOns || []) as Array<{
                                      price: number;
                                      quantity?: number;
                                    }>).reduce(
                                      (s, ao) =>
                                        s + (Number(ao.price) || 0) * (Number(ao.quantity) || 1),
                                      0
                                    )
                                  ).toFixed(2)}
                                </span>
                              </div>
                            </div>
                          </div>
                        </div>

                        {/* Add-ons Section */}
                        {!!item.dishId?.enabledAddOns?.length && (
                          <div className="border-t border-gray-100 pt-3">
                            <div className="flex items-center gap-2 mb-3">
                              <Plus className="h-4 w-4 text-gray-500" />
                              <span className="text-sm font-semibold text-gray-700">Add-ons</span>
                              <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full">
                                Click to modify
                              </span>
                            </div>
                            <div className="grid gap-2">
                              {item.dishId.enabledAddOns.map(
                                (ao: {
                                  _id: string;
                                  name: string;
                                  price: number;
                                }) => {
                                  const isSelected = (item.addOns || []).some(
                                    (s) => (s.addOnId || s._id) === ao._id
                                  );
                                  const qty = (item.addOns || []).find(
                                    (s: AddOnSel) => (s.addOnId || s._id) === ao._id
                                  )?.quantity || 1;
                                  
                                  return (
                                    <div
                                      key={ao._id}
                                      className={`flex items-center justify-between p-3 border rounded-lg transition-all ${
                                        isSelected 
                                          ? 'bg-green-50 border-green-200' 
                                          : 'bg-gray-50 border-gray-200 hover:border-gray-300'
                                      }`}
                                    >
                                      <div className="flex items-center gap-3">
                                        <div className={`p-1 rounded ${isSelected ? 'bg-green-100' : 'bg-gray-200'}`}>
                                          <Utensils className={`h-3 w-3 ${isSelected ? 'text-green-600' : 'text-gray-500'}`} />
                                        </div>
                                        <div>
                                          <div className="text-sm font-medium text-gray-900">
                                            {ao.name}
                                          </div>
                                          <div className="text-xs text-gray-500">
                                            ₹{ao.price}
                                          </div>
                                        </div>
                                      </div>
                                      
                                      <div className="flex items-center gap-2">
                                        {isSelected && (
                                          <div className="flex items-center bg-white border rounded-md">
                                            <Button
                                              variant="ghost"
                                              size="sm"
                                              className="h-7 w-7"
                                              onClick={() =>
                                                changeItemAddOnQty(
                                                  index,
                                                  ao._id,
                                                  Math.max(1, qty - 1)
                                                )
                                              }
                                            >
                                              <Minus className="h-3 w-3" />
                                            </Button>
                                            <span className="w-6 text-center text-xs font-medium">
                                              {qty}
                                            </span>
                                            <Button
                                              variant="ghost"
                                              size="sm"
                                              className="h-7 w-7"
                                              onClick={() =>
                                                changeItemAddOnQty(
                                                  index,
                                                  ao._id,
                                                  qty + 1
                                                )
                                              }
                                            >
                                              <Plus className="h-3 w-3" />
                                            </Button>
                                          </div>
                                        )}
                                        <Button
                                          size="sm"
                                          variant={isSelected ? "secondary" : "outline"}
                                          className={`text-xs px-3 ${
                                            isSelected 
                                              ? 'bg-green-100 text-green-700 hover:bg-green-200' 
                                              : 'hover:bg-green-50 hover:border-green-200 hover:text-green-700'
                                          }`}
                                          onClick={() => toggleItemAddOn(index, ao)}
                                        >
                                          {isSelected ? "Remove" : "Add"}
                                        </Button>
                                      </div>
                                    </div>
                                  );
                                }
                              )}
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>


          {/* Bottom Section - Coupons, Offers, Summary */}
          <div className="mt-6 space-y-4">
            
            {/* Coupon Section */}
            <div className="bg-gradient-to-r from-orange-50 to-amber-50 rounded-xl p-5 border border-orange-100">
              <div className="flex items-center gap-3 mb-4">
                <Tag className="h-5 w-5 text-orange-600" />
                <h4 className="font-semibold text-orange-900">
                  {!appliedCoupon ? "Apply Coupon" : "Applied Coupon"}
                </h4>
              </div>

              {!appliedCoupon ? (
                <div className="flex flex-col sm:flex-row gap-3">
                  <div className="flex-1">
                    <Input
                      id="coupon"
                      value={couponCode}
                      onChange={(e) => setCouponCode(e.target.value.toUpperCase())}
                      placeholder="Enter coupon code"
                      disabled={isLoading || couponLoading}
                      className="bg-white border-orange-200 focus:border-orange-400 focus:ring-orange-100"
                    />
                  </div>
                  <Button
                    type="button"
                    variant="outline"
                    onClick={handleApplyCoupon}
                    disabled={isLoading || couponLoading || !couponCode.trim()}
                    className="border-orange-200 text-orange-600 hover:bg-orange-100 hover:border-orange-300"
                  >
                    {couponLoading ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : (
                      <>
                        <Tag className="h-4 w-4 mr-2" />
                        Apply Coupon
                      </>
                    )}
                  </Button>
                </div>
              ) : (
                <div className="bg-white rounded-lg border border-green-200 p-4">
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-green-100 rounded-lg">
                      <CheckCircle className="h-4 w-4 text-green-600" />
                    </div>
                    <div className="flex-1">
                      <div className="font-semibold text-green-800">
                        {appliedCoupon.code}
                      </div>
                      <div className="text-sm text-green-600">
                        ₹{estimateCouponDiscount().toFixed(2)} discount applied
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Applied Offers */}
            {appliedOffers.length > 0 && (
              <div className="bg-gradient-to-r from-purple-50 to-indigo-50 rounded-xl p-5 border border-purple-100">
                <div className="flex items-center gap-3 mb-4">
                  <Gift className="h-5 w-5 text-purple-600" />
                  <h4 className="font-semibold text-purple-900">Applied Offers</h4>
                  <span className="bg-purple-100 text-purple-700 px-2 py-1 rounded-full text-xs font-medium">
                    {appliedOffers.length}
                  </span>
                </div>
                <div className="grid gap-3">
                  {appliedOffers.map((offer, index) => (
                    <div
                      key={index}
                      className="bg-white rounded-lg border border-purple-200 p-3"
                    >
                      <div className="flex items-center gap-3">
                        <div className="p-2 bg-purple-100 rounded-lg">
                          <Gift className="h-4 w-4 text-purple-600" />
                        </div>
                        <div className="flex-1">
                          <div className="font-semibold text-purple-800">
                            {offer.offerName}
                          </div>
                          <Badge variant="secondary" className="mt-1 text-xs bg-purple-100 text-purple-700">
                            {offer.offerType}
                          </Badge>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Order Summary */}
            <div className="bg-gradient-to-r from-slate-50 to-gray-50 rounded-xl p-5 border border-slate-200">
              <div className="flex items-center gap-3 mb-4">
                <Calculator className="h-5 w-5 text-slate-600" />
                <h4 className="font-semibold text-slate-900">Order Summary</h4>
              </div>
              
              <div className="bg-white rounded-lg border border-slate-200 p-4">
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600">Subtotal:</span>
                    <span className="font-medium">₹{calculateSubtotal().toFixed(2)}</span>
                  </div>
                  
                  {appliedOffers.length > 0 && (
                    <div className="flex justify-between items-center text-purple-600">
                      <span>Offer Discount:</span>
                      <span className="font-medium">-₹{calculateOfferDiscount().toFixed(2)}</span>
                    </div>
                  )}
                  
                  {appliedCoupon && (
                    <div className="flex justify-between items-center text-green-600">
                      <span>Coupon Discount ({appliedCoupon.code}):</span>
                      <span className="font-medium flex items-center gap-1">
                        -₹{estimateCouponDiscount().toFixed(2)}
                      </span>
                    </div>
                  )}
                  
                  <div className="border-t border-gray-200 pt-3">
                    <div className="flex justify-between items-center">
                      <span className="text-lg font-bold text-gray-900">Total:</span>
                      <span className="text-xl font-bold text-blue-600">₹{calculateTotal().toFixed(2)}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <DialogFooter className="pt-4 border-t border-gray-100 bg-gray-50 rounded-b-lg">
          <div className="flex flex-col sm:flex-row justify-between items-center gap-3 w-full">
            <div className="text-sm text-gray-500">
              {updatedItems.length > 0 && (
                <span>
                  {updatedItems.reduce((sum, item) => sum + item.quantity, 0)} items • 
                  ₹{calculateTotal().toFixed(2)}
                </span>
              )}
            </div>
            <div className="flex gap-3">
              <Button
                variant="outline"
                onClick={onClose}
                disabled={isLoading}
                className="px-6"
              >
                Cancel
              </Button>
              <Button
                onClick={handleUpdateOrder}
                disabled={isLoading || updatedItems.length === 0}
                className="bg-blue-600 hover:bg-blue-700 px-8"
              >
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Updating...
                  </>
                ) : (
                  <>
                    <ShoppingCart className="mr-2 h-4 w-4" />
                    Update Order
                  </>
                )}
              </Button>
            </div>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
