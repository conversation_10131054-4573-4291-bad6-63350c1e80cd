"use client";

import { useState } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  Di<PERSON>Footer,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Loader2, Plus, Minus } from "lucide-react";
import { toast } from "sonner";
import { updateOrderMiscellaneousAmount } from "@/server/admin";

interface MiscellaneousAmountDialogProps {
  isOpen: boolean;
  onClose: () => void;
  orderId: string;
  currentAmount?: number;
  currentReason?: string;
  onUpdate: () => void;
}

export default function MiscellaneousAmountDialog({
  isOpen,
  onClose,
  orderId,
  currentAmount = 0,
  currentReason = "",
  onUpdate,
}: MiscellaneousAmountDialogProps) {
  const [amount, setAmount] = useState(currentAmount.toString());
  const [reason, setReason] = useState(currentReason);
  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = async () => {
    try {
      setIsLoading(true);
      
      const numericAmount = parseFloat(amount) || 0;
      
      const response = await updateOrderMiscellaneousAmount(
        orderId,
        numericAmount,
        reason
      );

      if (response.success) {
        toast.success("Miscellaneous amount updated successfully");
        onUpdate();
        onClose();
      } else {
        toast.error(response.message || "Failed to update miscellaneous amount");
      }
    } catch (error) {
      console.error("Error updating miscellaneous amount:", error);
      toast.error("Failed to update miscellaneous amount");
    } finally {
      setIsLoading(false);
    }
  };

  const handleClose = () => {
    setAmount(currentAmount.toString());
    setReason(currentReason);
    onClose();
  };

  const addPresetAmount = (presetAmount: number) => {
    const currentNumeric = parseFloat(amount) || 0;
    setAmount((currentNumeric + presetAmount).toString());
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Adjust Miscellaneous Amount</DialogTitle>
          <DialogDescription>
            Add or subtract an amount from the order total. This adjustment is independent of offers and coupons.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Current Amount Display */}
          <div className="flex items-center gap-2">
            <Label>Current Adjustment:</Label>
            <Badge variant={currentAmount >= 0 ? "default" : "destructive"}>
              {currentAmount >= 0 ? "+" : ""}₹{currentAmount}
            </Badge>
          </div>

          {/* Amount Input */}
          <div className="space-y-2">
            <Label htmlFor="amount">
              Adjustment Amount (₹)
              <span className="text-sm text-gray-500 ml-2">
                (Positive to add, negative to subtract)
              </span>
            </Label>
            <Input
              id="amount"
              type="number"
              step="0.01"
              placeholder="0.00"
              value={amount}
              onChange={(e) => setAmount(e.target.value)}
            />
          </div>

          {/* Quick Preset Buttons */}
          <div className="space-y-2">
            <Label>Quick Adjustments:</Label>
            <div className="grid grid-cols-3 gap-2">
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() => addPresetAmount(10)}
                className="text-green-600"
              >
                <Plus className="h-3 w-3 mr-1" />
                ₹10
              </Button>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() => addPresetAmount(50)}
                className="text-green-600"
              >
                <Plus className="h-3 w-3 mr-1" />
                ₹50
              </Button>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() => addPresetAmount(100)}
                className="text-green-600"
              >
                <Plus className="h-3 w-3 mr-1" />
                ₹100
              </Button>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() => addPresetAmount(-10)}
                className="text-red-600"
              >
                <Minus className="h-3 w-3 mr-1" />
                ₹10
              </Button>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() => addPresetAmount(-50)}
                className="text-red-600"
              >
                <Minus className="h-3 w-3 mr-1" />
                ₹50
              </Button>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() => addPresetAmount(-100)}
                className="text-red-600"
              >
                <Minus className="h-3 w-3 mr-1" />
                ₹100
              </Button>
            </div>
          </div>

          {/* Reason Input */}
          <div className="space-y-2">
            <Label htmlFor="reason">Reason (Optional)</Label>
            <Textarea
              id="reason"
              placeholder="e.g., Service charge, Discount for complaint, Extra delivery fee..."
              value={reason}
              onChange={(e) => setReason(e.target.value)}
              rows={3}
            />
          </div>

          {/* Calculation Preview */}
          <div className="p-3 bg-gray-50 rounded-lg">
            <div className="text-sm text-gray-600">
              <div>Final calculation will be:</div>
              <div className="font-mono mt-1">
                ((Total - Offers) - Coupons) {parseFloat(amount) >= 0 ? "+" : ""} ₹{amount || "0"} = Final Amount
              </div>
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={handleClose} disabled={isLoading}>
            Cancel
          </Button>
          <Button onClick={handleSubmit} disabled={isLoading}>
            {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            Update Amount
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
