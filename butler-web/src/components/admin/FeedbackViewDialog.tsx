"use client";

import { useState, useEffect, useCallback } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  Di<PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";

import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

import { 
  Star, 
  MessageSquare, 
  User, 
  Calendar, 

  Reply,
  Loader2,
  AlertCircle,
  CheckCircle,
  Clock
} from "lucide-react";
import { toast } from "sonner";
import { getOutletFeedbackSummary, respondToFeedback } from "@/server/admin";

interface FeedbackViewDialogProps {
  isOpen: boolean;
  onClose: () => void;
  outletId?: string;
  mode: "outlet" | "order";
}

interface Feedback {
  _id: string;
  userId: {
    name: string;
    email: string;
  };
  orderId?: {
    orderNumber: string;
  };
  rating: number;
  comment: string;
  categories: string[];
  feedbackType: string;
  status: "pending" | "reviewed" | "responded";
  adminResponse?: {
    message: string;
    respondedBy: string;
    respondedAt: string;
  };
  createdAt: string;
}

interface FeedbackSummary {
  averageRating: number;
  totalFeedbacks: number;
  ratingDistribution: Record<number, number>;
}

const categoryLabels: Record<string, string> = {
  food_quality: "Food Quality",
  service: "Service",
  delivery_time: "Delivery Time",
  packaging: "Packaging",
  value_for_money: "Value for Money",
  overall_experience: "Overall Experience",
};

const feedbackTypeLabels: Record<string, string> = {
  order_experience: "Order Experience",
  general: "General",
  complaint: "Complaint",
  suggestion: "Suggestion",
};

export default function FeedbackViewDialog({
  isOpen,
  onClose,
  outletId,
  mode,
}: FeedbackViewDialogProps) {
  const [feedbacks, setFeedbacks] = useState<Feedback[]>([]);
  const [summary, setSummary] = useState<FeedbackSummary | null>(null);
  const [loading, setLoading] = useState(false);
  const [respondingTo, setRespondingTo] = useState<string | null>(null);
  const [responseMessage, setResponseMessage] = useState("");
  const [submittingResponse, setSubmittingResponse] = useState(false);

  const fetchFeedbackData = useCallback(async () => {
    if (!outletId) return;

    setLoading(true);
    try {
      const response = await getOutletFeedbackSummary(outletId, {
        page: 1,
        limit: 50,
      });
      if (response.success) {
        setFeedbacks(response.data.recentFeedbacks);
        setSummary(response.data.summary);
      } else {
        toast.error("Failed to load feedback data");
      }
    } catch (error) {
      console.error("Error fetching feedback:", error);
      toast.error("Failed to load feedback data");
    } finally {
      setLoading(false);
    }
  }, [outletId]);

  useEffect(() => {
    if (isOpen && outletId) {
      fetchFeedbackData();
    }
  }, [isOpen, outletId, fetchFeedbackData]);

  const handleRespondToFeedback = async (feedbackId: string) => {
    if (!responseMessage.trim()) {
      toast.error("Please enter a response message");
      return;
    }

    setSubmittingResponse(true);
    try {
      const response = await respondToFeedback(feedbackId, responseMessage);
      
      if (response.success) {
        toast.success("Response sent successfully");
        setRespondingTo(null);
        setResponseMessage("");
        fetchFeedbackData(); // Refresh data
      } else {
        toast.error(response.message || "Failed to send response");
      }
    } catch (error) {
      console.error("Error responding to feedback:", error);
      toast.error("Failed to send response");
    } finally {
      setSubmittingResponse(false);
    }
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`h-4 w-4 ${
          i < rating ? "text-yellow-400 fill-current" : "text-gray-300"
        }`}
      />
    ));
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "pending":
        return <Clock className="h-4 w-4 text-yellow-500" />;
      case "reviewed":
        return <AlertCircle className="h-4 w-4 text-blue-500" />;
      case "responded":
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      default:
        return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "pending":
        return "bg-yellow-100 text-yellow-800";
      case "reviewed":
        return "bg-blue-100 text-blue-800";
      case "responded":
        return "bg-green-100 text-green-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[900px] max-h-[90vh] overflow-hidden flex flex-col">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <MessageSquare className="h-5 w-5" />
            {mode === "outlet" ? "Outlet Feedback" : "Order Feedback"}
          </DialogTitle>
          <DialogDescription>
            {mode === "outlet" 
              ? "View and manage customer feedback for this outlet"
              : "View feedback for this specific order"
            }
          </DialogDescription>
        </DialogHeader>

        <div className="flex-1 overflow-y-auto space-y-6">
          {loading ? (
            <div className="flex justify-center items-center h-32">
              <Loader2 className="h-6 w-6 animate-spin" />
            </div>
          ) : (
            <>
              {/* Summary Section */}
              {summary && mode === "outlet" && (
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm font-medium">Average Rating</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="flex items-center gap-2">
                        <span className="text-2xl font-bold">
                          {summary.averageRating.toFixed(1)}
                        </span>
                        <div className="flex">
                          {renderStars(Math.round(summary.averageRating))}
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm font-medium">Total Feedback</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold">{summary.totalFeedbacks}</div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm font-medium">Rating Distribution</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-1">
                        {[5, 4, 3, 2, 1].map((rating) => (
                          <div key={rating} className="flex items-center gap-2 text-sm">
                            <span className="w-3">{rating}</span>
                            <Star className="h-3 w-3 text-yellow-400 fill-current" />
                            <div className="flex-1 bg-gray-200 rounded-full h-2">
                              <div
                                className="bg-yellow-400 h-2 rounded-full"
                                style={{
                                  width: `${
                                    summary.totalFeedbacks > 0
                                      ? ((summary.ratingDistribution[rating] || 0) / summary.totalFeedbacks) * 100
                                      : 0
                                  }%`,
                                }}
                              />
                            </div>
                            <span className="w-8 text-right">
                              {summary.ratingDistribution[rating] || 0}
                            </span>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                </div>
              )}

              {/* Feedback List */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Recent Feedback</h3>
                {feedbacks.length === 0 ? (
                  <div className="text-center py-8 text-gray-500">
                    No feedback available
                  </div>
                ) : (
                  feedbacks.map((feedback) => (
                    <Card key={feedback._id} className="border-l-4 border-l-blue-500">
                      <CardHeader className="pb-3">
                        <div className="flex items-start justify-between">
                          <div className="space-y-1">
                            <div className="flex items-center gap-2">
                              <User className="h-4 w-4 text-gray-500" />
                              <span className="font-medium">{feedback.userId.name}</span>
                              <Badge className={getStatusColor(feedback.status)}>
                                {getStatusIcon(feedback.status)}
                                <span className="ml-1 capitalize">{feedback.status}</span>
                              </Badge>
                            </div>
                            <div className="flex items-center gap-4 text-sm text-gray-500">
                              <div className="flex items-center gap-1">
                                <Calendar className="h-3 w-3" />
                                {new Date(feedback.createdAt).toLocaleDateString()}
                              </div>
                              {feedback.orderId && (
                                <span>Order: {feedback.orderId.orderNumber}</span>
                              )}
                            </div>
                          </div>
                          <div className="flex items-center gap-1">
                            {renderStars(feedback.rating)}
                          </div>
                        </div>
                      </CardHeader>
                      <CardContent className="space-y-3">
                        {feedback.comment && (
                          <p className="text-gray-700">{feedback.comment}</p>
                        )}
                        
                        {feedback.categories.length > 0 && (
                          <div className="flex flex-wrap gap-1">
                            {feedback.categories.map((category) => (
                              <Badge key={category} variant="outline" className="text-xs">
                                {categoryLabels[category] || category}
                              </Badge>
                            ))}
                          </div>
                        )}

                        <Badge variant="secondary" className="text-xs">
                          {feedbackTypeLabels[feedback.feedbackType] || feedback.feedbackType}
                        </Badge>

                        {feedback.adminResponse && (
                          <div className="mt-3 p-3 bg-blue-50 rounded-lg border border-blue-200">
                            <div className="flex items-center gap-2 mb-2">
                              <Reply className="h-4 w-4 text-blue-600" />
                              <span className="text-sm font-medium text-blue-800">Admin Response</span>
                            </div>
                            <p className="text-sm text-blue-700">{feedback.adminResponse.message}</p>
                            <p className="text-xs text-blue-600 mt-1">
                              Responded on {new Date(feedback.adminResponse.respondedAt).toLocaleDateString()}
                            </p>
                          </div>
                        )}

                        {!feedback.adminResponse && feedback.status !== "responded" && (
                          <div className="mt-3">
                            {respondingTo === feedback._id ? (
                              <div className="space-y-3">
                                <Textarea
                                  placeholder="Enter your response..."
                                  value={responseMessage}
                                  onChange={(e) => setResponseMessage(e.target.value)}
                                  rows={3}
                                />
                                <div className="flex gap-2">
                                  <Button
                                    size="sm"
                                    onClick={() => handleRespondToFeedback(feedback._id)}
                                    disabled={submittingResponse}
                                  >
                                    {submittingResponse && (
                                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                    )}
                                    Send Response
                                  </Button>
                                  <Button
                                    size="sm"
                                    variant="outline"
                                    onClick={() => {
                                      setRespondingTo(null);
                                      setResponseMessage("");
                                    }}
                                  >
                                    Cancel
                                  </Button>
                                </div>
                              </div>
                            ) : (
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => setRespondingTo(feedback._id)}
                              >
                                <Reply className="mr-2 h-4 w-4" />
                                Respond
                              </Button>
                            )}
                          </div>
                        )}
                      </CardContent>
                    </Card>
                  ))
                )}
              </div>
            </>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
