"use client";

import { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  Di<PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { toast } from "sonner";
import {
  Loader2,
  Plus,
  Minus,
  ShoppingCart,
  Edit3,
  Tag,
  X,
  ShoppingBag,
  Utensils,
  ChefHat,
  CheckCircle,
  Calculator,
} from "lucide-react";
import {
  updateAdminOrderItems,
  getAllDishes,
  validateCouponForOrder,
} from "@/server/admin";

interface OrderItem {
  dishId?: {
    _id: string;
    name: string;
    price: number;
    enabledAddOns?: Array<{
      _id: string;
      name: string;
      price: number;
      type?: string;
    }>;
  } | null;
  dishName?: string; // Stored dish name for deleted dishes
  quantity: number;
  price: number;
  addOns?: Array<{
    addOnId?: string;
    _id?: string;
    name: string;
    price: number;
    quantity?: number;
  }>;
  isServed?: boolean;
  servedQuantity?: number;
}

interface Order {
  _id: string;
  items: OrderItem[];
  totalAmount: number;
  finalAmount: number;
  paymentStatus: string;
  status: string;
  couponCode?: string;
  couponDiscount?: number;
  discountValue?: number;
  discountType?: string;
  appliedOffers?: Array<{
    offerId: string;
    offerName: string;
    offerType: string;
    discount: number;
    discountType: string;
    freeItems?: Array<{
      dishId: string;
      dishName: string;
      quantity: number;
      price: number;
    }>;
  }>;
  offerDiscount?: number;
  outletId: {
    _id: string;
    name: string;
  };
}

interface Dish {
  _id: string;
  name: string;
  price: number;
  category: string;
  description?: string;
  image?: string;
  isAvailable: boolean;
  enabledAddOns?: Array<{
    _id: string;
    name: string;
    price: number;
    type?: string;
  }>;
}

interface AdminOrderUpdateDialogProps {
  order: Order;
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

type AddOnSel = {
  addOnId?: string;
  _id?: string;
  name: string;
  price: number;
  quantity?: number;
};

export default function AdminOrderUpdateDialog({
  order,
  isOpen,
  onClose,
  onSuccess,
}: AdminOrderUpdateDialogProps) {
  const [updatedItems, setUpdatedItems] = useState<OrderItem[]>([]);
  const [dishes, setDishes] = useState<Dish[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [couponCode, setCouponCode] = useState("");
  const [couponLoading, setCouponLoading] = useState(false);
  const [isAddDishesExpanded, setIsAddDishesExpanded] = useState(false);
  const [appliedCoupon, setAppliedCoupon] = useState<{
    code: string;
    discount: number;
    discountType: string;
    discountValue?: number;
  } | null>(null);
  const [appliedOffers, setAppliedOffers] = useState<
    Array<{
      offerId: string;
      offerName: string;
      offerType: string;
      discount: number;
      discountType: string;
      freeItems?: Array<{
        dishId: string;
        dishName: string;
        quantity: number;
        price: number;
      }>;
    }>
  >([]);

  useEffect(() => {
    if (isOpen && order) {
      // Initialize with current order items
      setUpdatedItems([...order.items]);

      // Set existing coupon if any
      if (order.couponCode && order.couponDiscount) {
        console.log(order, "discountType one");
        setAppliedCoupon({
          code: order.couponCode,
          discount: order.couponDiscount,
          discountType: order.discountType || "fixed", // Default, will be updated if needed
          discountValue: order.discountValue,
        });
      }

      // Initialize applied offers from order
      setAppliedOffers(order.appliedOffers || []);

      fetchDishes();
    }
  }, [isOpen, order]);

  const fetchDishes = async () => {
    try {
      const response = await getAllDishes();
      if (response.success) {
        setDishes(response.data.filter((dish: Dish) => dish.isAvailable));
      }
    } catch (error) {
      console.error("Error fetching dishes:", error);
      toast.error("Failed to load dishes");
    }
  };

  const addDishToOrder = (dish: Dish) => {
    const existingItemIndex = updatedItems.findIndex(
      (item) => item.dishId?._id === dish._id
    );

    if (existingItemIndex >= 0) {
      const newItems = [...updatedItems];
      newItems[existingItemIndex].quantity += 1;
      setUpdatedItems(newItems);
    } else {
      const newItem: OrderItem = {
        dishId: {
          _id: dish._id,
          name: dish.name,
          price: dish.price,
        },
        dishName: dish.name,
        quantity: 1,
        price: dish.price,
        isServed: false,
      };
      setUpdatedItems([...updatedItems, newItem]);
    }
  };

  const updateItemQuantity = (itemIndex: number, newQuantity: number) => {
    const item = updatedItems[itemIndex];
    const servedQuantity = item.servedQuantity || 0;

    // If dish has served quantities, don't allow reducing quantity below served amount
    if (newQuantity < servedQuantity) {
      toast.error(
        `Cannot reduce quantity below served amount (${servedQuantity}). Current served: ${servedQuantity}, minimum allowed: ${servedQuantity}`
      );
      return;
    }

    if (newQuantity <= 0) {
      // Don't allow removing dishes with served quantities
      if (servedQuantity > 0) {
        toast.error(
          `Cannot remove dishes that have been served. This dish has ${servedQuantity} served items.`
        );
        return;
      }
      const newItems = updatedItems.filter((_, index) => index !== itemIndex);
      setUpdatedItems(newItems);
    } else {
      const newItems = [...updatedItems];
      newItems[itemIndex].quantity = newQuantity;
      setUpdatedItems(newItems);
    }
  };

  const handleApplyCoupon = async () => {
    if (!couponCode.trim()) {
      toast.error("Please enter a coupon code");
      return;
    }

    const subtotal = calculateSubtotal();

    setCouponLoading(true);
    try {
      const response = await validateCouponForOrder({
        code: couponCode,
        outletId: order.outletId._id,
        amount: subtotal,
      });

      if (response.success) {
        setAppliedCoupon({
          code: couponCode.toUpperCase(),
          discount: response.data.discount,
          discountType: response.data.coupon.discountType,
          discountValue: response.data.coupon.discountValue,
        });
        toast.success("Coupon applied successfully!");
        setCouponCode("");
      } else {
        toast.error(response.message || "Failed to apply coupon");
      }
    } catch (error) {
      console.error("Error applying coupon:", error);
      toast.error("Failed to apply coupon");
    } finally {
      setCouponLoading(false);
    }
  };

  const removeCoupon = () => {
    setAppliedCoupon(null);
    toast.success("Coupon removed");
  };

  const calculateSubtotal = () => {
    return updatedItems.reduce((sum, item) => {
      const addOns = (item.addOns || []) as AddOnSel[];
      const addOnsPerUnit = addOns.reduce(
        (s, ao) => s + (Number(ao.price) || 0) * (Number(ao.quantity) || 1),
        0
      );
      return sum + item.price * item.quantity + addOnsPerUnit;
    }, 0);
  };

  // Estimate offer discount based on current subtotal (for real-time updates)
  const estimateOfferDiscount = () => {
    const subtotal = calculateSubtotal();
    let estimatedDiscount = 0;

    appliedOffers.forEach((offer) => {
      if (offer.discountType === "percentage") {
        const originalOfferPercentage =
          (offer.discount / order.totalAmount) * 100;
        estimatedDiscount += (subtotal * originalOfferPercentage) / 100;
      } else {
        // For fixed offers, use the original amount (may not be accurate but better than nothing)
        estimatedDiscount += offer.discount;
      }
    });

    return estimatedDiscount;
  };

  // Estimate coupon discount based on current subtotal (for real-time updates)
  const estimateCouponDiscount = () => {
    if (!appliedCoupon) return 0;

    const subtotal = calculateSubtotal();
    const offerDiscount = estimateOfferDiscount();
    const amountAfterOffers = Math.max(0, subtotal - offerDiscount);
    console.log(amountAfterOffers, appliedCoupon.discountType,'value');
    if (appliedCoupon.discountType === "percentage") {
      return amountAfterOffers * ((appliedCoupon.discountValue || 1) / 100);
    } else {
      // For fixed coupons, use the original amount
      return appliedCoupon.discount;
    }
  };

  const calculateTotal = () => {
    const subtotal = calculateSubtotal();
    const offerDiscount = estimateOfferDiscount();
    const couponDiscount = estimateCouponDiscount();
    // Apply offers first, then coupons (matching backend logic)
    console.log(offerDiscount, couponDiscount, "offerDiscount");
    const amountAfterOffers = Math.max(0, subtotal - offerDiscount);
    return Math.max(0, amountAfterOffers - couponDiscount);
  };

  const handleUpdateOrder = async () => {
    if (updatedItems.length === 0) {
      toast.error("Please add at least one item to the order");
      return;
    }
    setIsLoading(true);
    try {
      const itemsToUpdate = updatedItems
        .filter((item) => item.dishId?._id) // Only include items with valid dishId
        .map((item) => ({
          dishId: item.dishId!._id,
          quantity: item.quantity,
          price: item.price,
          dishName: item.dishName,
          addOns: (item.addOns || []) as AddOnSel[],
        }));

      // Handle coupon removal explicitly
      // If appliedCoupon is null but order originally had a coupon, send empty string to remove it
      let couponCodeToSend;
      if (appliedCoupon) {
        couponCodeToSend = appliedCoupon.code;
      } else if (order.couponCode) {
        // Admin removed the coupon, send empty string to signal removal
        couponCodeToSend = "";
      } else {
        // No coupon originally, send undefined
        couponCodeToSend = undefined;
      }

      const response = await updateAdminOrderItems(
        order._id,
        itemsToUpdate,
        couponCodeToSend,
        undefined // Let backend recalculate the discount
      );

      if (response.success) {
        toast.success("Order updated successfully!");
        onSuccess();
        onClose();
      } else {
        toast.error("Failed to update order");
      }
    } catch (error) {
      console.error("Error updating order:", error);
      toast.error("Failed to update order");
    } finally {
      setIsLoading(false);
    }
  };

  const availableDishes = dishes.filter(
    (dish) => !updatedItems.some((item) => item.dishId?._id === dish._id)
  );

  const handleOpenChange = (open: boolean) => {
    if (!open) {
      onClose();
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleOpenChange}>
      <DialogContent className="max-w-6xl max-h-[95vh] overflow-hidden flex flex-col w-[95vw] sm:w-full">
        <DialogHeader className="pb-4 border-b border-gray-100">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-blue-50 rounded-lg">
              <Edit3 className="h-5 w-5 text-blue-600" />
            </div>
            <div>
              <DialogTitle className="text-xl font-semibold">
                Update Order #{order._id.slice(-6)}
              </DialogTitle>
              <DialogDescription className="text-gray-500 mt-1">
                Add or remove items from this order. Changes can only be made
                before payment is completed.
              </DialogDescription>
            </div>
          </div>
        </DialogHeader>

        <div className="flex-1 overflow-y-auto">
          <div className="xl:col-span-1">
            <div className="bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl border border-green-100 h-full">
              <div className="p-5 border-b border-green-200">
                <div className="flex items-center gap-3 mb-2">
                  <Plus className="h-5 w-5 text-green-600 " />
                  <h3 className="text-lg font-semibold text-green-900">
                    Add More Items
                  </h3>
                  <span className="bg-green-100 text-green-700 px-2 py-1 rounded-full text-xs font-medium">
                    {availableDishes.length} available
                  </span>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setIsAddDishesExpanded(!isAddDishesExpanded)}
                    className="hover:bg-green-50 hover:border-green-300 hover:text-green-700"
                  >
                    {isAddDishesExpanded ? "Hide" : "Show"}
                  </Button>
                </div>
              </div>
              {isAddDishesExpanded && (
                <div className="p-5">
                  <div className="space-y-3 max-h-96 overflow-y-auto">
                    {availableDishes.length > 0 ? (
                      availableDishes.map((dish) => (
                        <div
                          key={dish._id}
                          className="bg-white rounded-lg border border-green-200 p-4 hover:shadow-md transition-all hover:border-green-300 group"
                        >
                          <div className="flex justify-between items-start gap-3">
                            <div className="flex-1 min-w-0">
                              <div className="flex items-center gap-2 mb-2">
                                <div className="p-1 bg-orange-50 rounded">
                                  <ChefHat className="h-3 w-3 text-orange-500" />
                                </div>
                                <h4 className="font-semibold text-gray-900 truncate">
                                  {dish.name}
                                </h4>
                              </div>
                              <p className="text-xs text-gray-500 line-clamp-2 mb-3">
                                {dish.description}
                              </p>
                              <div className="bg-green-100 text-green-700 px-2 py-1 rounded-full text-xs font-semibold inline-block">
                                ₹{dish.price}
                              </div>
                            </div>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => addDishToOrder(dish)}
                              disabled={isLoading}
                              className="shrink-0 hover:bg-green-50 hover:border-green-300 hover:text-green-700 group-hover:shadow-sm"
                            >
                              <Plus className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                      ))
                    ) : (
                      <div className="text-center py-8">
                        <CheckCircle className="h-12 w-12 text-green-300 mx-auto mb-3" />
                        <p className="text-sm text-green-600 font-medium">
                          All dishes added to order
                        </p>
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          </div>
          <div className="xl:col-span-2">
            <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl border border-blue-100">
              <div className="p-5 border-b border-blue-200">
                <div className="flex items-center gap-3">
                  <ShoppingBag className="h-5 w-5 text-blue-600" />
                  <h3 className="text-lg font-semibold text-blue-900">
                    Current Order Items
                  </h3>
                  <span className="bg-blue-100 text-blue-700 px-2 py-1 rounded-full text-xs font-medium">
                    {updatedItems.length} items
                  </span>
                </div>
              </div>

              <div className="p-5">
                <div className="space-y-4 max-h-[500px] overflow-y-auto">
                  {updatedItems.map((item, index) => (
                    <div
                      key={index}
                      className="bg-white rounded-lg border border-blue-200 p-4 hover:shadow-sm transition-shadow"
                    >
                      {/* Item Header */}
                      <div className="flex justify-between items-start mb-4">
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-2">
                            <div className="p-1 bg-orange-50 rounded">
                              <Utensils className="h-4 w-4 text-orange-500" />
                            </div>
                            <h4 className="font-semibold text-gray-900">
                              {item.dishId?.name ||
                                item.dishName ||
                                "Deleted Dish"}
                            </h4>
                            {item.isServed && (
                              <Badge
                                variant="secondary"
                                className="bg-green-100 text-green-700 border-green-200"
                              >
                                Served ({item.servedQuantity || 0})
                              </Badge>
                            )}
                          </div>

                          <div className="text-sm text-gray-600 mb-2">
                            Base price: ₹{item.price} each
                          </div>

                          {/* Add-ons Display */}
                          {item.addOns && item.addOns.length > 0 && (
                            <div className="bg-gray-50 rounded-lg p-3 mb-3">
                              <div className="flex items-center gap-1 mb-2">
                                <Plus className="h-3 w-3 text-gray-500" />
                                <span className="text-xs font-semibold text-gray-700">
                                  Current Add-ons:
                                </span>
                              </div>
                              <div className="space-y-1">
                                {item.addOns.map((addOn, addOnIndex) => (
                                  <div
                                    key={addOnIndex}
                                    className="text-xs text-gray-600 flex justify-between"
                                  >
                                    <span>
                                      • {addOn.name} x{addOn.quantity || 1}
                                    </span>
                                    <span className="font-medium">
                                      ₹
                                      {(
                                        (addOn.price || 0) *
                                        (addOn.quantity || 1)
                                      ).toFixed(2)}
                                    </span>
                                  </div>
                                ))}
                              </div>
                            </div>
                          )}
                        </div>

                        {/* Quantity Controls & Total */}
                        <div className="flex flex-col items-end gap-3">
                          <div className="flex items-center bg-gray-50 border rounded-lg">
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-9 w-9 hover:bg-red-50 hover:text-red-600"
                              onClick={() =>
                                updateItemQuantity(index, item.quantity - 1)
                              }
                              disabled={
                                isLoading ||
                                item.quantity <= (item.servedQuantity || 0)
                              }
                              title={
                                item.quantity <= (item.servedQuantity || 0)
                                  ? `Cannot reduce below served amount (${
                                      item.servedQuantity || 0
                                    })`
                                  : "Decrease quantity"
                              }
                            >
                              <Minus className="h-4 w-4" />
                            </Button>
                            <span className="w-12 text-center font-semibold text-gray-900">
                              {item.quantity}
                            </span>
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-9 w-9 hover:bg-green-50 hover:text-green-600"
                              onClick={() =>
                                updateItemQuantity(index, item.quantity + 1)
                              }
                              disabled={isLoading}
                            >
                              <Plus className="h-4 w-4" />
                            </Button>
                          </div>

                          {/* Item Total */}
                          <div className="bg-blue-50 px-3 py-2 rounded-lg border border-blue-200">
                            {(() => {
                              const dishTotal = item.price * item.quantity;
                              const addOnsTotal = (item.addOns || []).reduce(
                                (sum, addOn) =>
                                  sum +
                                  (addOn.price || 0) * (addOn.quantity || 1),
                                0
                              );
                              const itemTotal = dishTotal + addOnsTotal;

                              return (
                                <div className="text-right">
                                  <div className="text-xs text-blue-600 mb-1">
                                    Dish: ₹{dishTotal.toFixed(2)}
                                    {addOnsTotal > 0 && (
                                      <span> + ₹{addOnsTotal.toFixed(2)}</span>
                                    )}
                                  </div>
                                  <div className="font-bold text-blue-700">
                                    ₹{itemTotal.toFixed(2)}
                                  </div>
                                </div>
                              );
                            })()}
                          </div>
                        </div>
                      </div>

                      {!!item.dishId?.enabledAddOns?.length && (
                        <div className="border-t border-gray-100 pt-4">
                          <div className="flex items-center gap-2 mb-3">
                            <Plus className="h-4 w-4 text-gray-500" />
                            <span className="text-sm font-semibold text-gray-700">
                              Manage Add-ons
                            </span>
                          </div>
                          <div className="grid gap-2">
                            {item.dishId.enabledAddOns.map(
                              (ao: {
                                _id: string;
                                name: string;
                                price: number;
                              }) => {
                                const selectedAddOn = (item.addOns || []).find(
                                  (s) => (s.addOnId || s._id) === ao._id
                                );
                                const isSelected = !!selectedAddOn;
                                const quantity = selectedAddOn?.quantity || 1;

                                return (
                                  <div
                                    key={ao._id}
                                    className={`flex items-center justify-between p-3 border rounded-lg transition-all ${
                                      isSelected
                                        ? "bg-green-50 border-green-200"
                                        : "bg-gray-50 border-gray-200 hover:border-gray-300"
                                    }`}
                                  >
                                    <div className="flex items-center gap-3">
                                      <div
                                        className={`p-1 rounded ${
                                          isSelected
                                            ? "bg-green-100"
                                            : "bg-gray-200"
                                        }`}
                                      >
                                        <Utensils
                                          className={`h-3 w-3 ${
                                            isSelected
                                              ? "text-green-600"
                                              : "text-gray-500"
                                          }`}
                                        />
                                      </div>
                                      <div>
                                        <div className="text-sm font-medium text-gray-900">
                                          {ao.name}
                                        </div>
                                        <div className="text-xs text-gray-500">
                                          ₹{ao.price} each
                                        </div>
                                      </div>
                                    </div>

                                    <div className="flex items-center gap-2">
                                      {isSelected && (
                                        <div className="flex items-center bg-white border rounded-md">
                                          <Button
                                            variant="ghost"
                                            size="sm"
                                            className="h-7 w-7"
                                            onClick={() => {
                                              setUpdatedItems((prev) => {
                                                const copy = [...prev];
                                                const target = {
                                                  ...copy[index],
                                                };
                                                const list = Array.isArray(
                                                  target.addOns
                                                )
                                                  ? [...target.addOns]
                                                  : [];
                                                const idx = list.findIndex(
                                                  (s) =>
                                                    (s.addOnId || s._id) ===
                                                    ao._id
                                                );
                                                if (idx >= 0) {
                                                  const newQuantity = Math.max(
                                                    1,
                                                    (list[idx].quantity || 1) -
                                                      1
                                                  );
                                                  if (newQuantity === 1) {
                                                    list.splice(idx, 1);
                                                  } else {
                                                    list[idx] = {
                                                      ...list[idx],
                                                      quantity: newQuantity,
                                                    };
                                                  }
                                                }
                                                target.addOns = list;
                                                copy[index] = target;
                                                return copy;
                                              });
                                            }}
                                          >
                                            <Minus className="h-3 w-3" />
                                          </Button>
                                          <span className="w-6 text-center text-xs font-semibold">
                                            {quantity}
                                          </span>
                                          <Button
                                            variant="ghost"
                                            size="sm"
                                            className="h-7 w-7"
                                            onClick={() => {
                                              setUpdatedItems((prev) => {
                                                const copy = [...prev];
                                                const target = {
                                                  ...copy[index],
                                                };
                                                const list = Array.isArray(
                                                  target.addOns
                                                )
                                                  ? [...target.addOns]
                                                  : [];
                                                const idx = list.findIndex(
                                                  (s) =>
                                                    (s.addOnId || s._id) ===
                                                    ao._id
                                                );
                                                if (idx >= 0) {
                                                  list[idx] = {
                                                    ...list[idx],
                                                    quantity:
                                                      (list[idx].quantity ||
                                                        1) + 1,
                                                  };
                                                }
                                                target.addOns = list;
                                                copy[index] = target;
                                                return copy;
                                              });
                                            }}
                                          >
                                            <Plus className="h-3 w-3" />
                                          </Button>
                                        </div>
                                      )}

                                      <Button
                                        size="sm"
                                        variant={
                                          isSelected ? "secondary" : "outline"
                                        }
                                        className={`text-xs px-3 ${
                                          isSelected
                                            ? "bg-green-100 text-green-700 hover:bg-green-200"
                                            : "hover:bg-green-50 hover:border-green-200 hover:text-green-700"
                                        }`}
                                        onClick={() => {
                                          setUpdatedItems((prev) => {
                                            const copy = [...prev];
                                            const target = { ...copy[index] };
                                            const list = Array.isArray(
                                              target.addOns
                                            )
                                              ? [...target.addOns]
                                              : [];
                                            const idx = list.findIndex(
                                              (s) =>
                                                (s.addOnId || s._id) === ao._id
                                            );

                                            if (isSelected && idx >= 0) {
                                              list.splice(idx, 1);
                                            } else if (!isSelected) {
                                              list.push({
                                                addOnId: ao._id,
                                                name: ao.name,
                                                price: ao.price,
                                                quantity: 1,
                                              });
                                            }

                                            target.addOns = list;
                                            copy[index] = target;
                                            return copy;
                                          });
                                        }}
                                      >
                                        {isSelected ? "Remove" : "Add"}
                                      </Button>
                                    </div>
                                  </div>
                                );
                              }
                            )}
                          </div>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* Bottom Section - Coupon & Summary */}
          <div className="mt-6 space-y-4">
            {/* Coupon Section */}
            <div className="bg-gradient-to-r from-orange-50 to-amber-50 rounded-xl p-5 border border-orange-100">
              <div className="flex items-center gap-3 mb-4">
                <Tag className="h-5 w-5 text-orange-600" />
                <h4 className="font-semibold text-orange-900">Apply Coupon</h4>
              </div>

              {!appliedCoupon ? (
                <div className="flex flex-col sm:flex-row gap-3">
                  <div className="flex-1">
                    <Input
                      id="coupon"
                      value={couponCode}
                      onChange={(e) =>
                        setCouponCode(e.target.value.toUpperCase())
                      }
                      placeholder="Enter coupon code"
                      disabled={isLoading || couponLoading}
                      className="bg-white border-orange-200 focus:border-orange-400 focus:ring-orange-100"
                    />
                  </div>
                  <Button
                    type="button"
                    variant="outline"
                    onClick={handleApplyCoupon}
                    disabled={isLoading || couponLoading || !couponCode.trim()}
                    className="border-orange-200 text-orange-600 hover:bg-orange-100 hover:border-orange-300"
                  >
                    {couponLoading ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : (
                      <>
                        <Tag className="h-4 w-4 mr-2" />
                        Apply Coupon
                      </>
                    )}
                  </Button>
                </div>
              ) : (
                <div className="bg-white rounded-lg border border-green-200 p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="p-2 bg-green-100 rounded-lg">
                        <CheckCircle className="h-4 w-4 text-green-600" />
                      </div>
                      <div>
                        <div className="font-semibold text-green-800">
                          {appliedCoupon.code}
                        </div>
                        <div className="text-sm text-green-600">
                          ₹{estimateCouponDiscount().toFixed(2)} discount
                          applied
                        </div>
                      </div>
                    </div>
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={removeCoupon}
                      disabled={isLoading}
                      className="text-red-500 hover:bg-red-50"
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              )}
            </div>

            {/* Order Summary */}
            <div className="bg-gradient-to-r from-slate-50 to-gray-50 rounded-xl p-5 border border-slate-200">
              <div className="flex items-center gap-3 mb-4">
                <Calculator className="h-5 w-5 text-slate-600" />
                <h4 className="font-semibold text-slate-900">Order Summary</h4>
              </div>

              <div className="bg-white rounded-lg border border-slate-200 p-4">
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600">Subtotal:</span>
                    <span className="font-medium">
                      ₹{calculateSubtotal().toFixed(2)}
                    </span>
                  </div>

                  {appliedOffers.length > 0 && (
                    <div className="flex justify-between items-center text-blue-600">
                      <span>Offer Discount:</span>
                      <span className="font-medium">
                        -₹{estimateOfferDiscount().toFixed(2)}
                      </span>
                    </div>
                  )}

                  {appliedCoupon && appliedCoupon.discount > 0 && (
                    <div className="flex justify-between items-center text-green-600">
                      <span>Coupon Discount ({appliedCoupon.code}):</span>
                      <span className="font-medium">
                        -₹{estimateCouponDiscount().toFixed(2)}
                      </span>
                    </div>
                  )}

                  <div className="border-t border-gray-200 pt-3">
                    <div className="flex justify-between items-center">
                      <span className="text-lg font-bold text-gray-900">
                        Total:
                      </span>
                      <span className="text-xl font-bold text-blue-600">
                        ₹{calculateTotal().toFixed(2)}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <DialogFooter className="pt-4 border-t border-gray-100 bg-gray-50 rounded-b-lg">
          <div className="flex flex-col sm:flex-row justify-between items-center gap-3 w-full">
            <div className="text-sm text-gray-500">
              {updatedItems.length > 0 && (
                <span>
                  {updatedItems.reduce((sum, item) => sum + item.quantity, 0)}{" "}
                  items • ₹{calculateTotal().toFixed(2)}
                </span>
              )}
            </div>
            <div className="flex gap-3">
              <Button
                variant="outline"
                onClick={onClose}
                disabled={isLoading}
                className="px-6"
              >
                Cancel
              </Button>
              <Button
                onClick={handleUpdateOrder}
                disabled={isLoading || updatedItems.length === 0}
                className="bg-blue-600 hover:bg-blue-700 px-8"
              >
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Updating Order...
                  </>
                ) : (
                  <>
                    <ShoppingCart className="mr-2 h-4 w-4" />
                    Update Order
                  </>
                )}
              </Button>
            </div>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
