"use client";

import React, { useState, useEffect, useCallback } from "react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Outlet, User, Dish } from "@/app/type";
import {
  getAllOutlets,
  getAllCustomersForOrderCreation,
  getAllDishes,
  createManualOrder,
  createCustomer,
  validateCouponForOrder,
} from "@/server/admin";
import { calculateOfferDiscount } from "@/utils/offerCalculations";
import { toast } from "sonner";
import {
  Loader2,
  Plus,
  Minus,
  Trash2,
  UserPlus,
  User as UserIcon,
  Tag,
  X,
  Mail,
  ShoppingCart,
  Store,
  MapPin,
  Users,
  Phone,
  UserX,
  Search,
  CheckCircle,
  ChefHat,
  MessageSquare,
  Hash,
  ShoppingBag,
  Utensils,
} from "lucide-react";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
// import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { getApplicableOffers } from "@/server/marketing";
import TableSelector from "@/components/custom/table/TableSelector";

interface CreateOrderDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess?: () => void;
  outletId?: string;
}

export default function CreateOrderDialog({
  open,
  onOpenChange,
  onSuccess,
  outletId,
}: CreateOrderDialogProps) {
  const [offers, setOffers] = useState([]);
  const [loading, setLoading] = useState(false);
  const [outlets, setOutlets] = useState<Outlet[]>([]);
  const [customers, setCustomers] = useState<User[]>([]);
  const [allDishes, setAllDishes] = useState<Dish[]>([]);
  const [filteredDishes, setFilteredDishes] = useState<Dish[]>([]);
  const [selectedOutlet, setSelectedOutlet] = useState<string>(outletId || "");
  const [selectedCustomer, setSelectedCustomer] = useState<string>("");
  const [paymentMethod, setPaymentMethod] = useState<"cash" | "online">("cash");
  const [specialInstructions, setSpecialInstructions] = useState<string>("");
  const [tableNumber, setTableNumber] = useState<string>("");
  const [selectedTableId, setSelectedTableId] = useState<string | null>(null);
  const [orderItems, setOrderItems] = useState<
    {
      dishId: string;
      quantity: number;
      addOns?: Array<{
        addOnId?: string;
        _id?: string;
        name?: string;
        price?: number;
        quantity?: number;
      }>;
    }[]
  >([]);
  const [dishSearchTerm, setDishSearchTerm] = useState<string>("");
  const [offerDiscount, setOfferDiscount] = useState<number>(0);
  const [customerSearchTerm, setCustomerSearchTerm] = useState<string>("");
  const [filteredCustomers, setFilteredCustomers] = useState<User[]>([]);
  const [isLoadingCustomers, setIsLoadingCustomers] = useState<boolean>(false);
  const [totalAmount, setTotalAmount] = useState<number>(0);

  // Coupon related state
  const [couponCode, setCouponCode] = useState<string>("");
  const [validatingCoupon, setValidatingCoupon] = useState<boolean>(false);
  const [appliedCoupon, setAppliedCoupon] = useState<{
    code: string;
    discount: number;
    discountType: "percentage" | "fixed";
    discountValue: number;
  } | null>(null);
  const [finalAmount, setFinalAmount] = useState<number>(0);
  const [sendInvoiceEmail, setSendInvoiceEmail] = useState<boolean>(true);

  // Miscellaneous amount state
  const [miscellaneousAmount, setMiscellaneousAmount] = useState<number>(0);
  const [miscellaneousReason, setMiscellaneousReason] = useState<string>("");

  // New customer form state
  const [showNewCustomerForm, setShowNewCustomerForm] =
    useState<boolean>(false);
  const [newCustomerName, setNewCustomerName] = useState<string>("");
  const [newCustomerEmail, setNewCustomerEmail] = useState<string>("");
  const [newCustomerPhone, setNewCustomerPhone] = useState<string>("");
  const [creatingCustomer, setCreatingCustomer] = useState<boolean>(false);
  // We need to store the password temporarily but don't display it in the UI
  // It's shown in toast notifications instead
  const [, setNewCustomerPassword] = useState<string | null>(null);

  useEffect(() => {
    if (outletId) {
      setSelectedOutlet(outletId);
    }
  }, [outletId]);

  const getAllOffers = useCallback(
    async (outletId: string) => {
      try {
        const response = await getApplicableOffers({
          outletId,
          orderAmount: totalAmount,
          customerId: selectedCustomer,
          items: orderItems,
          foodChainId: localStorage.getItem("chainId") || "",
        });
        if (response.success) {
          setOffers(response.data);
        } else {
          toast.error(response.message || "Failed to fetch offers");
        }
      } catch (error) {
        console.error("Error fetching offers:", error);
      }
    },
    [totalAmount, selectedCustomer, orderItems]
  );

  useEffect(() => {
    if (open) {
      console.log("Dialog opened, fetching initial data");
      fetchInitialData();
    }
  }, [open]);

  // Debug log for customers
  useEffect(() => {
    console.log("Customers state updated:", customers);
  }, [customers]);

  useEffect(() => {
    if (selectedOutlet) {
      fetchOutletDishes(selectedOutlet);
    }
  }, [selectedOutlet]);

  useEffect(() => {
    // Filter dishes based on search term
    if (allDishes.length > 0) {
      const filtered = allDishes.filter((dish) =>
        dish.name.toLowerCase().includes(dishSearchTerm.toLowerCase())
      );
      setFilteredDishes(filtered);
    }
  }, [dishSearchTerm, allDishes]);

  useEffect(() => {
    // Calculate total amount
    let total = 0;
    orderItems.forEach((item) => {
      const dish = allDishes.find((d) => d._id === item.dishId);
      if (dish) {
        total += dish.price * item.quantity;
        const addOnsTotal = (item.addOns || []).reduce(
          (sum, ao) => sum + (ao.price || 0) * (ao.quantity || 1),
          0
        );
        total += addOnsTotal;
      }
    });
    setTotalAmount(total);
  }, [orderItems, allDishes, appliedCoupon]);

  useEffect(() => {
    if (
      selectedOutlet &&
      selectedCustomer &&
      orderItems.length > 0 &&
      totalAmount > 0
    ) {
      getAllOffers(selectedOutlet);
    }
  }, [selectedOutlet, selectedCustomer, orderItems, totalAmount, getAllOffers]);

  // Update final amount when total amount or applied coupon changes
  useEffect(() => {
    const amount = totalAmount;
    let offerDiscountAmount = 0;

    // Calculate offer discounts based on offer type
    if (offers.length > 0) {
      // Convert orderItems to CartItem format for calculation
      const cartItems = orderItems.map((item) => {
        const dish = allDishes.find((d: Dish) => d._id === item.dishId);
        return dish ? {
          ...dish,
          quantity: item.quantity,
        } : null;
      }).filter((item): item is NonNullable<typeof item> => item !== null);

      // Calculate discount for each offer
      offers.forEach((offer) => {
        const result = calculateOfferDiscount(cartItems, offer, amount);
        offerDiscountAmount += result.discount;
      });
    }
    setOfferDiscount(offerDiscountAmount);

    // Apply offers first, then coupons
    const totalAfterOffers = Math.max(0, amount - offerDiscountAmount);
    let couponDiscountAmount = 0;

    if (appliedCoupon) {
      if (appliedCoupon.discountType === "percentage") {
        couponDiscountAmount =
          (totalAfterOffers * appliedCoupon.discountValue) / 100;
      } else {
        couponDiscountAmount = appliedCoupon.discount;
      }
    }

    const amountAfterCoupons = Math.max(
      0,
      totalAfterOffers - couponDiscountAmount
    );
    setFinalAmount(Math.max(0, amountAfterCoupons + miscellaneousAmount));
  }, [totalAmount, appliedCoupon, offers, miscellaneousAmount, allDishes, orderItems]);

  const fetchInitialData = async () => {
    try {
      // Fetch outlets
      const outletsResponse = await getAllOutlets();
      if (outletsResponse.success) {
        setOutlets(outletsResponse.data);
        if (outletsResponse?.data && outletsResponse.data.length == 1) {
          setSelectedOutlet(outletsResponse.data[0]._id);
        }
      } else {
        toast.error("Failed to load outlets");
      }

      // Fetch all customers for order creation (no food chain filtering)
      const customersResponse = await getAllCustomersForOrderCreation({
        page: 1,
        limit: 100,
      });
      console.log("Raw customer response:", customersResponse);

      if (customersResponse && customersResponse.success) {
        // Handle different response formats
        if (customersResponse.data && customersResponse.data.customers) {
          setCustomers(customersResponse.data.customers);
        } else if (Array.isArray(customersResponse.data)) {
          setCustomers(customersResponse.data);
        } else {
          console.error(
            "Unexpected customer data format:",
            customersResponse.data
          );
          setCustomers([]);
        }
      } else {
        console.error("Failed to load customers:", customersResponse);
        toast.error("Failed to load customers");
        setCustomers([]);
      }

      // Reset search states
      setCustomerSearchTerm("");
      setFilteredCustomers([]);
    } catch (error) {
      console.error("Error fetching initial data:", error);
      toast.error("Failed to load initial data");
    }
  };

  const fetchOutletDishes = async (outletId: string) => {
    try {
      const dishesResponse = await getAllDishes();
      if (dishesResponse.success) {
        // Filter dishes that are available in the selected outlet
        const outletDishes = dishesResponse.data.filter(
          (dish: Dish) => dish.outlets.includes(outletId) && dish.isAvailable
        );
        setAllDishes(outletDishes);
        setFilteredDishes(outletDishes);
      } else {
        toast.error("Failed to load dishes");
      }
    } catch (error) {
      console.error("Error fetching dishes:", error);
      toast.error("Failed to load dishes");
    }
  };

  // Minimal inline handlers for add-ons in Selected Items card
  type AddOnSel = {
    addOnId?: string;
    _id?: string;
    name: string;
    price: number;
    quantity?: number;
  };
  const toggleAddOnForOrderItem = (
    dishId: string,
    addOn: { _id: string; name: string; price: number }
  ) => {
    setOrderItems((prev) =>
      prev.map((it) => {
        if (it.dishId !== dishId) return it;
        const list: AddOnSel[] = Array.isArray(it.addOns)
          ? [...(it.addOns as AddOnSel[])]
          : [];
        const idx = list.findIndex(
          (ao) => (ao.addOnId || ao._id) === addOn._id
        );
        if (idx >= 0) list.splice(idx, 1);
        else
          list.push({
            addOnId: addOn._id,
            name: addOn.name,
            price: addOn.price,
            quantity: 1,
          });
        return { ...it, addOns: list };
      })
    );
  };
  const changeOrderItemAddOnQty = (
    dishId: string,
    addOnId: string,
    qty: number
  ) => {
    setOrderItems((prev) =>
      prev.map((it) => {
        if (it.dishId !== dishId) return it;
        const list: AddOnSel[] = (
          (it.addOns as AddOnSel[] | undefined) || []
        ).map((ao) =>
          (ao.addOnId || ao._id) === addOnId
            ? { ...ao, quantity: Math.max(1, qty) }
            : ao
        );
        return { ...it, addOns: list };
      })
    );
  };

  const handleAddDish = (dishId: string) => {
    const existingItem = orderItems.find((item) => item.dishId === dishId);
    if (existingItem) {
      // Increment quantity if dish already exists in order
      setOrderItems(
        orderItems.map((item) =>
          item.dishId === dishId
            ? { ...item, quantity: item.quantity + 1 }
            : item
        )
      );
    } else {
      // Add new dish to order
      setOrderItems([...orderItems, { dishId, quantity: 1 }]);
    }
  };

  const handleRemoveDish = (dishId: string) => {
    setOrderItems(orderItems.filter((item) => item.dishId !== dishId));
  };

  const handleQuantityChange = (dishId: string, newQuantity: number) => {
    if (newQuantity < 1) {
      handleRemoveDish(dishId);
      return;
    }

    setOrderItems(
      orderItems.map((item) =>
        item.dishId === dishId ? { ...item, quantity: newQuantity } : item
      )
    );
  };

  // Table selection handlers
  const handleTableSelect = (tableId: string | null, tableName: string) => {
    setSelectedTableId(tableId);
    setTableNumber(tableName);
  };

  const handleTableNumberChange = (tableNumber: string) => {
    setTableNumber(tableNumber);
  };

  const resetForm = () => {
    setSelectedOutlet("");
    setSelectedCustomer("");
    setPaymentMethod("cash");
    setSpecialInstructions("");
    setTableNumber("");
    setSelectedTableId(null);
    setOrderItems([]);
    setDishSearchTerm("");
    setCustomerSearchTerm("");
    setAllDishes([]);
    setFilteredDishes([]);
    setShowNewCustomerForm(false);
    setNewCustomerName("");
    setNewCustomerEmail("");
    setNewCustomerPhone("");
    setCouponCode("");
    setAppliedCoupon(null);
    setSendInvoiceEmail(true);
    setOffers([]);
    setMiscellaneousAmount(0);
    setMiscellaneousReason("");
  };

  // Function to validate and apply coupon
  const handleValidateCoupon = async () => {
    if (!couponCode.trim()) {
      toast.error("Please enter a coupon code");
      return;
    }

    if (!selectedOutlet) {
      toast.error("Please select an outlet first");
      return;
    }

    if (totalAmount <= 0) {
      toast.error("Please add items to the order first");
      return;
    }
    setValidatingCoupon(true);
    try {
      const response = await validateCouponForOrder({
        code: couponCode,
        outletId: selectedOutlet,
        amount: totalAmount - offerDiscount,
      });

      if (response.success) {
        setAppliedCoupon({
          code: couponCode.toUpperCase(),
          discount: response.data.discount,
          discountType: response.data.coupon.discountType,
          discountValue: response.data.coupon.discountValue,
        });
        toast.success("Coupon applied successfully!");
      } else {
        toast.error(response.message || "Failed to apply coupon");
      }
    } catch (error) {
      console.error("Error validating coupon:", error);
      toast.error("An error occurred while validating the coupon");
    } finally {
      setValidatingCoupon(false);
    }
  };

  // Function to remove applied coupon
  const handleRemoveCoupon = () => {
    setAppliedCoupon(null);
    setCouponCode("");
  };

  // Function to search for customers
  const searchCustomers = async (searchTerm: string) => {
    if (!searchTerm || searchTerm.length < 2) {
      return;
    }

    setIsLoadingCustomers(true);
    try {
      const response = await getAllCustomersForOrderCreation({
        page: 1,
        limit: 10,
        search: searchTerm,
      });
      if (response.success) {
        // Handle different response formats
        if (response.data && response.data.customers) {
          setFilteredCustomers(response.data.customers);
        } else if (Array.isArray(response.data)) {
          setFilteredCustomers(response.data);
        } else {
          console.error("Unexpected customer data format:", response.data);
          setFilteredCustomers([]);
        }
      } else {
        console.error("Failed to search customers:", response);
        setFilteredCustomers([]);
      }
    } catch (error) {
      console.error("Error searching customers:", error);
      setFilteredCustomers([]);
    } finally {
      setIsLoadingCustomers(false);
    }
  };

  const handleCreateCustomer = async () => {
    // Validate form
    if (!newCustomerName) {
      toast.error("Please enter customer name");
      return;
    }

    if (!newCustomerEmail) {
      toast.error("Please enter customer email");
      return;
    }

    setCreatingCustomer(true);
    try {
      const response = await createCustomer({
        name: newCustomerName,
        email: newCustomerEmail,
        phone: newCustomerPhone,
      });

      if (response.success) {
        // Store the initial password if provided
        if (response.data.initialPassword) {
          setNewCustomerPassword(response.data.initialPassword);

          // Show a toast with instructions
          toast.success(
            "Customer created successfully. Please note down the temporary password: " +
              response.data.initialPassword
          );
        } else {
          toast.success("Customer created successfully");
        }

        // Add the new customer to the customers list
        const newCustomer = response.data;
        setCustomers([...customers, newCustomer]);

        // Select the new customer
        setSelectedCustomer(newCustomer._id);

        // Reset the form
        setShowNewCustomerForm(false);
        setNewCustomerName("");
        setNewCustomerEmail("");
        setNewCustomerPhone("");
      } else {
        toast.error(response.message || "Failed to create customer");
      }
    } catch (error) {
      console.error("Error creating customer:", error);
      toast.error("An error occurred while creating the customer");
    } finally {
      setCreatingCustomer(false);
    }
  };

  const handleSubmit = async () => {
    // Validate form
    if (!selectedOutlet) {
      toast.error("Please select an outlet");
      return;
    }

    if (
      (!selectedCustomer || selectedCustomer === "no-id-placeholder") &&
      !showNewCustomerForm
    ) {
      toast.error("Please select a valid customer");
      return;
    }

    if (showNewCustomerForm) {
      // Create customer first
      if (!newCustomerName || !newCustomerEmail) {
        toast.error("Please fill in customer details");
        return;
      }

      // Create the customer and then proceed with order creation
      setCreatingCustomer(true);
      try {
        const customerResponse = await createCustomer({
          name: newCustomerName,
          email: newCustomerEmail,
          phone: newCustomerPhone,
        });

        if (!customerResponse.success) {
          toast.error(customerResponse.message || "Failed to create customer");
          setCreatingCustomer(false);
          return;
        }

        // Store the initial password if provided
        if (customerResponse.data.initialPassword) {
          setNewCustomerPassword(customerResponse.data.initialPassword);

          // Show a toast with instructions
          toast.success(
            "Customer created successfully. Please note down the temporary password: " +
              customerResponse.data.initialPassword
          );
        }

        // Set the newly created customer as the selected customer
        setSelectedCustomer(customerResponse.data._id);
        setCreatingCustomer(false);
      } catch (error) {
        console.error("Error creating customer:", error);
        toast.error("An error occurred while creating the customer");
        setCreatingCustomer(false);
        return;
      }
    }

    if (orderItems.length === 0) {
      toast.error("Please add at least one dish to the order");
      return;
    }

    setLoading(true);
    try {
      const orderPayload: {
        outletId: string;
        customerId: string;
        items: {
          dishId: string;
          quantity: number;
          addOns?: Array<{
            addOnId?: string;
            _id?: string;
            name?: string;
            price?: number;
            quantity?: number;
          }>;
        }[];
        specialInstructions?: string;
        tableNumber?: string;
        tableId?: string | null;
        paymentMethod?: "cash" | "online";
        sendInvoiceEmail?: boolean;
        couponCode?: string;
        couponDiscount?: number;
        discountValue?: number;
        discountType?: string;
        miscellaneousAmount?: number;
        miscellaneousReason?: string;
      } = {
        outletId: selectedOutlet,
        customerId: selectedCustomer,
        items: orderItems,
        specialInstructions,
        tableNumber,
        tableId: selectedTableId,
        paymentMethod,
        sendInvoiceEmail,
      };
      // Add coupon information if a coupon is applied
      if (appliedCoupon) {
        orderPayload.couponCode = appliedCoupon.code;
        orderPayload.couponDiscount = appliedCoupon.discount;
        orderPayload.discountValue = appliedCoupon.discountValue;
        orderPayload.discountType = appliedCoupon.discountType;
      }
      // Add miscellaneous amount if specified
      if (miscellaneousAmount !== 0) {
        orderPayload.miscellaneousAmount = miscellaneousAmount;
        orderPayload.miscellaneousReason = miscellaneousReason;
      }
      const response = await createManualOrder(orderPayload);

      if (response.success) {
        let successMessage = "Order created successfully";
        if (sendInvoiceEmail) {
          successMessage += ". Invoice has been sent to the customer's email.";
        }
        toast.success(successMessage);
        resetForm();
        if (onSuccess) onSuccess();
        onOpenChange(false);
      } else {
        toast.error(response.message || "Failed to create order");
      }
    } catch (error) {
      console.error("Error creating order:", error);
      toast.error("An error occurred while creating the order");
    } finally {
      setLoading(false);
    }
  };

  const formatOutletDisplay = (outlet: Outlet) => {
    return `${outlet.name} (${outlet.address.split(",")[0]})`;
  };

  const getDishById = (dishId: string) => {
    return allDishes.find((dish) => dish._id === dishId);
  };

  const handleOpenChange = (open: boolean) => {
    if (!open) {
      resetForm();
    }
    onOpenChange(open);
  };

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent className="sm:max-w-[800px] max-h-[95vh] overflow-hidden flex flex-col">
        <DialogHeader className="pb-4 border-b border-gray-100">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-blue-50 rounded-lg">
              <ShoppingCart className="h-5 w-5 text-blue-600" />
            </div>
            <div>
              <DialogTitle className="text-xl font-semibold">
                Create New Order
              </DialogTitle>
              <DialogDescription className="text-gray-500 mt-1">
                Create a manual order for a customer with real-time pricing
              </DialogDescription>
            </div>
          </div>
        </DialogHeader>

        <div className="flex-1 overflow-y-auto">
          <div className="space-y-6 p-1">
            {/* Progress Indicator */}
            <div className="flex items-center gap-2 text-sm">
              <div
                className={`flex items-center gap-2 px-3 py-1 rounded-full ${
                  selectedOutlet
                    ? "bg-green-100 text-green-700"
                    : "bg-gray-100 text-gray-500"
                }`}
              >
                <div
                  className={`w-2 h-2 rounded-full ${
                    selectedOutlet ? "bg-green-500" : "bg-gray-300"
                  }`}
                ></div>
                Outlet
              </div>
              <div className="w-4 h-px bg-gray-200"></div>
              <div
                className={`flex items-center gap-2 px-3 py-1 rounded-full ${
                  (selectedCustomer &&
                    selectedCustomer !== "no-id-placeholder") ||
                  (showNewCustomerForm && newCustomerName && newCustomerEmail)
                    ? "bg-green-100 text-green-700"
                    : "bg-gray-100 text-gray-500"
                }`}
              >
                <div
                  className={`w-2 h-2 rounded-full ${
                    (selectedCustomer &&
                      selectedCustomer !== "no-id-placeholder") ||
                    (showNewCustomerForm && newCustomerName && newCustomerEmail)
                      ? "bg-green-500"
                      : "bg-gray-300"
                  }`}
                ></div>
                Customer
              </div>
              <div className="w-4 h-px bg-gray-200"></div>
              <div
                className={`flex items-center gap-2 px-3 py-1 rounded-full ${
                  orderItems.length > 0
                    ? "bg-green-100 text-green-700"
                    : "bg-gray-100 text-gray-500"
                }`}
              >
                <div
                  className={`w-2 h-2 rounded-full ${
                    orderItems.length > 0 ? "bg-green-500" : "bg-gray-300"
                  }`}
                ></div>
                Items ({orderItems.length})
              </div>
            </div>

            {/* Outlet Selection Card */}
            <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-5 border border-blue-100">
              <div className="flex items-center gap-3 mb-3">
                <Store className="h-5 w-5 text-blue-600" />
                <Label className="text-base font-semibold text-gray-900">
                  Select Outlet
                </Label>
              </div>
              <Select value={selectedOutlet} onValueChange={setSelectedOutlet}>
                <SelectTrigger className="bg-white border-blue-200 focus:border-blue-400 focus:ring-blue-100">
                  <SelectValue placeholder="Choose your outlet location" />
                </SelectTrigger>
                <SelectContent>
                  {outlets.map((outlet) => (
                    <SelectItem key={outlet._id} value={outlet._id || ""}>
                      <div className="flex items-center gap-2">
                        <MapPin className="h-4 w-4 text-gray-400" />
                        {formatOutletDisplay(outlet)}
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Customer Selection Card */}
            <div className="bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl p-5 border border-green-100">
              <div className="flex justify-between items-center mb-3">
                <div className="flex items-center gap-3">
                  <Users className="h-5 w-5 text-green-600" />
                  <Label className="text-base font-semibold text-gray-900">
                    Customer Details
                  </Label>
                </div>
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowNewCustomerForm(!showNewCustomerForm)}
                  className="text-green-600 hover:text-green-700 hover:bg-green-100 flex items-center gap-2 px-3 py-2 rounded-lg transition-colors"
                >
                  {showNewCustomerForm ? (
                    <>
                      <UserIcon className="h-4 w-4" />
                      Select Existing
                    </>
                  ) : (
                    <>
                      <UserPlus className="h-4 w-4" />
                      Create New
                    </>
                  )}
                </Button>
              </div>

              {!showNewCustomerForm ? (
                <div className="space-y-3">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                    <Input
                      placeholder="Search customers by name, email, or phone"
                      value={customerSearchTerm}
                      onChange={(e) => {
                        const value = e.target.value;
                        setCustomerSearchTerm(value);
                        if (value.length >= 2) {
                          searchCustomers(value);
                        } else if (value.length === 0) {
                          setFilteredCustomers([]);
                        }
                      }}
                      className="pl-10 bg-white border-green-200 focus:border-green-400 focus:ring-green-100"
                    />
                    {isLoadingCustomers && (
                      <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                        <Loader2 className="h-4 w-4 animate-spin text-green-500" />
                      </div>
                    )}
                  </div>

                  {customerSearchTerm.length >= 2 ? (
                    <div className="bg-white border border-green-200 rounded-lg max-h-[200px] overflow-y-auto shadow-sm">
                      {filteredCustomers.length > 0 ? (
                        filteredCustomers.map((customer) => (
                          <div
                            key={customer._id}
                            className={`p-3 hover:bg-green-50 cursor-pointer border-b border-gray-50 last:border-b-0 transition-colors ${
                              selectedCustomer === customer._id
                                ? "bg-green-50 border-green-200"
                                : ""
                            }`}
                            onClick={() => {
                              setSelectedCustomer(customer._id || "");
                              setCustomerSearchTerm("");
                            }}
                          >
                            <div className="font-medium text-gray-900">
                              {customer.name}
                            </div>
                            <div className="text-sm text-gray-500 flex items-center gap-2 mt-1">
                              <Mail className="h-3 w-3" />
                              <span>{customer.email}</span>
                              {customer.phone && (
                                <>
                                  <span>•</span>
                                  <Phone className="h-3 w-3" />
                                  <span>{customer.phone}</span>
                                </>
                              )}
                            </div>
                          </div>
                        ))
                      ) : (
                        <div className="p-4 text-center text-gray-500">
                          <UserX className="h-8 w-8 mx-auto mb-2 text-gray-300" />
                          No customers found matching your search
                        </div>
                      )}
                    </div>
                  ) : (
                    <Select
                      value={selectedCustomer}
                      onValueChange={setSelectedCustomer}
                    >
                      <SelectTrigger className="bg-white border-green-200 focus:border-green-400 focus:ring-green-100">
                        <SelectValue placeholder="Select a customer" />
                      </SelectTrigger>
                      <SelectContent>
                        {Array.isArray(customers) && customers.length > 0 ? (
                          customers.map((customer) => (
                            <SelectItem
                              key={customer._id}
                              value={customer._id || "no-id-placeholder"}
                            >
                              <div className="flex items-center gap-2">
                                <UserIcon className="h-4 w-4 text-gray-400" />
                                <div>
                                  <div>{customer.name}</div>
                                  {customer.phone && (
                                    <div className="text-xs text-gray-500">
                                      ({customer.phone})
                                    </div>
                                  )}
                                </div>
                              </div>
                            </SelectItem>
                          ))
                        ) : (
                          <div className="p-2 text-center text-gray-500">
                            No customers found
                          </div>
                        )}
                      </SelectContent>
                    </Select>
                  )}

                  {selectedCustomer && (
                    <div className="bg-white p-3 rounded-lg border border-green-200">
                      <div className="flex items-center gap-2 text-sm text-green-700">
                        <CheckCircle className="h-4 w-4" />
                        <span className="font-medium">
                          {customers.find((c) => c._id === selectedCustomer)
                            ?.name || "Selected customer"}
                        </span>
                      </div>
                    </div>
                  )}
                </div>
              ) : (
                <div className="bg-white rounded-lg border border-green-200 p-4">
                  <div className="grid gap-4">
                    <div className="grid gap-2">
                      <Label
                        htmlFor="newCustomerName"
                        className="flex items-center gap-2"
                      >
                        <UserIcon className="h-4 w-4" />
                        Name *
                      </Label>
                      <Input
                        id="newCustomerName"
                        value={newCustomerName}
                        onChange={(e) => setNewCustomerName(e.target.value)}
                        placeholder="Enter customer name"
                        className="focus:border-green-400 focus:ring-green-100"
                      />
                    </div>
                    <div className="grid gap-2">
                      <Label
                        htmlFor="newCustomerEmail"
                        className="flex items-center gap-2"
                      >
                        <Mail className="h-4 w-4" />
                        Email *
                      </Label>
                      <Input
                        id="newCustomerEmail"
                        type="email"
                        value={newCustomerEmail}
                        onChange={(e) => setNewCustomerEmail(e.target.value)}
                        placeholder="Enter customer email"
                        className="focus:border-green-400 focus:ring-green-100"
                      />
                    </div>
                    <div className="grid gap-2">
                      <Label
                        htmlFor="newCustomerPhone"
                        className="flex items-center gap-2"
                      >
                        <Phone className="h-4 w-4" />
                        Phone <span className="text-gray-400">(optional)</span>
                      </Label>
                      <Input
                        id="newCustomerPhone"
                        value={newCustomerPhone}
                        onChange={(e) => setNewCustomerPhone(e.target.value)}
                        placeholder="Enter customer phone"
                        className="focus:border-green-400 focus:ring-green-100"
                      />
                    </div>
                    <Button
                      type="button"
                      onClick={handleCreateCustomer}
                      disabled={
                        creatingCustomer ||
                        !newCustomerName ||
                        !newCustomerEmail
                      }
                      className="w-full bg-green-600 hover:bg-green-700"
                    >
                      {creatingCustomer ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Creating Customer...
                        </>
                      ) : (
                        <>
                          <UserPlus className="mr-2 h-4 w-4" />
                          Create Customer
                        </>
                      )}
                    </Button>
                  </div>
                </div>
              )}
            </div>

            {/* Additional Options */}

            {/* Special Instructions */}
            <div className="bg-gray-50 rounded-xl p-5 border border-gray-200">
              <div className="flex items-center gap-3 mb-3">
                <MessageSquare className="h-5 w-5 text-gray-600" />
                <Label className="text-base font-semibold text-gray-900">
                  Special Instructions
                </Label>
              </div>
              <Textarea
                placeholder="Add any special instructions for this order..."
                value={specialInstructions}
                onChange={(e) => setSpecialInstructions(e.target.value)}
                className="bg-white focus:border-gray-400 focus:ring-gray-100 min-h-[80px]"
              />
            </div>

            {/* Dish Selection */}
            {selectedOutlet && (
              <div className="bg-gradient-to-r from-slate-50 to-gray-50 rounded-xl p-5 border border-slate-200">
                <div className="flex justify-between items-center mb-4">
                  <div className="flex items-center gap-3">
                    <ChefHat className="h-5 w-5 text-slate-600" />
                    <Label className="text-base font-semibold text-gray-900">
                      Menu Items
                    </Label>
                  </div>
                </div>

                {/* Selected Dishes */}

                {/* Search Box */}
                <div className="relative mb-4">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder="Search dishes..."
                    value={dishSearchTerm}
                    onChange={(e) => setDishSearchTerm(e.target.value)}
                    className="pl-10 bg-white border-slate-200 focus:border-slate-400 focus:ring-slate-100"
                  />
                </div>

                {/* Available Dishes */}
                <div className="bg-white rounded-lg border border-slate-200 shadow-sm">
                  <div className="p-4 border-b border-gray-100">
                    <div className="flex items-center gap-2">
                      <ChefHat className="h-4 w-4 text-slate-600" />
                      <h4 className="font-semibold text-gray-900">
                        Available Dishes
                      </h4>
                      <span className="bg-slate-100 text-slate-600 px-2 py-1 rounded-full text-xs">
                        {filteredDishes.length} items
                      </span>
                    </div>
                  </div>
                  <div className="max-h-[300px] overflow-y-auto p-4">
                    {filteredDishes.length === 0 ? (
                      <div className="text-center py-8">
                        <ChefHat className="h-12 w-12 text-gray-300 mx-auto mb-3" />
                        <p className="text-gray-500">
                          No dishes available for this outlet
                        </p>
                      </div>
                    ) : (
                      <div className="grid gap-2">
                        {filteredDishes.map((dish) => (
                          <div
                            key={dish._id}
                            className="flex justify-between items-center p-3 hover:bg-gray-50 rounded-lg border border-transparent hover:border-gray-200 transition-all"
                          >
                            <div className="flex items-center gap-3">
                              <div className="p-2 bg-orange-50 rounded-lg">
                                <Utensils className="h-4 w-4 text-orange-600" />
                              </div>
                              <div>
                                <p className="font-semibold text-gray-900">
                                  {dish.name}
                                </p>
                                <p className="text-sm font-medium text-green-600">
                                  ₹{dish.price.toFixed(2)}
                                </p>
                              </div>
                            </div>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleAddDish(dish._id || "")}
                              className="hover:bg-blue-50 hover:border-blue-200 hover:text-blue-700"
                            >
                              <Plus className="h-4 w-4 mr-1" />
                              Add
                            </Button>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </div>

                {orderItems.length > 0 && (
                  <div className="bg-white rounded-lg border border-slate-200 p-4 mt-4 shadow-sm">
                    <div className="flex items-center gap-2 mb-3">
                      <ShoppingBag className="h-4 w-4 text-green-600" />
                      <h4 className="font-semibold text-green-700">
                        Selected Items ({orderItems.length})
                      </h4>
                    </div>
                    <div className="space-y-3">
                      {orderItems.map((item) => {
                        const dish = getDishById(item.dishId);
                        return dish ? (
                          <div
                            key={item.dishId}
                            className="bg-gray-50 rounded-lg p-3 border border-gray-200"
                          >
                            <div className="flex justify-between items-start">
                              <div className="flex-1">
                                <div className="flex items-center gap-2">
                                  <p className="font-semibold text-gray-900">
                                    {dish.name}
                                  </p>
                                  <span className="bg-blue-100 text-blue-700 px-2 py-1 rounded-full text-xs font-medium">
                                    ₹{dish.price.toFixed(2)}
                                  </span>
                                </div>

                                {!!dish.enabledAddOns?.length && (
                                  <div className="mt-3 space-y-2">
                                    <div className="text-sm font-medium text-gray-700 flex items-center gap-1">
                                      <Plus className="h-3 w-3" />
                                      Add-ons
                                    </div>
                                    <div className="grid gap-2">
                                      {dish.enabledAddOns.map(
                                        (ao: {
                                          _id: string;
                                          name: string;
                                          price: number;
                                        }) => {
                                          const selected = (
                                            item.addOns || []
                                          ).some(
                                            (s: {
                                              addOnId?: string;
                                              _id?: string;
                                            }) =>
                                              (s.addOnId || s._id) === ao._id
                                          );
                                          const qty =
                                            (item.addOns || []).find(
                                              (s: {
                                                addOnId?: string;
                                                _id?: string;
                                                quantity?: number;
                                              }) =>
                                                (s.addOnId || s._id) === ao._id
                                            )?.quantity || 1;

                                          return (
                                            <div
                                              key={ao._id}
                                              className="flex items-center justify-between bg-white border rounded-lg p-2"
                                            >
                                              <div>
                                                <div className="text-sm font-medium">
                                                  {ao.name}
                                                </div>
                                                <div className="text-xs text-gray-500">
                                                  ₹{ao.price}
                                                </div>
                                              </div>
                                              <div className="flex items-center gap-2">
                                                {selected && (
                                                  <div className="flex items-center gap-1">
                                                    <Button
                                                      variant="outline"
                                                      size="icon"
                                                      className="h-6 w-6"
                                                      onClick={() =>
                                                        changeOrderItemAddOnQty(
                                                          item.dishId,
                                                          ao._id,
                                                          Math.max(1, qty - 1)
                                                        )
                                                      }
                                                    >
                                                      <Minus className="h-3 w-3" />
                                                    </Button>
                                                    <span className="w-6 text-center text-sm font-medium">
                                                      {qty}
                                                    </span>
                                                    <Button
                                                      variant="outline"
                                                      size="icon"
                                                      className="h-6 w-6"
                                                      onClick={() =>
                                                        changeOrderItemAddOnQty(
                                                          item.dishId,
                                                          ao._id,
                                                          qty + 1
                                                        )
                                                      }
                                                    >
                                                      <Plus className="h-3 w-3" />
                                                    </Button>
                                                  </div>
                                                )}
                                                <Button
                                                  size="sm"
                                                  variant={
                                                    selected
                                                      ? "secondary"
                                                      : "outline"
                                                  }
                                                  onClick={() =>
                                                    toggleAddOnForOrderItem(
                                                      item.dishId,
                                                      ao
                                                    )
                                                  }
                                                  className="text-xs"
                                                >
                                                  {selected ? "Remove" : "Add"}
                                                </Button>
                                              </div>
                                            </div>
                                          );
                                        }
                                      )}
                                    </div>
                                  </div>
                                )}
                              </div>

                              <div className="flex items-center space-x-2 ml-4">
                                <div className="flex items-center bg-white border rounded-lg">
                                  <Button
                                    variant="ghost"
                                    size="icon"
                                    className="h-8 w-8"
                                    onClick={() =>
                                      handleQuantityChange(
                                        item.dishId,
                                        item.quantity - 1
                                      )
                                    }
                                  >
                                    <Minus className="h-3 w-3" />
                                  </Button>
                                  <span className="w-8 text-center font-semibold">
                                    {item.quantity}
                                  </span>
                                  <Button
                                    variant="ghost"
                                    size="icon"
                                    className="h-8 w-8"
                                    onClick={() =>
                                      handleQuantityChange(
                                        item.dishId,
                                        item.quantity + 1
                                      )
                                    }
                                  >
                                    <Plus className="h-3 w-3" />
                                  </Button>
                                </div>
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  className="h-8 w-8 text-red-500 hover:bg-red-50 hover:text-red-600"
                                  onClick={() => handleRemoveDish(item.dishId)}
                                >
                                  <Trash2 className="h-4 w-4" />
                                </Button>
                              </div>
                            </div>
                          </div>
                        ) : null;
                      })}
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            {/* Coupon Code */}
            <div className="bg-gradient-to-r from-orange-50 to-amber-50 rounded-xl p-5 border border-orange-100">
              <div className="flex items-center gap-3 mb-3">
                <Tag className="h-5 w-5 text-orange-600" />
                <Label className="text-base font-semibold text-gray-900">
                  Coupon Code
                </Label>
              </div>
              <div className="flex space-x-2">
                <Input
                  placeholder="Enter coupon code"
                  value={couponCode}
                  onChange={(e) => setCouponCode(e.target.value)}
                  disabled={!!appliedCoupon || validatingCoupon}
                  className="flex-1 bg-white border-orange-200 focus:border-orange-400 focus:ring-orange-100"
                />
                {appliedCoupon ? (
                  <Button
                    type="button"
                    variant="outline"
                    size="icon"
                    onClick={handleRemoveCoupon}
                    className="shrink-0 border-orange-200 text-orange-600 hover:bg-orange-100"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                ) : (
                  <Button
                    type="button"
                    variant="outline"
                    onClick={handleValidateCoupon}
                    disabled={!couponCode.trim() || validatingCoupon}
                    className="shrink-0 border-orange-200 text-orange-600 hover:bg-orange-100"
                  >
                    {validatingCoupon ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : (
                      <>
                        <Tag className="h-4 w-4 mr-2" />
                        Apply
                      </>
                    )}
                  </Button>
                )}
              </div>
              {appliedCoupon && (
                <div className="mt-3 bg-white p-3 rounded-lg border border-green-200">
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-600" />
                    <p className="font-medium text-green-700">
                      Coupon {appliedCoupon.code} applied!
                    </p>
                  </div>
                  <p className="text-green-600 text-sm mt-1">
                    {appliedCoupon.discountType === "percentage"
                      ? `${appliedCoupon.discountValue}% off`
                      : `₹${appliedCoupon.discountValue} off`}
                    {" • "}
                    Discount: ₹{appliedCoupon.discount.toFixed(2)}
                  </p>
                </div>
              )}
            </div>

            {/* Miscellaneous Amount Adjustment */}
            <div className="bg-yellow-50 rounded-xl p-5 border border-yellow-200">
              <div className="flex items-center gap-3 mb-3">
                <Plus className="h-5 w-5 text-yellow-600" />
                <Label className="text-base font-semibold text-gray-900">
                  Miscellaneous Amount
                </Label>
                <span className="text-xs text-gray-500 bg-yellow-200 px-2 py-1 rounded-full">
                  Optional
                </span>
              </div>
              <p className="text-sm text-gray-600 mb-4">
                Add or subtract an amount independent of offers and coupons
                (e.g., service charge, delivery fee, complaint discount)
              </p>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="miscellaneousAmount">
                    Amount (₹)
                    <span className="text-xs text-gray-500 ml-2">
                      (+ to add, - to subtract)
                    </span>
                  </Label>
                  <Input
                    id="miscellaneousAmount"
                    type="number"
                    step="0.01"
                    placeholder="0.00"
                    value={miscellaneousAmount || ""}
                    onChange={(e) =>
                      setMiscellaneousAmount(parseFloat(e.target.value) || 0)
                    }
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="miscellaneousReason">Reason</Label>
                  <Input
                    id="miscellaneousReason"
                    placeholder="e.g., Service charge, Delivery fee..."
                    value={miscellaneousReason}
                    onChange={(e) => setMiscellaneousReason(e.target.value)}
                  />
                </div>
              </div>
              {miscellaneousAmount !== 0 && (
                <div className="mt-3 p-3 bg-white rounded-lg border border-yellow-300">
                  <div className="text-sm">
                    <span className="text-gray-600">Adjustment: </span>
                    <span
                      className={`font-medium ${
                        miscellaneousAmount >= 0
                          ? "text-green-600"
                          : "text-red-600"
                      }`}
                    >
                      {miscellaneousAmount >= 0 ? "+" : ""}₹
                      {miscellaneousAmount.toFixed(2)}
                    </span>
                    {miscellaneousReason && (
                      <span className="text-gray-500 ml-2">
                        ({miscellaneousReason})
                      </span>
                    )}
                  </div>
                </div>
              )}
            </div>

            {/* Table & Email Options */}
            <div className="space-y-4">
              {/* Send Invoice Email */}
              <div className="bg-gradient-to-r from-purple-50 to-indigo-50 rounded-xl p-5 border border-purple-100">
                <div className="flex items-center space-x-3">
                  <Checkbox
                    id="sendInvoiceEmail"
                    checked={sendInvoiceEmail}
                    onCheckedChange={(checked) =>
                      setSendInvoiceEmail(checked as boolean)
                    }
                    className="data-[state=checked]:bg-purple-600 data-[state=checked]:border-purple-600"
                  />
                  <Label
                    htmlFor="sendInvoiceEmail"
                    className="flex items-center gap-2 cursor-pointer"
                  >
                    <Mail className="h-4 w-4 text-purple-600" />
                    Send invoice via email
                  </Label>
                </div>
                <p className="text-xs text-gray-500 ml-7 mt-1">
                  Invoice will be sent after order completion
                </p>
              </div>

              {/* Table Selection */}
              <div className="bg-gray-50 rounded-xl p-5 border border-gray-200">
                <div className="flex items-center gap-3 mb-3">
                  <Hash className="h-5 w-5 text-gray-600" />
                  <Label className="text-base font-semibold text-gray-900">
                    Table Selection
                  </Label>
                  <span className="text-xs text-gray-500 bg-gray-200 px-2 py-1 rounded-full">
                    Optional
                  </span>
                </div>
                <TableSelector
                  outletId={selectedOutlet}
                  selectedTableId={selectedTableId || undefined}
                  selectedTableNumber={tableNumber}
                  onTableSelect={handleTableSelect}
                  onTableNumberChange={handleTableNumberChange}
                />
              </div>
            </div>
          </div>
        </div>

        <DialogFooter className="pt-4 border-t border-gray-100 bg-gray-50 rounded-b-lg">
          <div className="flex justify-between items-center w-full">
            <div className="text-sm text-gray-500">
              {/* {orderItems.length > 0 && (
                <span>
                  {orderItems.reduce((sum, item) => sum + item.quantity, 0)} items • 
                  ₹{(appliedCoupon || offers.length > 0 ? finalAmount : totalAmount).toFixed(2)}
                </span>
              )} */}
              <div className="">
                {appliedCoupon || offers.length > 0 ? (
                  <div className="bg-white px-4 py-2 rounded-lg border border-green-200">
                    <div className="text-sm text-gray-500 line-through">
                      ₹{totalAmount.toFixed(2)}
                    </div>
                    <div className="text-sm text-green-600 font-medium">
                      Offers: -₹{offerDiscount.toFixed(2)}
                    </div>
                    {appliedCoupon && (
                      <div className="text-sm text-green-600 font-medium">
                        Coupon: -₹{appliedCoupon.discount.toFixed(2)}
                      </div>
                    )}
                    {miscellaneousAmount !== 0 && (
                      <div
                        className={`text-sm font-medium ${
                          miscellaneousAmount >= 0
                            ? "text-blue-600"
                            : "text-red-600"
                        }`}
                      >
                        Misc: {miscellaneousAmount >= 0 ? "+" : ""}₹
                        {miscellaneousAmount.toFixed(2)}
                      </div>
                    )}
                    <div className="text-lg font-bold text-green-700">
                      ₹{finalAmount.toFixed(2)}
                    </div>
                  </div>
                ) : (
                  <div className="bg-white px-4 py-2 rounded-lg border border-slate-200">
                    <div className="text-lg font-bold text-slate-700">
                      ₹{totalAmount.toFixed(2)}
                    </div>
                  </div>
                )}
              </div>
            </div>
            <div className="flex gap-3">
              <Button
                variant="outline"
                onClick={() => onOpenChange(false)}
                className="px-6"
              >
                Cancel
              </Button>
              <Button
                onClick={handleSubmit}
                disabled={
                  loading ||
                  creatingCustomer ||
                  !selectedOutlet ||
                  ((!selectedCustomer ||
                    selectedCustomer === "no-id-placeholder") &&
                    !showNewCustomerForm) ||
                  (showNewCustomerForm &&
                    (!newCustomerName || !newCustomerEmail)) ||
                  orderItems.length === 0
                }
                className="bg-blue-600 hover:bg-blue-700 px-8"
              >
                {loading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Creating Order...
                  </>
                ) : (
                  <>
                    <ShoppingCart className="mr-2 h-4 w-4" />
                    Create Order
                  </>
                )}
              </Button>
            </div>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
