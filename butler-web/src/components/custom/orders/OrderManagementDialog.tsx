"use client";

import React, { useState, useEffect } from "react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Order } from "@/types/order";
import { User } from "@/app/type";
import {
  updateOrderStatus,
  updateOrderPriority,
  assignOrderToStaff,
  addKitchenNotes,
} from "@/server/admin";
import { toast } from "sonner";
import {
  Loader2,
  AlertCircle,
  Clock,
  ChefHat,
  Truck,
  FileDown,
} from "lucide-react";
import { downloadOrderBill } from "@/utils/pdfGenerator";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";

interface OrderManagementDialogProps {
  order: Order;
  staffList: User[];
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess?: () => void;
}

export default function OrderManagementDialog({
  order,
  staffList,
  open,
  onOpenChange,
  onSuccess,
}: OrderManagementDialogProps) {
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState("details");
  const [selectedStatus, setSelectedStatus] = useState<
    | "cancelled"
    | "pending"
    | "confirmed"
    | "preparing"
    | "ready"
    | "completed"
    | "rejected"
    | "modified"
  >(
    order.status as
      | "cancelled"
      | "pending"
      | "confirmed"
      | "preparing"
      | "ready"
      | "completed"
      | "rejected"
      | "modified"
  );
  const [selectedPriority, setSelectedPriority] = useState<
    "low" | "normal" | "high" | "urgent"
  >(order.priority || "normal");
  const [cancellationReason, setCancellationReason] = useState("");
  const [modificationReason, setModificationReason] = useState("");
  const [kitchenNotes, setKitchenNotes] = useState(order.kitchenNotes || "");
  const [selectedStaff, setSelectedStaff] = useState("");
  const [selectedRole, setSelectedRole] = useState<
    "chef" | "delivery" | "manager"
  >("chef");

  useEffect(() => {
    if (open) {
      setSelectedStatus(order.status);
      setSelectedPriority(order.priority || "normal");
      setCancellationReason("");
      setModificationReason("");
      setKitchenNotes(order.kitchenNotes || "");
      setSelectedStaff("");
      setSelectedRole("chef");
    }
  }, [open, order]);

  const handleUpdateStatus = async () => {
    if (selectedStatus === order.status) {
      toast.info("No changes to update");
      return;
    }

    // Validate reason if cancelling or modifying
    if (selectedStatus === "cancelled" && !cancellationReason.trim()) {
      toast.error("Please provide a cancellation reason");
      return;
    }

    if (selectedStatus === "modified" && !modificationReason.trim()) {
      toast.error("Please provide a modification reason");
      return;
    }

    setLoading(true);
    try {
      const reason =
        selectedStatus === "cancelled"
          ? cancellationReason
          : selectedStatus === "modified"
          ? modificationReason
          : undefined;

      const response = await updateOrderStatus(
        order._id,
        selectedStatus,
        reason
      );

      if (response.success) {
        toast.success("Order status updated successfully");
        if (onSuccess) onSuccess();
        onOpenChange(false);
      } else {
        toast.error(response.message || "Failed to update order status");
      }
    } catch (error) {
      console.error("Error updating order status:", error);
      toast.error("An error occurred while updating order status");
    } finally {
      setLoading(false);
    }
  };

  const handleUpdatePriority = async () => {
    if (selectedPriority === order.priority) {
      toast.info("No changes to update");
      return;
    }

    setLoading(true);
    try {
      const response = await updateOrderPriority(
        order._id,
        selectedPriority as "low" | "normal" | "high" | "urgent"
      );

      if (response.success) {
        toast.success("Order priority updated successfully");
        if (onSuccess) onSuccess();
        onOpenChange(false);
      } else {
        toast.error(response.message || "Failed to update order priority");
      }
    } catch (error) {
      console.error("Error updating order priority:", error);
      toast.error("An error occurred while updating order priority");
    } finally {
      setLoading(false);
    }
  };

  const handleAssignStaff = async () => {
    if (!selectedStaff) {
      toast.error("Please select a staff member");
      return;
    }

    setLoading(true);
    try {
      const response = await assignOrderToStaff(
        order._id,
        selectedStaff,
        selectedRole
      );

      if (response.success) {
        toast.success(`Order assigned to ${selectedRole} successfully`);
        if (onSuccess) onSuccess();
        onOpenChange(false);
      } else {
        toast.error(response.message || "Failed to assign order");
      }
    } catch (error) {
      console.error("Error assigning order:", error);
      toast.error("An error occurred while assigning the order");
    } finally {
      setLoading(false);
    }
  };

  const handleOpenChange = (open: boolean) => {
    if (!open) {
      // Reset all state when dialog is closed
      setSelectedStatus(order.status);
      setSelectedPriority(order.priority || "normal");
      setCancellationReason("");
      setModificationReason("");
      setKitchenNotes(order.kitchenNotes || "");
      setSelectedStaff("");
      setSelectedRole("chef");
      setActiveTab("details");
      setLoading(false);
    }
    onOpenChange(open);
  };

  const handleAddKitchenNotes = async () => {
    if (!kitchenNotes.trim()) {
      toast.error("Please enter kitchen notes");
      return;
    }

    setLoading(true);
    try {
      const response = await addKitchenNotes(order._id, kitchenNotes);

      if (response.success) {
        toast.success("Kitchen notes added successfully");
        if (onSuccess) onSuccess();
        onOpenChange(false);
      } else {
        toast.error(response.message || "Failed to add kitchen notes");
      }
    } catch (error) {
      console.error("Error adding kitchen notes:", error);
      toast.error("An error occurred while adding kitchen notes");
    } finally {
      setLoading(false);
    }
  };

  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case "pending":
        return "bg-yellow-100 text-yellow-800 border-yellow-300";
      case "confirmed":
        return "bg-blue-100 text-blue-800 border-blue-300";
      case "preparing":
        return "bg-purple-100 text-purple-800 border-purple-300";
      case "ready":
        return "bg-green-100 text-green-800 border-green-300";
      case "completed":
        return "bg-green-100 text-green-800 border-green-300";
      case "cancelled":
        return "bg-red-100 text-red-800 border-red-300";
      case "rejected":
        return "bg-red-100 text-red-800 border-red-300";
      case "modified":
        return "bg-orange-100 text-orange-800 border-orange-300";
      default:
        return "bg-gray-100 text-gray-800 border-gray-300";
    }
  };

  const getPriorityBadgeColor = (priority: string) => {
    switch (priority) {
      case "low":
        return "bg-blue-100 text-blue-800 border-blue-300";
      case "normal":
        return "bg-green-100 text-green-800 border-green-300";
      case "high":
        return "bg-orange-100 text-orange-800 border-orange-300";
      case "urgent":
        return "bg-red-100 text-red-800 border-red-300";
      default:
        return "bg-gray-100 text-gray-800 border-gray-300";
    }
  };

  const getRoleIcon = (role: string) => {
    switch (role) {
      case "chef":
        return <ChefHat className="h-4 w-4" />;
      case "delivery":
        return <Truck className="h-4 w-4" />;
      case "manager":
        return <AlertCircle className="h-4 w-4" />;
      default:
        return <Clock className="h-4 w-4" />;
    }
  };

  const filteredStaff = staffList.filter((staff) => {
    // Filter staff based on selected role
    if (selectedRole === "chef") {
      return staff.role === "chef" || staff.role === "kitchen";
    } else if (selectedRole === "delivery") {
      return staff.role === "delivery" || staff.role === "driver";
    } else {
      return staff.role === "manager" || staff.role === "admin";
    }
  });

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Manage Order #{order.orderNumber}</DialogTitle>
          <DialogDescription>
            Update order status, priority, or assign staff
          </DialogDescription>
        </DialogHeader>

        <div className="flex items-center justify-between mb-4">
          <Badge
            variant="outline"
            className={getStatusBadgeColor(order.status)}
          >
            Status:{" "}
            {order.status.charAt(0).toUpperCase() + order.status.slice(1)}
          </Badge>

          <Badge
            variant="outline"
            className={getPriorityBadgeColor(order.priority || "normal")}
          >
            Priority:{" "}
            {order.priority?.charAt(0).toUpperCase() +
              order.priority?.slice(1) || "Normal"}
          </Badge>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="details">Details</TabsTrigger>
            <TabsTrigger value="status">Status</TabsTrigger>
            <TabsTrigger value="priority">Priority</TabsTrigger>
            <TabsTrigger value="assign">Assign</TabsTrigger>
          </TabsList>

          <TabsContent value="details" className="space-y-4 py-4">
            <div className="space-y-4">
              <div className="border rounded-md p-4 bg-gray-50">
                <h3 className="font-medium mb-2">Order Items</h3>
                <div className="space-y-2">
                  {order.items.map((item, index) => (
                    <div
                      key={index}
                      className="flex justify-between items-center"
                    >
                      <div className="flex items-center">
                        <span className="bg-primary/10 text-primary px-2 py-1 rounded-full text-xs mr-2">
                          {item.quantity}x
                        </span>
                        <span>
                          {item.dishId?.name ||
                            item?.dishName ||
                            "Deleted Dish"}
                        </span>
                      </div>
                      <span>
                        ₹{(item.dishId?.price || 1 * item.quantity).toFixed(2)}
                      </span>
                    </div>
                  ))}
                </div>

                <div className="mt-4 pt-3 border-t border-gray-200 space-y-2">
                  <div className="flex justify-between">
                    <span className="font-medium">Subtotal</span>
                    <span>₹{order.totalAmount.toFixed(2)}</span>
                  </div>

                  {order.couponCode &&
                    order.couponDiscount &&
                    order.couponDiscount > 0 && (
                      <div className="flex justify-between text-green-600">
                        <span className="font-medium">
                          Coupon Discount ({order.couponCode})
                        </span>
                        <span>-₹{order.couponDiscount.toFixed(2)}</span>
                      </div>
                    )}

                  <div className="flex justify-between font-bold">
                    <span>Final Amount</span>
                    <span>
                      ₹{(order.finalAmount || order.totalAmount).toFixed(2)}
                    </span>
                  </div>
                </div>
              </div>

              {order.specialInstructions && (
                <div className="border rounded-md p-4 bg-yellow-50 border-yellow-200">
                  <h3 className="font-medium mb-2 text-yellow-800">
                    Special Instructions
                  </h3>
                  <p className="text-yellow-700">{order.specialInstructions}</p>
                </div>
              )}

              <div className="border rounded-md p-4 bg-gray-50">
                <h3 className="font-medium mb-2">Payment Information</h3>
                <div className="grid grid-cols-2 gap-2">
                  <div>
                    <span className="text-sm text-gray-500">Method:</span>
                    <p className="font-medium">
                      {order.paymentMethod === "cash" ? "Cash" : "Online"}
                    </p>
                  </div>
                  <div>
                    <span className="text-sm text-gray-500">Status:</span>
                    <p
                      className={`font-medium ${
                        order.paymentStatus === "paid"
                          ? "text-green-600"
                          : order.paymentStatus === "requested"
                          ? "text-blue-600"
                          : order.paymentStatus === "failed"
                          ? "text-red-600"
                          : "text-gray-600"
                      }`}
                    >
                      {order.paymentStatus.charAt(0).toUpperCase() +
                        order.paymentStatus.slice(1)}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="status" className="space-y-4 py-4">
            <div className="space-y-4">
              <div>
                <Label className="text-base">Update Order Status</Label>
                <RadioGroup
                  value={selectedStatus}
                  onValueChange={(value: string) =>
                    setSelectedStatus(
                      value as
                        | "cancelled"
                        | "pending"
                        | "confirmed"
                        | "preparing"
                        | "ready"
                        | "completed"
                        | "rejected"
                        | "modified"
                    )
                  }
                  className="mt-2 space-y-2"
                >
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="pending" id="pending" />
                    <Label htmlFor="pending">Pending</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="preparing" id="preparing" />
                    <Label htmlFor="preparing">Preparing</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="ready" id="ready" />
                    <Label htmlFor="ready">Ready</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="completed" id="completed" />
                    <Label htmlFor="completed">Completed</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="modified" id="modified" />
                    <Label htmlFor="modified">Modified</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="cancelled" id="cancelled" />
                    <Label htmlFor="cancelled">Cancelled</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="rejected" id="rejected" />
                    <Label htmlFor="rejected">Rejected</Label>
                  </div>
                </RadioGroup>
              </div>

              {selectedStatus === "cancelled" && (
                <div className="space-y-2">
                  <Label htmlFor="cancellationReason">
                    Cancellation Reason
                  </Label>
                  <Textarea
                    id="cancellationReason"
                    placeholder="Enter reason for cancellation"
                    value={cancellationReason}
                    onChange={(e) => setCancellationReason(e.target.value)}
                  />
                </div>
              )}

              {selectedStatus === "modified" && (
                <div className="space-y-2">
                  <Label htmlFor="modificationReason">
                    Modification Details
                  </Label>
                  <Textarea
                    id="modificationReason"
                    placeholder="Enter details about the modification"
                    value={modificationReason}
                    onChange={(e) => setModificationReason(e.target.value)}
                  />
                </div>
              )}

              <div className="space-y-2">
                <Label htmlFor="kitchenNotes">Kitchen Notes</Label>
                <Textarea
                  id="kitchenNotes"
                  placeholder="Add notes for the kitchen"
                  value={kitchenNotes}
                  onChange={(e) => setKitchenNotes(e.target.value)}
                />
                <Button
                  variant="outline"
                  size="sm"
                  className="mt-2"
                  onClick={handleAddKitchenNotes}
                  disabled={loading || !kitchenNotes.trim()}
                >
                  Save Notes
                </Button>
              </div>
            </div>

            <Button
              onClick={handleUpdateStatus}
              disabled={loading || selectedStatus === order.status}
              className="w-full"
            >
              {loading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Updating...
                </>
              ) : (
                "Update Status"
              )}
            </Button>
          </TabsContent>

          <TabsContent value="priority" className="space-y-4 py-4">
            <div className="space-y-4">
              <div>
                <Label className="text-base">Set Order Priority</Label>
                <RadioGroup
                  value={selectedPriority}
                  onValueChange={(value: string) =>
                    setSelectedPriority(
                      value as "low" | "normal" | "high" | "urgent"
                    )
                  }
                  className="mt-2 space-y-2"
                >
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="low" id="low" />
                    <Label htmlFor="low">Low</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="normal" id="normal" />
                    <Label htmlFor="normal">Normal</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="high" id="high" />
                    <Label htmlFor="high">High</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="urgent" id="urgent" />
                    <Label htmlFor="urgent">Urgent</Label>
                  </div>
                </RadioGroup>
              </div>
            </div>

            <Button
              onClick={handleUpdatePriority}
              disabled={loading || selectedPriority === order.priority}
              className="w-full"
            >
              {loading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Updating...
                </>
              ) : (
                "Update Priority"
              )}
            </Button>
          </TabsContent>

          <TabsContent value="assign" className="space-y-4 py-4">
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="role">Staff Role</Label>
                <Select
                  value={selectedRole}
                  onValueChange={(value) =>
                    setSelectedRole(value as "chef" | "delivery" | "manager")
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select role" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="chef">
                      <div className="flex items-center">
                        <ChefHat className="h-4 w-4 mr-2" />
                        Chef
                      </div>
                    </SelectItem>
                    <SelectItem value="delivery">
                      <div className="flex items-center">
                        <Truck className="h-4 w-4 mr-2" />
                        Delivery
                      </div>
                    </SelectItem>
                    <SelectItem value="manager">
                      <div className="flex items-center">
                        <AlertCircle className="h-4 w-4 mr-2" />
                        Manager
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="staff">Select Staff</Label>
                <Select value={selectedStaff} onValueChange={setSelectedStaff}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select staff member" />
                  </SelectTrigger>
                  <SelectContent>
                    {filteredStaff.length === 0 ? (
                      <SelectItem value="none" disabled>
                        No staff available for this role
                      </SelectItem>
                    ) : (
                      filteredStaff.map((staff) => (
                        <SelectItem key={staff._id} value={staff._id || ""}>
                          {staff.name}
                        </SelectItem>
                      ))
                    )}
                  </SelectContent>
                </Select>
              </div>

              {order.assignedTo && order.assignedTo.userId && (
                <div className="mt-4 p-3 border rounded-md bg-gray-50">
                  <p className="text-sm font-medium">Currently Assigned To:</p>
                  <div className="flex items-center mt-1">
                    {getRoleIcon(order.assignedTo.role || "")}
                    <span className="ml-2 text-sm">
                      {order.assignedTo.userId.name} ({order.assignedTo.role})
                    </span>
                  </div>
                </div>
              )}
            </div>

            <Button
              onClick={handleAssignStaff}
              disabled={loading || !selectedStaff}
              className="w-full"
            >
              {loading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Assigning...
                </>
              ) : (
                "Assign Staff"
              )}
            </Button>
          </TabsContent>
        </Tabs>

        <DialogFooter className="flex justify-between sm:justify-between">
          <Button
            variant="outline"
            onClick={() => downloadOrderBill(order)}
            className="mr-auto"
          >
            <FileDown className="mr-2 h-4 w-4" />
            Download Bill
          </Button>
          <Button variant="outline" onClick={() => handleOpenChange(false)}>
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
