/* eslint-disable @typescript-eslint/no-explicit-any */
import { getAdminFoodChainId } from "@/server";
import { Download, Share, Copy } from "lucide-react";
import React, { useEffect, useState, useRef } from "react";
import QRCode from "react-qr-code";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

export const handleTableQRDownload = (ref: any, tableName: string, outletName: string) => {
  if (!ref.current) return;

  const svg = ref.current;
  const svgData = new XMLSerializer().serializeToString(svg);
  const canvas = document.createElement("canvas");

  const ctx: any = canvas.getContext("2d");
  const img = new Image();
  img.onload = () => {
    // Set canvas dimensions to match the QR code
    canvas.width = img.width;
    canvas.height = img.height;

    // Draw white background to ensure QR code is visible
    ctx.fillStyle = "white";
    ctx.fillRect(0, 0, canvas.width, canvas.height);

    // Draw the QR code
    ctx.drawImage(img, 0, 0);
    // Convert canvas to data URL and trigger download
    const dataUrl = canvas.toDataURL("image/png");
    const a = document.createElement("a");
    a.download = `table-qr-${outletName}-${tableName}.png`;
    a.href = dataUrl;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
  };

  // Load SVG as image
  img.src = "data:image/svg+xml;base64," + btoa(svgData);
};

interface TableQrCodeProps {
  outletId: string;
  tableId: string;
  tableName: string;
  outletName?: string;
  onlyQr?: boolean;
  size?: number;
}

const TableQrCode: React.FC<TableQrCodeProps> = ({
  outletId,
  tableId,
  tableName,
  outletName = "",
  onlyQr = false,
  size = 200,
}) => {
  const [link, setLink] = useState("");
  const qrCodeRef = useRef(null);

  useEffect(() => {
    const chainId = getAdminFoodChainId();
    setLink(
      `${window.location.origin}/chat?${chainId ? `chainId=${chainId}` : ""}${
        outletId ? `&outletId=${outletId}` : ""
      }${tableId ? `&tableId=${tableId}` : ""}`
    );
  }, [outletId, tableId]);

  const handleShare = async () => {
    try {
      if (navigator.share) {
        await navigator.share({
          title: `Table ${tableName} - ${outletName}`,
          text: `Scan this QR code to order from Table ${tableName}`,
          url: link,
        });
      } else {
        // Fallback to clipboard
        await navigator.clipboard.writeText(link);
        // You might want to show a toast notification here
      }
    } catch (error) {
      console.error("Error sharing:", error);
    }
  };

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(link);
      // You might want to show a toast notification here
    } catch (error) {
      console.error("Error copying to clipboard:", error);
    }
  };

  if (onlyQr) {
    return (
      <QRCode
        ref={qrCodeRef}
        value={link}
        size={size}
        className="animate-pulse gradient-flow"
      />
    );
  }

  return (
    <Card className="w-full max-w-md">
      <CardHeader>
        <CardTitle className="text-center">
          Table {tableName} QR Code
        </CardTitle>
        {outletName && (
          <p className="text-sm text-gray-500 text-center">{outletName}</p>
        )}
      </CardHeader>
      <CardContent className="flex flex-col items-center space-y-4">
        <div className="p-4 bg-white rounded-lg shadow-sm border">
          <QRCode ref={qrCodeRef} value={link} size={size} />
        </div>
        
        <p className="text-xs text-gray-500 text-center">
          Scan this QR code to order from Table {tableName}
        </p>

        <div className="flex gap-2 justify-center w-full">
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleTableQRDownload(qrCodeRef, tableName, outletName)}
          >
            <Download className="h-4 w-4 mr-1" />
            Download
          </Button>
          
          <Button variant="outline" size="sm" onClick={handleShare}>
            <Share className="h-4 w-4 mr-1" />
            Share
          </Button>
          
          <Button variant="outline" size="sm" onClick={handleCopy}>
            <Copy className="h-4 w-4 mr-1" />
            Copy Link
          </Button>
        </div>

        <div className="text-xs text-gray-400 text-center">
          Powered by Butler AI
        </div>
      </CardContent>
    </Card>
  );
};

export default TableQrCode;
