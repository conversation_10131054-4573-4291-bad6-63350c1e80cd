"use client";

import React, { useState, useEffect } from "react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { getOutletTables, type TableInfo } from "@/utils/tableUtils";

interface TableSelectorProps {
  outletId: string;
  selectedTableId?: string;
  selectedTableNumber?: string;
  onTableSelect: (tableId: string | null, tableName: string) => void;
  onTableNumberChange: (tableNumber: string) => void;
  disabled?: boolean;
}

const TableSelector: React.FC<TableSelectorProps> = ({
  outletId,
  selectedTableId,
  selectedTableNumber,
  onTableSelect,
  onTableNumberChange,
  disabled = false,
}) => {
  const [tables, setTables] = useState<TableInfo[]>([]);
  const [loading, setLoading] = useState(false);
  const [useExistingTable, setUseExistingTable] = useState(!!selectedTableId);

  useEffect(() => {
    const fetchTables = async () => {
      if (!outletId) return;

      setLoading(true);
      try {
        const tablesData = await getOutletTables(outletId);
        setTables(tablesData);
        if (disabled && selectedTableId) {
          const tableNumber = tablesData.find(
            (table) => table._id === selectedTableId
          )?.name;
          onTableSelect(selectedTableId, tableNumber || "");
          setUseExistingTable(true);
        }

        // Default to existing tables if available and no table is already selected
        if (tablesData.length > 0 && !selectedTableId) {
          setUseExistingTable(true);
        }
      } catch (error) {
        console.error("Error fetching tables:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchTables();
  }, [outletId, selectedTableId]);

  const handleTableModeChange = (mode: string) => {
    const isExisting = mode === "existing";
    setUseExistingTable(isExisting);

    if (isExisting) {
      // Clear manual table number when switching to existing table
      onTableNumberChange("");
    } else {
      // Clear selected table when switching to manual entry
      onTableSelect(null, "");
    }
  };

  const handleExistingTableSelect = (tableId: string) => {
    const selectedTable = tables.find((table) => table._id === tableId);
    if (selectedTable) {
      onTableSelect(tableId, selectedTable.name);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "available":
        return "bg-green-100 text-green-800";
      case "occupied":
        return "bg-red-100 text-red-800";
      case "reserved":
        return "bg-blue-100 text-blue-800";
      case "cleaning":
        return "bg-yellow-100 text-yellow-800";
      case "maintenance":
        return "bg-gray-100 text-gray-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const formatTableName = (table: TableInfo): string => {
    let displayName = table.name;

    if (table.location) {
      displayName += ` (${table.location})`;
    }

    displayName += ` - ${table.capacity} seats`;

    return displayName;
  };

  return (
    <div className="space-y-4">
      <div>
        <Label className="text-sm font-medium">Table Selection</Label>
        <Select
          value={useExistingTable ? "existing" : "manual"}
          onValueChange={handleTableModeChange}
          disabled={disabled}
        >
          <SelectTrigger className="mt-1">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="existing">
              Select from existing tables
            </SelectItem>
            <SelectItem value="manual">Enter table number manually</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {useExistingTable ? (
        <div>
          <Label className="text-sm font-medium">Available Tables</Label>
          {loading ? (
            <div className="mt-1 p-3 border rounded-md text-sm text-gray-500">
              Loading tables...
            </div>
          ) : tables.length > 0 ? (
            <Select
              value={selectedTableId || ""}
              onValueChange={handleExistingTableSelect}
              disabled={disabled}
            >
              <SelectTrigger className="mt-1">
                <SelectValue placeholder="Select a table" />
              </SelectTrigger>
              <SelectContent>
                {tables.map((table) => (
                  <SelectItem key={table._id} value={table._id}>
                    <div className="flex items-center justify-between w-full">
                      <div className="flex items-center space-x-2">
                        <span>{formatTableName(table)}</span>
                        <Badge className={getStatusColor(table.status)}>
                          {table.status}
                        </Badge>
                      </div>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          ) : (
            <div className="mt-1 p-3 border rounded-md text-sm text-gray-500">
              No tables found for this outlet
            </div>
          )}

          {selectedTableId && (
            <div className="mt-2 p-3 bg-blue-50 border border-blue-200 rounded-md">
              <div className="text-sm text-blue-800">
                <strong>Selected Table:</strong>{" "}
                {tables.find((t) => t._id === selectedTableId)?.name}
                {tables.find((t) => t._id === selectedTableId)?.location &&
                  ` (${
                    tables.find((t) => t._id === selectedTableId)?.location
                  })`}
              </div>
              <div className="text-xs text-blue-600 mt-1">
                This order will be tracked for analytics and table management.
              </div>
            </div>
          )}
        </div>
      ) : (
        <div>
          <Label htmlFor="tableNumber" className="text-sm font-medium">
            Table Number
          </Label>
          <Input
            id="tableNumber"
            type="text"
            placeholder="Enter table number (e.g., T1, Table 5)"
            value={selectedTableNumber || ""}
            onChange={(e) => onTableNumberChange(e.target.value)}
            disabled={disabled}
            className="mt-1"
          />
          {selectedTableNumber && (
            <div className="mt-2 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
              <div className="text-sm text-yellow-800">
                <strong>Manual Entry:</strong> {selectedTableNumber}
              </div>
              <div className="text-xs text-yellow-600 mt-1">
                This table won&apos;t be tracked for analytics unless it matches an
                existing table.
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default TableSelector;
