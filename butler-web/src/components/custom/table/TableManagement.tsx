"use client";

import React, { useState } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { 
  Plus, 
  Edit, 
  Trash2, 
  MoreVertical, 
  QrCode, 
  Users,
  MapPin,
  Clock,
  AlertCircle
} from "lucide-react";
import { toast } from "sonner";
import { Table as TableType } from "@/app/type";
import CreateTableDialog from "./CreateTableDialog";
import EditTableDialog from "./EditTableDialog";
import TableQrCode from "./TableQrCode";

interface TableManagementProps {
  outletId: string;
  outletName: string;
  tables: TableType[];
  onTablesUpdate: () => void;
}

const TableManagement: React.FC<TableManagementProps> = ({
  outletId,
  outletName,
  tables,
  onTablesUpdate,
}) => {
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [showQrDialog, setShowQrDialog] = useState(false);
  const [selectedTable, setSelectedTable] = useState<TableType | null>(null);
  const [loading, setLoading] = useState(false);

  const getStatusColor = (status?: string) => {
    switch (status) {
      case "available":
        return "bg-green-100 text-green-800";
      case "occupied":
        return "bg-red-100 text-red-800";
      case "reserved":
        return "bg-blue-100 text-blue-800";
      case "cleaning":
        return "bg-yellow-100 text-yellow-800";
      case "maintenance":
        return "bg-gray-100 text-gray-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getStatusIcon = (status?: string) => {
    switch (status) {
      case "occupied":
        return <Users className="h-3 w-3" />;
      case "reserved":
        return <Clock className="h-3 w-3" />;
      case "cleaning":
      case "maintenance":
        return <AlertCircle className="h-3 w-3" />;
      default:
        return null;
    }
  };

  const handleToggleAvailability = async (table: TableType) => {
    if (!table._id) return;
    
    setLoading(true);
    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_BASE_URL}/api/v1/admin/outlets/${outletId}/tables/${table._id}/availability`,
        {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${localStorage.getItem("auth-token")}`,
          },
        }
      );

      const data = await response.json();

      if (data.success) {
        toast.success(`Table ${table.isAvailable ? 'disabled' : 'enabled'} successfully`);
        onTablesUpdate();
      } else {
        toast.error(data.message || "Failed to update table availability");
      }
    } catch (error) {
      console.error("Error toggling table availability:", error);
      toast.error("Failed to update table availability");
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteTable = async (table: TableType) => {
    if (!table._id) return;
    
    if (!confirm(`Are you sure you want to delete table "${table.name}"?`)) {
      return;
    }

    setLoading(true);
    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_BASE_URL}/api/v1/admin/outlets/${outletId}/tables/${table._id}`,
        {
          method: "DELETE",
          headers: {
            Authorization: `Bearer ${localStorage.getItem("auth-token")}`,
          },
        }
      );

      const data = await response.json();

      if (data.success) {
        toast.success("Table deleted successfully");
        onTablesUpdate();
      } else {
        toast.error(data.message || "Failed to delete table");
      }
    } catch (error) {
      console.error("Error deleting table:", error);
      toast.error("Failed to delete table");
    } finally {
      setLoading(false);
    }
  };

  const handleEditTable = (table: TableType) => {
    setSelectedTable(table);
    setShowEditDialog(true);
  };

  const handleShowQr = (table: TableType) => {
    setSelectedTable(table);
    setShowQrDialog(true);
  };

  const handleQrDialogChange = (open: boolean) => {
    setShowQrDialog(open);
    if (!open) {
      setSelectedTable(null);
    }
  };

  return (
    <>
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <div>
              <CardTitle className="text-lg font-medium">
                Table Management
              </CardTitle>
              <p className="text-sm text-gray-500 mt-1">
                Manage tables, availability, and QR codes
              </p>
            </div>
            <Button onClick={() => setShowCreateDialog(true)}>
              <Plus className="h-4 w-4 mr-2" />
              Add Table
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {tables.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-gray-500 mb-4">No tables found</p>
              <Button onClick={() => setShowCreateDialog(true)}>
                <Plus className="h-4 w-4 mr-2" />
                Create Your First Table
              </Button>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Name</TableHead>
                  <TableHead>Capacity</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Location</TableHead>
                  <TableHead>Available</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {tables.map((table) => (
                  <TableRow key={table._id || table.name}>
                    <TableCell className="font-medium">{table.name}</TableCell>
                    <TableCell>
                      <div className="flex items-center">
                        <Users className="h-4 w-4 mr-1 text-gray-500" />
                        {table.capacity}
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge className={getStatusColor(table.status)}>
                        <div className="flex items-center gap-1">
                          {getStatusIcon(table.status)}
                          {table.status || "available"}
                        </div>
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center text-sm text-gray-500">
                        {table.location ? (
                          <>
                            <MapPin className="h-3 w-3 mr-1" />
                            {table.location}
                          </>
                        ) : (
                          "-"
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <Switch
                        checked={table.isAvailable}
                        onCheckedChange={() => handleToggleAvailability(table)}
                        disabled={loading}
                      />
                    </TableCell>
                    <TableCell>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="icon" className="h-8 w-8">
                            <MoreVertical className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem onClick={() => handleEditTable(table)}>
                            <Edit className="h-4 w-4 mr-2" />
                            Edit
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => handleShowQr(table)}>
                            <QrCode className="h-4 w-4 mr-2" />
                            QR Code
                          </DropdownMenuItem>
                          <DropdownMenuItem 
                            onClick={() => handleDeleteTable(table)}
                            className="text-red-600"
                          >
                            <Trash2 className="h-4 w-4 mr-2" />
                            Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      <CreateTableDialog
        open={showCreateDialog}
        onOpenChange={setShowCreateDialog}
        onSuccess={onTablesUpdate}
        outletId={outletId}
      />

      <EditTableDialog
        open={showEditDialog}
        onOpenChange={(open) => {
          setShowEditDialog(open);
          if (!open) {
            setSelectedTable(null);
          }
        }}
        onSuccess={onTablesUpdate}
        outletId={outletId}
        table={selectedTable}
      />

      <Dialog open={showQrDialog} onOpenChange={handleQrDialogChange}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Table QR Code</DialogTitle>
          </DialogHeader>
          {selectedTable && (
            <div className="flex justify-center">
              <TableQrCode
                outletId={outletId}
                tableId={selectedTable._id || ""}
                tableName={selectedTable.name}
                outletName={outletName}
                onlyQr={false}
              />
            </div>
          )}
        </DialogContent>
      </Dialog>
    </>
  );
};

export default TableManagement;
