"use client";

import React, { useState, useEffect } from "react";
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  BarC<PERSON>,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Pie<PERSON>hart,
  Pie,
  Cell,
} from "recharts";
import {
  Users,
  TrendingUp,
  DollarSign,
  Activity,
  MapPin,
} from "lucide-react";
import { toast } from "sonner";

interface TableAnalyticsProps {
  outletId: string;
}

interface TableUtilization {
  tableId: string;
  tableName: string;
  capacity: number;
  location?: string;
  status: string;
  totalOrders: number;
  totalRevenue: number;
  avgOrderValue: number;
  completedOrders: number;
  lastOrder?: string;
  utilizationRate: number;
  revenuePerSeat: number;
}

interface AnalyticsData {
  overview: {
    totalTables: number;
    occupiedTables: number;
    availableTables: number;
    maintenanceTables: number;
    occupancyRate: number;
    totalOrders: number;
    totalRevenue: number;
    avgRevenuePerTable: number;
  };
  tableUtilization: TableUtilization[];
  peakHours: Array<{ _id: number; orderCount: number; revenue: number }>;
  period: {
    days: number;
    startDate: string;
    endDate: string;
  };
}

const TableAnalytics: React.FC<TableAnalyticsProps> = ({ outletId }) => {
  const [analytics, setAnalytics] = useState<AnalyticsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [period, setPeriod] = useState("30");

  const fetchAnalytics = async () => {
    setLoading(true);
    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_BASE_URL}/api/v1/admin/outlets/${outletId}/tables-analytics?days=${period}`,
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem("auth-token")}`,
          },
        }
      );

      const data = await response.json();

      if (data.success) {
        setAnalytics(data.data);
      } else {
        toast.error(data.message || "Failed to fetch table analytics");
      }
    } catch (error) {
      console.error("Error fetching table analytics:", error);
      toast.error("Failed to fetch table analytics");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchAnalytics();
  }, [outletId, period]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case "available":
        return "bg-green-100 text-green-800";
      case "occupied":
        return "bg-red-100 text-red-800";
      case "reserved":
        return "bg-blue-100 text-blue-800";
      case "cleaning":
        return "bg-yellow-100 text-yellow-800";
      case "maintenance":
        return "bg-gray-100 text-gray-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-IN", {
      style: "currency",
      currency: "INR",
    }).format(amount);
  };

  const formatHour = (hour: number) => {
    return `${hour.toString().padStart(2, "0")}:00`;
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardContent className="p-6">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-8 bg-gray-200 rounded w-1/2"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (!analytics) {
    return (
      <Card>
        <CardContent className="p-6 text-center">
          <p className="text-gray-500">No analytics data available</p>
        </CardContent>
      </Card>
    );
  }

  const pieData = [
    { name: "Available", value: analytics.overview.availableTables, color: "#10b981" },
    { name: "Occupied", value: analytics.overview.occupiedTables, color: "#ef4444" },
    { name: "Maintenance", value: analytics.overview.maintenanceTables, color: "#6b7280" },
  ];

  return (
    <div className="space-y-6">
      {/* Period Selector */}
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Table Analytics</h2>
        <Select value={period} onValueChange={setPeriod}>
          <SelectTrigger className="w-40">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="7">Last 7 days</SelectItem>
            <SelectItem value="30">Last 30 days</SelectItem>
            <SelectItem value="90">Last 90 days</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Tables</p>
                <p className="text-2xl font-bold">{analytics.overview.totalTables}</p>
              </div>
              <Users className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Occupancy Rate</p>
                <p className="text-2xl font-bold">
                  {analytics.overview.occupancyRate.toFixed(1)}%
                </p>
              </div>
              <Activity className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Orders</p>
                <p className="text-2xl font-bold">{analytics.overview.totalOrders}</p>
              </div>
              <TrendingUp className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Revenue</p>
                <p className="text-2xl font-bold">
                  {formatCurrency(analytics.overview.totalRevenue)}
                </p>
              </div>
              <DollarSign className="h-8 w-8 text-yellow-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Table Status Distribution */}
        <Card>
          <CardHeader>
            <CardTitle>Table Status Distribution</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={pieData}
                  cx="50%"
                  cy="50%"
                  outerRadius={80}
                  dataKey="value"
                  label={({ name, value }) => `${name}: ${value}`}
                >
                  {pieData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Peak Hours */}
        <Card>
          <CardHeader>
            <CardTitle>Peak Hours Analysis</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={analytics.peakHours}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis
                  dataKey="_id"
                  tickFormatter={formatHour}
                />
                <YAxis />
                <Tooltip
                  labelFormatter={(hour) => `Time: ${formatHour(hour as number)}`}
                  formatter={(value, name) => [
                    name === "orderCount" ? value : formatCurrency(value as number),
                    name === "orderCount" ? "Orders" : "Revenue"
                  ]}
                />
                <Bar dataKey="orderCount" fill="#3b82f6" name="orderCount" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* Table Utilization Details */}
      <Card>
        <CardHeader>
          <CardTitle>Table Utilization Details</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Table</TableHead>
                <TableHead>Capacity</TableHead>
                <TableHead>Location</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Orders</TableHead>
                <TableHead>Revenue</TableHead>
                <TableHead>Utilization</TableHead>
                <TableHead>Revenue/Seat</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {analytics.tableUtilization
                .sort((a, b) => b.totalRevenue - a.totalRevenue)
                .map((table) => (
                  <TableRow key={table.tableId}>
                    <TableCell className="font-medium">{table.tableName}</TableCell>
                    <TableCell>
                      <div className="flex items-center">
                        <Users className="h-4 w-4 mr-1 text-gray-500" />
                        {table.capacity}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center text-sm text-gray-500">
                        {table.location ? (
                          <>
                            <MapPin className="h-3 w-3 mr-1" />
                            {table.location}
                          </>
                        ) : (
                          "-"
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge className={getStatusColor(table.status)}>
                        {table.status}
                      </Badge>
                    </TableCell>
                    <TableCell>{table.totalOrders}</TableCell>
                    <TableCell>{formatCurrency(table.totalRevenue)}</TableCell>
                    <TableCell>
                      <div className="flex items-center">
                        <div className="w-16 bg-gray-200 rounded-full h-2 mr-2">
                          <div
                            className="bg-blue-600 h-2 rounded-full"
                            style={{ width: `${Math.min(table.utilizationRate, 100)}%` }}
                          ></div>
                        </div>
                        <span className="text-sm">
                          {table.utilizationRate.toFixed(1)}%
                        </span>
                      </div>
                    </TableCell>
                    <TableCell>{formatCurrency(table.revenuePerSeat)}</TableCell>
                  </TableRow>
                ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
};

export default TableAnalytics;
