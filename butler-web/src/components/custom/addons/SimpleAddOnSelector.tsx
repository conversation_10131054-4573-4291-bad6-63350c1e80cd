"use client";

import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Plus, Minus } from "lucide-react";
import { AddOn, SelectedAddOn } from "@/app/type";

interface SimpleAddOnSelectorProps {
  availableAddOns: AddOn[];
  selectedAddOns: SelectedAddOn[];
  onAddOnChange: (addOns: SelectedAddOn[]) => void;
  disabled?: boolean;
  showPrices?: boolean;
  compact?: boolean;
}

const SimpleAddOnSelector: React.FC<SimpleAddOnSelectorProps> = ({
  availableAddOns,
  selectedAddOns,
  onAddOnChange,
  disabled = false,
  showPrices = true,
  compact = false,
}) => {
  const getSelectedQuantity = (addOnId: string): number => {
    const selected = selectedAddOns.find(
      (selected) => selected.addOnId === addOnId
    );
    return selected ? selected.quantity : 0;
  };

  const isSelected = (addOnId: string): boolean => {
    return selectedAddOns.some((selected) => selected.addOnId === addOnId);
  };

  const handleAddOnToggle = (addOn: AddOn) => {
    const existingIndex = selectedAddOns.findIndex(
      (selected) => selected.addOnId === addOn._id
    );

    let newSelectedAddOns: SelectedAddOn[];

    if (existingIndex >= 0) {
      // Remove the addon
      newSelectedAddOns = selectedAddOns.filter(
        (selected) => selected.addOnId !== addOn._id
      );
    } else {
      // Add the addon with quantity 1
      newSelectedAddOns = [
        ...selectedAddOns,
        {
          addOnId: addOn._id,
          name: addOn.name,
          price: addOn.price,
          quantity: 1,
        },
      ];
    }

    onAddOnChange(newSelectedAddOns);
  };

  const handleQuantityChange = (addOnId: string, newQuantity: number) => {
    let newSelectedAddOns: SelectedAddOn[];

    if (newQuantity <= 0) {
      // Remove the addon if quantity is 0 or less
      newSelectedAddOns = selectedAddOns.filter(
        (selected) => selected.addOnId !== addOnId
      );
    } else {
      newSelectedAddOns = selectedAddOns.map((selected) =>
        selected.addOnId === addOnId
          ? { ...selected, quantity: newQuantity }
          : selected
      );
    }

    onAddOnChange(newSelectedAddOns);
  };

  const totalAddOnPrice = selectedAddOns.reduce(
    (total, addOn) => total + addOn.price * addOn.quantity,
    0
  );

  if (!availableAddOns || availableAddOns.length === 0) {
    return null;
  }

  return (
    <div className="space-y-3">
      <div className="flex justify-between items-center">
        <h4 className={`font-medium ${compact ? "text-sm" : "text-base"}`}>
          Add-ons
        </h4>
        {showPrices && selectedAddOns.length > 0 && (
          <Badge variant="outline" className="text-green-600 border-green-600">
            +₹{totalAddOnPrice.toFixed(2)}
          </Badge>
        )}
      </div>

      <div className={`space-y-2 ${compact ? "space-y-1" : "space-y-2"}`}>
        {availableAddOns.map((addOn) => {
          const quantity = getSelectedQuantity(addOn._id);
          const selected = isSelected(addOn._id);

          return (
            <div
              key={addOn._id}
              className={`flex items-center justify-between p-2 border rounded-md transition-colors ${
                selected
                  ? "border-green-500 bg-green-50"
                  : "border-gray-200 hover:border-gray-300"
              } ${disabled ? "opacity-50 cursor-not-allowed" : ""}`}
            >
              <div className="flex-1">
                <div className={`flex items-center gap-2 ${compact ? "text-sm" : ""}`}>
                  <span className="font-medium">{addOn.name}</span>
                  {showPrices && (
                    <Badge variant="outline" className="text-xs">
                      ₹{addOn.price}
                    </Badge>
                  )}
                </div>
                {addOn.description && !compact && (
                  <p className="text-xs text-gray-500 mt-1">{addOn.description}</p>
                )}
              </div>

              <div className="flex items-center gap-2">
                {selected ? (
                  <div className="flex items-center gap-2">
                    <Button
                      variant="outline"
                      size="icon"
                      className="h-6 w-6"
                      onClick={() => handleQuantityChange(addOn._id, quantity - 1)}
                      disabled={disabled}
                    >
                      <Minus className="h-3 w-3" />
                    </Button>
                    <span className="text-sm font-medium w-6 text-center">
                      {quantity}
                    </span>
                    <Button
                      variant="outline"
                      size="icon"
                      className="h-6 w-6"
                      onClick={() => handleQuantityChange(addOn._id, quantity + 1)}
                      disabled={disabled}
                    >
                      <Plus className="h-3 w-3" />
                    </Button>
                  </div>
                ) : (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleAddOnToggle(addOn)}
                    disabled={disabled}
                    className="text-xs"
                  >
                    <Plus className="h-3 w-3 mr-1" />
                    Add
                  </Button>
                )}
              </div>
            </div>
          );
        })}
      </div>

      {selectedAddOns.length > 0 && (
        <div className="text-xs text-gray-600">
          {selectedAddOns.length} add-on{selectedAddOns.length > 1 ? "s" : ""} selected
        </div>
      )}
    </div>
  );
};

export default SimpleAddOnSelector;
