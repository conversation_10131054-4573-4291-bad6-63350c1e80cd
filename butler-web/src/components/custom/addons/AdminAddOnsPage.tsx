"use client";
import React, { useEffect, useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { toast } from "sonner";
import { createAddOn, listAddOns, updateAddOn, deleteAddOn, setDishEnabledAddOns, getAllDishes, importFromInventory, getAvailableInventoryItems, linkAddOnToInventory, unlinkAddOnFromInventory } from "@/server/admin";
import { Dish } from "@/app/type";

type AddOn = {
  _id: string;
  name: string;
  price: number;
  description?: string;
  type?: string;
  inventoryItem?: { _id: string; name: string; quantity: number; unit: string; };
  inventoryQuantityPerUnit?: number;
  trackInventory?: boolean;
};

type InventoryItem = {
  _id: string;
  name: string;
  description?: string;
  category: string;
  unit: string;
  quantity: number;
};

export default function AdminAddOnsPage() {
  const [addOns, setAddOns] = useState<AddOn[]>([]);
  const [loading, setLoading] = useState(false);
  const [form, setForm] = useState({ name: "", description: "", price: "", type: "" });
  const [editing, setEditing] = useState<AddOn | null>(null);
  const [dishes, setDishes] = useState<Dish[]>([]);
  const [selectedDishId, setSelectedDishId] = useState<string>("");
  const [selectedAddOnIds, setSelectedAddOnIds] = useState<string[]>([]);

  // Inventory integration state
  const [availableInventoryItems, setAvailableInventoryItems] = useState<InventoryItem[]>([]);
  const [showInventoryImport, setShowInventoryImport] = useState(false);
  const [importForm, setImportForm] = useState({ inventoryItemId: "", price: "", inventoryQuantityPerUnit: "1", type: "" });
  const [linkingAddOn, setLinkingAddOn] = useState<AddOn | null>(null);
  const [linkForm, setLinkForm] = useState({ inventoryItemId: "", inventoryQuantityPerUnit: "1", trackInventory: true });

  const fetchAll = async () => {
    setLoading(true);
    try {
      const res = await listAddOns();
      if (res.success) setAddOns(res.data);
      const dishesRes = await getAllDishes();
      if (dishesRes.success) setDishes(dishesRes.data);
      const inventoryRes = await getAvailableInventoryItems();
      if (inventoryRes.success) setAvailableInventoryItems(inventoryRes.data);
    } catch {
      toast.error("Failed to load add-ons or dishes");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchAll();
  }, []);

  const handleSave = async () => {
    if (!form.name || !form.price) {
      toast.error("Name and price are required");
      return;
    }
    const payload = { name: form.name, description: form.description, price: Number(form.price), type: form.type };
    const res = editing ? await updateAddOn(editing._id, payload) : await createAddOn(payload);
    if (res.success) {
      toast.success(editing ? "Add-on updated" : "Add-on created");
      setForm({ name: "", description: "", price: "", type: "" });
      setEditing(null);
      fetchAll();
    } else toast.error(res.message || "Failed to save add-on");
  };

  const handleDelete = async (id: string) => {
    const res = await deleteAddOn(id);
    if (res.success) {
      toast.success("Add-on deleted");
      fetchAll();
    } else toast.error(res.message || "Delete failed");
  };

  const handleAssignToDish = async () => {
    if (!selectedDishId) return toast.error("Select a dish first");
    const res = await setDishEnabledAddOns(selectedDishId, selectedAddOnIds);
    if (res.success) {
      toast.success("Assigned add-ons to dish");
      fetchAll();
    } else toast.error(res.message || "Failed to assign add-ons");
  };

  const handleImportFromInventory = async () => {
    if (!importForm.inventoryItemId || !importForm.price) {
      return toast.error("Please select an inventory item and set a price");
    }
    const res = await importFromInventory({
      inventoryItemId: importForm.inventoryItemId,
      price: Number(importForm.price),
      inventoryQuantityPerUnit: Number(importForm.inventoryQuantityPerUnit),
      type: importForm.type || "imported"
    });
    if (res.success) {
      toast.success("Successfully imported inventory item as add-on");
      setImportForm({ inventoryItemId: "", price: "", inventoryQuantityPerUnit: "1", type: "" });
      setShowInventoryImport(false);
      fetchAll();
    } else {
      toast.error(res.message || "Failed to import from inventory");
    }
  };

  const handleLinkToInventory = async () => {
    if (!linkingAddOn || !linkForm.inventoryItemId) {
      return toast.error("Please select an inventory item");
    }
    const res = await linkAddOnToInventory(linkingAddOn._id, {
      inventoryItemId: linkForm.inventoryItemId,
      inventoryQuantityPerUnit: Number(linkForm.inventoryQuantityPerUnit),
      trackInventory: linkForm.trackInventory
    });
    if (res.success) {
      toast.success("Successfully linked add-on to inventory");
      setLinkingAddOn(null);
      setLinkForm({ inventoryItemId: "", inventoryQuantityPerUnit: "1", trackInventory: true });
      fetchAll();
    } else {
      toast.error(res.message || "Failed to link to inventory");
    }
  };

  const handleUnlinkFromInventory = async (addOnId: string) => {
    const res = await unlinkAddOnFromInventory(addOnId);
    if (res.success) {
      toast.success("Successfully unlinked add-on from inventory");
      fetchAll();
    } else {
      toast.error(res.message || "Failed to unlink from inventory");
    }
  };

  return (
    <div className="container mx-auto p-4">
      <div className="flex justify-between items-center mb-4">
        <h1 className="text-2xl font-bold">Add-ons</h1>
        <Button onClick={() => setShowInventoryImport(true)} variant="outline">
          Import from Inventory
        </Button>
      </div>
      <Separator className="my-4" />
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardContent className="p-4 space-y-3">
            <h2 className="font-semibold text-lg">{editing ? "Edit Add-on" : "Create Add-on"}</h2>
            <div className="grid gap-2">
              <Label>Name</Label>
              <Input value={form.name} onChange={(e) => setForm((p) => ({ ...p, name: e.target.value }))} />
            </div>
            <div className="grid gap-2">
              <Label>Description</Label>
              <Input value={form.description} onChange={(e) => setForm((p) => ({ ...p, description: e.target.value }))} />
            </div>
            <div className="grid gap-2">
              <Label>Price</Label>
              <Input type="number" value={form.price} onChange={(e) => setForm((p) => ({ ...p, price: e.target.value }))} />
            </div>
            <div className="grid gap-2">
              <Label>Type (optional)</Label>
              <Input value={form.type} onChange={(e) => setForm((p) => ({ ...p, type: e.target.value }))} />
            </div>
            <div className="flex gap-2">
              <Button onClick={handleSave} disabled={loading}>{editing ? "Update" : "Create"}</Button>
              {editing && <Button variant="outline" onClick={() => { setEditing(null); setForm({ name: "", description: "", price: "", type: "" }); }}>Cancel</Button>}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4 space-y-3">
            <h2 className="font-semibold text-lg">Existing Add-ons</h2>
            <div className="space-y-2 max-h-72 overflow-auto">
              {addOns.map((ao) => (
                <div key={ao._id} className="border rounded p-3">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="font-medium">{ao.name} <span className="text-sm text-gray-500">₹{ao.price}</span></div>
                      {ao.description && <div className="text-sm text-gray-600">{ao.description}</div>}
                      {ao.inventoryItem && (
                        <div className="text-xs text-green-600 mt-1">
                          📦 Linked to: {ao.inventoryItem.name} ({ao.inventoryItem.quantity} {ao.inventoryItem.unit})
                          {ao.inventoryQuantityPerUnit && ` - Uses ${ao.inventoryQuantityPerUnit} per unit`}
                        </div>
                      )}
                    </div>
                    <div className="flex gap-1 flex-wrap">
                      <Button size="sm" variant="outline" onClick={() => { setEditing(ao); setForm({ name: ao.name, description: ao.description || "", price: String(ao.price), type: ao.type || "" }); }}>Edit</Button>
                      {ao.inventoryItem ? (
                        <Button size="sm" variant="outline" onClick={() => handleUnlinkFromInventory(ao._id)}>Unlink</Button>
                      ) : (
                        <Button size="sm" variant="outline" onClick={() => { setLinkingAddOn(ao); setLinkForm({ inventoryItemId: "", inventoryQuantityPerUnit: "1", trackInventory: true }); }}>Link</Button>
                      )}
                      <Button size="sm" variant="destructive" onClick={() => handleDelete(ao._id)}>Delete</Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      <Separator className="my-6" />

      <Card>
        <CardContent className="p-4 space-y-3">
          <h2 className="font-semibold text-lg">Assign Add-ons to Dish</h2>
          <div className="grid gap-2">
            <Label>Select Dish</Label>
            <select className="border rounded p-2" value={selectedDishId} onChange={(e) => {
              const id = e.target.value; setSelectedDishId(id);
              const dish = dishes.find(d => d._id === id);
              setSelectedAddOnIds((dish?.enabledAddOns || []).map((x: { _id: string } | string) => (typeof x === 'string' ? x : x._id)));
            }}>
              <option value="">-- Select --</option>
              {dishes.map((d) => (
                <option key={d._id} value={d._id}>{d.name}</option>
              ))}
            </select>
          </div>
          <div className="grid gap-2">
            <Label>Enable Add-ons</Label>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-2 max-h-56 overflow-auto">
              {addOns.map((ao) => {
                const checked = selectedAddOnIds.includes(ao._id);
                return (
                  <label key={ao._id} className="flex items-center gap-2 border rounded p-2">
                    <input type="checkbox" checked={checked} onChange={(e) => {
                      setSelectedAddOnIds((prev) => e.target.checked ? [...prev, ao._id] : prev.filter(id => id !== ao._id));
                    }} />
                    <span>{ao.name} <span className="text-sm text-gray-500">₹{ao.price}</span></span>
                  </label>
                );
              })}
            </div>
          </div>
          <Button onClick={handleAssignToDish} disabled={!selectedDishId}>Assign</Button>
        </CardContent>
      </Card>

      {/* Inventory Import Dialog */}
      {showInventoryImport && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <Card className="w-full max-w-md">
            <CardContent className="p-4 space-y-3">
              <h2 className="font-semibold text-lg">Import from Inventory</h2>
              <div className="grid gap-2">
                <Label>Select Inventory Item</Label>
                <select
                  className="border rounded p-2"
                  value={importForm.inventoryItemId}
                  onChange={(e) => setImportForm(prev => ({ ...prev, inventoryItemId: e.target.value }))}
                >
                  <option value="">-- Select Inventory Item --</option>
                  {availableInventoryItems.map((item) => (
                    <option key={item._id} value={item._id}>
                      {item.name} ({item.quantity} {item.unit}) - {item.category}
                    </option>
                  ))}
                </select>
              </div>
              <div className="grid gap-2">
                <Label>Add-on Price (₹)</Label>
                <Input
                  type="number"
                  value={importForm.price}
                  onChange={(e) => setImportForm(prev => ({ ...prev, price: e.target.value }))}
                  placeholder="Enter price for this add-on"
                />
              </div>
              <div className="grid gap-2">
                <Label>Inventory Quantity per Add-on Unit</Label>
                <Input
                  type="number"
                  value={importForm.inventoryQuantityPerUnit}
                  onChange={(e) => setImportForm(prev => ({ ...prev, inventoryQuantityPerUnit: e.target.value }))}
                  placeholder="How much inventory is used per add-on"
                />
              </div>
              <div className="grid gap-2">
                <Label>Type (optional)</Label>
                <Input
                  value={importForm.type}
                  onChange={(e) => setImportForm(prev => ({ ...prev, type: e.target.value }))}
                  placeholder="e.g., sauce, topping"
                />
              </div>
              <div className="flex gap-2">
                <Button onClick={handleImportFromInventory} disabled={loading}>Import</Button>
                <Button variant="outline" onClick={() => setShowInventoryImport(false)}>Cancel</Button>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Link to Inventory Dialog */}
      {linkingAddOn && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <Card className="w-full max-w-md">
            <CardContent className="p-4 space-y-3">
              <h2 className="font-semibold text-lg">Link &quot;{linkingAddOn.name}&quot; to Inventory</h2>
              <div className="grid gap-2">
                <Label>Select Inventory Item</Label>
                <select
                  className="border rounded p-2"
                  value={linkForm.inventoryItemId}
                  onChange={(e) => setLinkForm(prev => ({ ...prev, inventoryItemId: e.target.value }))}
                >
                  <option value="">-- Select Inventory Item --</option>
                  {availableInventoryItems.map((item) => (
                    <option key={item._id} value={item._id}>
                      {item.name} ({item.quantity} {item.unit}) - {item.category}
                    </option>
                  ))}
                </select>
              </div>
              <div className="grid gap-2">
                <Label>Inventory Quantity per Add-on Unit</Label>
                <Input
                  type="number"
                  value={linkForm.inventoryQuantityPerUnit}
                  onChange={(e) => setLinkForm(prev => ({ ...prev, inventoryQuantityPerUnit: e.target.value }))}
                  placeholder="How much inventory is used per add-on"
                />
              </div>
              <div className="flex items-center gap-2">
                <input
                  type="checkbox"
                  checked={linkForm.trackInventory}
                  onChange={(e) => setLinkForm(prev => ({ ...prev, trackInventory: e.target.checked }))}
                />
                <Label>Track inventory automatically</Label>
              </div>
              <div className="flex gap-2">
                <Button onClick={handleLinkToInventory} disabled={loading}>Link</Button>
                <Button variant="outline" onClick={() => setLinkingAddOn(null)}>Cancel</Button>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
}

