/* eslint-disable react-hooks/exhaustive-deps */
"use client";

import React, { useState, useEffect, useCallback } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { getActiveOffersForCustomers } from "@/server/marketing";
import { Offer } from "@/app/type";
import {
  Gift,
  Percent,
  Tag,
  Clock,
  // Users,
  ShoppingCart,
  Sparkles,
  ChevronLeft,
  ChevronRight,
  Calendar,
  Star,
  ChevronDown,
  ChevronUp,
  Zap,
  Target,
  TrendingUp,
  Award,
} from "lucide-react";

interface OffersCarouselProps {
  outletId?: string;
  foodChainId?: string;
  className?: string;
  onOfferClick?: (offer: Offer) => void;
  autoSlide?: boolean;
  slideInterval?: number;
}

const OFFER_CONFIGS = {
  BOGO: {
    icon: Gift,
    gradient: "from-emerald-500 via-green-500 to-teal-600",
    badge: "BOGO",
    color: "emerald",
  },
  combo: {
    icon: ShoppingCart,
    gradient: "from-blue-500 via-cyan-500 to-indigo-600",
    badge: "COMBO",
    color: "blue",
  },
  discount: {
    icon: Percent,
    gradient: "from-purple-500 via-violet-500 to-indigo-600",
    badge: "SAVE",
    color: "purple",
  },
  freeItem: {
    icon: Gift,
    gradient: "from-orange-500 via-amber-500 to-yellow-600",
    badge: "FREE",
    color: "orange",
  },
  quantityDiscount: {
    icon: TrendingUp,
    gradient: "from-indigo-500 via-purple-500 to-pink-600",
    badge: "BULK",
    color: "indigo",
  },
  minimumAmount: {
    icon: Target,
    gradient: "from-rose-500 via-pink-500 to-red-600",
    badge: "MIN ORDER",
    color: "rose",
  },
  customerTier: {
    icon: Award,
    gradient: "from-yellow-500 via-orange-500 to-red-600",
    badge: "VIP",
    color: "yellow",
  },
  firstTime: {
    icon: Sparkles,
    gradient: "from-violet-500 via-purple-500 to-fuchsia-600",
    badge: "WELCOME",
    color: "violet",
  },
  timeBasedSpecial: {
    icon: Clock,
    gradient: "from-cyan-500 via-blue-500 to-indigo-600",
    badge: "LIMITED",
    color: "cyan",
  },
  dayOfWeek: {
    icon: Calendar,
    gradient: "from-teal-500 via-green-500 to-emerald-600",
    badge: "DAILY",
    color: "teal",
  },
  dateRange: {
    icon: Calendar,
    gradient: "from-red-500 via-rose-500 to-pink-600",
    badge: "SEASONAL",
    color: "red",
  },
} as const;

export default function OffersCarousel({
  outletId,
  foodChainId,
  className = "",
  onOfferClick,
  autoSlide = false,
  slideInterval = 4000,
}: OffersCarouselProps) {
  const [offers, setOffers] = useState<Offer[]>([]);
  const [loading, setLoading] = useState(false);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isExpanded, setIsExpanded] = useState(true);
  const [isHovered, setIsHovered] = useState(false);

  useEffect(() => {
    fetchActiveOffers();
  }, [outletId, foodChainId]);

  // Auto-slide functionality
  useEffect(() => {
    if (!autoSlide || offers.length <= 1 || isHovered) return;

    const interval = setInterval(() => {
      setCurrentIndex((prev) => (prev + 1) % offers.length);
    }, slideInterval);

    return () => clearInterval(interval);
  }, [autoSlide, offers.length, slideInterval, isHovered]);

  const fetchActiveOffers = async () => {
    try {
      setLoading(true);
      console.log("🔍 Fetching offers with params:", { outletId, foodChainId });

      const response = await getActiveOffersForCustomers({
        outletId,
        foodChainId,
      });

      console.log("📦 Offers API response:", response);

      if (response.success) {
        const displayableOffers = (response.data || []).filter(
          (offer: Offer) => offer.displayOnApp !== false
        );
        console.log("✨ Displayable offers:", displayableOffers);
        setOffers(displayableOffers);
      } else {
        console.error("❌ Failed to fetch offers:", response.message);
      }
    } catch (error) {
      console.error("💥 Error fetching active offers:", error);
    } finally {
      setLoading(false);
    }
  };

  const getOfferConfig = (offerType: string) => {
    return OFFER_CONFIGS[offerType as keyof typeof OFFER_CONFIGS] || {
      icon: Tag,
      gradient: "from-gray-500 to-gray-600",
      badge: "OFFER",
      color: "gray",
    };
  };

  const getOfferTitle = useCallback((offer: Offer) => {
    const { offerType, discountDetails } = offer;

    switch (offerType) {
      case "BOGO":
        return `Buy ${discountDetails.buyQuantity} Get ${discountDetails.getQuantity} FREE`;
      case "discount":
      case "minimumAmount":
        return discountDetails.discountType === "percentage"
          ? `${discountDetails.discountValue}% OFF`
          : `₹${discountDetails.discountValue} OFF`;
      case "quantityDiscount":
        return `${
          discountDetails.discountType === "percentage"
            ? `${discountDetails.discountValue}% OFF`
            : `₹${discountDetails.discountValue} OFF`
        } on ${discountDetails.buyQuantity}+ items`;
      case "freeItem":
        return "FREE ITEM INCLUDED";
      case "combo":
        return `COMBO SPECIAL @ ₹${discountDetails.comboPrice}`;
      case "firstTime":
        return "WELCOME SPECIAL";
      default:
        return offer.name;
    }
  }, []);

  const getOfferSubtitle = useCallback((offer: Offer) => {
    const { offerType, discountDetails } = offer;

    switch (offerType) {
      case "minimumAmount":
        return `Min. order ₹${discountDetails.minimumOrderValue}`;
      case "freeItem":
        return discountDetails.freeItemId?.name || "Get special item free";
      case "customerTier":
        return "Exclusive VIP member benefit";
      case "firstTime":
        return "New customer exclusive";
      case "timeBasedSpecial":
        return "Limited time only";
      case "dayOfWeek":
        return "Today's special deal";
      case "BOGO":
        return "Buy more, save more";
      case "combo":
        return "Perfect combination deal";
      default:
        return offer.description || "Special offer just for you";
    }
  }, []);

  const nextSlide = () => {
    setCurrentIndex((prev) => (prev + 1) % offers.length);
  };

  const prevSlide = () => {
    setCurrentIndex((prev) => (prev - 1 + offers.length) % offers.length);
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = date.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays <= 1) return "Ends today";
    if (diffDays <= 7) return `${diffDays} days left`;
    
    return date.toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
    });
  };

  // Loading skeleton
  if (loading) {
    return (
      <div className={`relative ${className}`}>
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-2">
            <div className="h-5 w-5 bg-gray-300 rounded animate-pulse" />
            <div className="h-6 w-32 bg-gray-300 rounded animate-pulse" />
          </div>
        </div>
        <div className="h-32 bg-gradient-to-r from-gray-200 to-gray-300 rounded-xl animate-pulse" />
      </div>
    );
  }

  if (offers.length === 0) {
    return null;
  }

  return (
    <div className={`relative ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-2">
          <div className="p-2 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full">
            <Star className="h-4 w-4 md:h-5 md:w-5 text-white" />
          </div>
          <h3 className="text-lg md:text-xl font-bold text-gray-900">
            Special Offers
            <span className="ml-2 text-sm font-normal text-gray-500">
              ({offers.length} available)
            </span>
          </h3>
        </div>
        
        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsExpanded(!isExpanded)}
            className="h-8 w-8 p-0 hover:bg-gray-100"
          >
            {isExpanded ? (
              <ChevronUp className="h-4 w-4" />
            ) : (
              <ChevronDown className="h-4 w-4" />
            )}
          </Button>
          
          {offers.length > 1 && isExpanded && (
            <div className="flex gap-1">
              <Button
                variant="outline"
                size="sm"
                onClick={prevSlide}
                className="h-8 w-8 p-0 hover:bg-gray-50"
                disabled={offers.length <= 1}
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={nextSlide}
                className="h-8 w-8 p-0 hover:bg-gray-50"
                disabled={offers.length <= 1}
              >
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          )}
        </div>
      </div>

      {/* Carousel */}
      {isExpanded && (
        <div 
          className="relative"
          onMouseEnter={() => setIsHovered(true)}
          onMouseLeave={() => setIsHovered(false)}
        >
          <div className="relative overflow-hidden rounded-2xl">
            <div
              className="flex transition-all duration-500 ease-out"
              style={{ transform: `translateX(-${currentIndex * 100}%)` }}
            >
              {offers.map((offer, index) => {
                const config = getOfferConfig(offer.offerType);
                const IconComponent = config.icon;
                
                return (
                  <div key={offer._id || index} className="w-full flex-shrink-0 px-1">
                    <Card
                      className={`relative overflow-hidden bg-gradient-to-br ${config.gradient} text-white cursor-pointer hover:scale-[1.02] transition-all duration-300 hover:shadow-2xl border-0 group`}
                      onClick={() => onOfferClick?.(offer)}
                    >
                      {/* Animated background pattern */}
                      <div className="absolute inset-0 opacity-10">
                        <div className="absolute -top-4 -right-4 w-24 h-24 bg-white rounded-full opacity-20 group-hover:scale-125 transition-transform duration-500" />
                        <div className="absolute -bottom-6 -left-6 w-32 h-32 bg-white rounded-full opacity-10 group-hover:scale-110 transition-transform duration-700" />
                      </div>

                      <CardContent className="p-6 relative z-10">
                        <div className="flex items-start justify-between mb-4">
                          <div className="flex items-center gap-3">
                            <div className="p-2 bg-white/20 rounded-lg backdrop-blur-sm">
                              <IconComponent className="h-5 w-5 text-white" />
                            </div>
                            <Badge
                              variant="secondary"
                              className="bg-white/20 text-white border-white/30 backdrop-blur-sm font-semibold px-3"
                            >
                              {config.badge}
                            </Badge>
                          </div>
                          
                          <div className="text-right">
                            <div className="text-xs text-white/80 mb-1">Valid till</div>
                            <div className="text-sm font-semibold text-white">
                              {formatDate(offer.endDate.toString())}
                            </div>
                          </div>
                        </div>

                        <div className="space-y-3">
                          <h4 className="font-black text-xl md:text-2xl leading-tight">
                            {getOfferTitle(offer)}
                          </h4>

                          <p className="text-white/90 text-sm font-medium">
                            {getOfferSubtitle(offer)}
                          </p>

                          <div className="flex items-center justify-between pt-2">
                            <div className="flex items-center gap-2">
                              {offer.autoApply && (
                                <Badge
                                  variant="secondary"
                                  className="bg-white/20 text-white border-white/30 backdrop-blur-sm text-xs"
                                >
                                  <Zap className="h-3 w-3 mr-1" />
                                  Auto Apply
                                </Badge>
                              )}
                            </div>
                            
                            <div className="text-xs text-white/70 font-medium">
                              Tap to apply →
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                );
              })}
            </div>
          </div>

          {/* Progress indicators */}
          {offers.length > 1 && (
            <div className="flex justify-center mt-4 gap-2">
              {offers.map((_, index) => (
                <button
                  key={index}
                  className={`h-2 rounded-full transition-all duration-300 ${
                    index === currentIndex 
                      ? "bg-gray-800 w-8" 
                      : "bg-gray-300 w-2 hover:bg-gray-400"
                  }`}
                  onClick={() => setCurrentIndex(index)}
                />
              ))}
            </div>
          )}

          {/* Swipe hint for mobile */}
          {offers.length > 1 && (
            <div className="md:hidden text-center mt-2">
              <p className="text-xs text-gray-500">Swipe to see more offers</p>
            </div>
          )}
        </div>
      )}
    </div>
  );
}