/* eslint-disable @typescript-eslint/no-explicit-any */
import { getAdminFoodChainId } from "@/server";
import { Icon } from "@iconify/react/dist/iconify.js";
import { Eye } from "lucide-react";
import Link from "next/link";
import React, { useEffect, useState, useRef } from "react";
import QRCode from "react-qr-code";

export const handleDownload = (ref: any, outletId: string) => {
  if (!ref.current) return;

  const svg = ref.current;
  const svgData = new XMLSerializer().serializeToString(svg);
  const canvas = document.createElement("canvas");

  const ctx: any = canvas.getContext("2d");
  const img = new Image();
  img.onload = () => {
    // Set canvas dimensions to match the QR code
    canvas.width = img.width;
    canvas.height = img.height;

    // Draw white background to ensure QR code is visible
    ctx.fillStyle = "white";
    ctx.fillRect(0, 0, canvas.width, canvas.height);

    // Draw the QR code
    ctx.drawImage(img, 0, 0);
    // Convert canvas to data URL and trigger download
    const dataUrl = canvas.toDataURL("image/png");
    const a = document.createElement("a");
    a.download = `qrcode-${outletId || "outlet"}.png`;
    a.href = dataUrl;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
  };

  // Load SVG as image
  img.src = "data:image/svg+xml;base64," + btoa(svgData);
};

const OutletQrCode = ({
  outletId,
  onlyQr,
}: {
  outletId?: string;
  onlyQr?: boolean;
}) => {
  const [link, setLink] = useState("");
  const qrCodeRef = useRef(null);

  useEffect(() => {
    const chainId = getAdminFoodChainId();
    setLink(
      `${window.location.origin}/chat?${chainId ? `chainId=${chainId}` : ""}${
        outletId ? `&outletId=${outletId}` : ""
      } `
    );
  }, [outletId]);

  if (onlyQr) {
    return <QRCode id="" className="animate-pulse gradient-flow" value={link} />;
  }

  return (
    <div className="border rounded p-4 flex flex-col justify-center items-center">
      <div className="flex gap-2 justify-end w-full">
        <div className="flex justify-center gap-2 cursor-pointer">
          <Link
            href={`${window.location.href}?view=true`}
            target="_blank"
            rel="noopener noreferrer"
          >
            <Eye className="h-5 w-5" />
          </Link>
        </div>
        <div
          className="flex justify-center gap-2 cursor-pointer"
          onClick={() => handleDownload(qrCodeRef, outletId as string)}
        >
          <Icon icon="line-md:download-loop" width="30" height="30" />
        </div>
        <div
          className="flex justify-center gap-2 cursor-pointer"
          onClick={() =>
            window.navigator.share({ url: link }).catch((error) => {
              console.error("Error sharing:", error);
            })
          }
        >
          <Icon icon="ic:baseline-share" width="24" height="24" />
        </div>
        <div
          className="flex justify-center gap-2 cursor-pointer"
          onClick={() => window.navigator.clipboard.writeText(link)}
        >
          <Icon icon="solar:copy-linear" width="24" height="24" />
        </div>
      </div>
      <div id="qr-code">
        <QRCode ref={qrCodeRef} value={link} />
      </div>
    </div>
  );
};

export default OutletQrCode;
