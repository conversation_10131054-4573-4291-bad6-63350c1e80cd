"use client";
import { Volume2, Ban } from "lucide-react";
import { useState, useEffect } from "react";
import { SupportedLanguage } from "../../../../hooks/useLanguagePreference";

interface TextToSpeechOptions {
  rate?: number;
  pitch?: number;
  volume?: number;
  voice?: string; // voice name
}

interface TextToSpeechProps {
  text: string;
  language?: SupportedLanguage;
  options?: TextToSpeechOptions;
  disabled?: boolean;
  className?: string;
  onStart?: () => void;
  onEnd?: () => void;
  onError?: () => void;
}

// Helper function to get the best voice for a language
const getBestVoiceForLanguage = (
  voices: SpeechSynthesisVoice[],
  language: SupportedLanguage = "en"
): SpeechSynthesisVoice | null => {
  if (voices.length === 0) return null;

  // Define preferred voices for each language
  const voicePreferences = {
    hi: [
      "Google हिन्दी", // Google Hindi
      "Microsoft Hemant - Hindi (India)",
      "Microsoft Kalpana - Hindi (India)",
      "Lekha", // iOS Hindi voice
      "Rishi", // iOS Hindi voice
      "hi-IN", // Generic Hindi
    ],
    en: [
      "Google US English",
      "Microsoft Zira - English (United States)",
      "Microsoft David - English (United States)",
      "Alex", // macOS default
      "Samantha", // macOS
      "en-US", // Generic US English
    ],
  };

  const preferences = voicePreferences[language as keyof typeof voicePreferences] || voicePreferences.en;

  // Try to find preferred voices in order
  for (const preferredVoice of preferences) {
    const voice = voices.find(v =>
      v.name.includes(preferredVoice) ||
      v.lang.includes(preferredVoice) ||
      v.name.toLowerCase().includes(preferredVoice.toLowerCase())
    );
    if (voice) return voice;
  }

  // Fallback: find any voice that matches the language code
  const langCode = language === "hi" ? "hi" : "en";
  const fallbackVoice = voices.find(v => v.lang.startsWith(langCode));
  if (fallbackVoice) return fallbackVoice;

  // Final fallback: return first available voice
  return voices[0];
};

export default function TextToSpeech({
  text,
  language = "en",
  options = {},
  disabled = false,
  className = "",
  onStart,
  onEnd,
  onError,
}: TextToSpeechProps) {
  const [isReading, setIsReading] = useState(false);
  const [voices, setVoices] = useState<SpeechSynthesisVoice[]>([]);
  const { rate = 1, pitch = 1, volume = 1, voice: voiceName } = options;

  useEffect(() => {
    const loadVoices = () => {
      const availableVoices = window.speechSynthesis.getVoices();
      setVoices(availableVoices);
    };

    loadVoices();
    window.speechSynthesis.addEventListener("voiceschanged", loadVoices);

    return () => {
      window.speechSynthesis.removeEventListener("voiceschanged", loadVoices);
    };
  }, []);

  const handleReadText = () => {
    if (!("speechSynthesis" in window)) {
      alert("Your browser does not support text-to-speech");
      return;
    }

    window.speechSynthesis.cancel();

    const utterance = new SpeechSynthesisUtterance(text);

    // Set voice based on language or specific voice name
    if (voiceName) {
      const selectedVoice = voices.find((v) => v.name === voiceName);
      if (selectedVoice) {
        utterance.voice = selectedVoice;
      }
    } else {
      // Use language-specific voice selection
      const bestVoice = getBestVoiceForLanguage(voices, language);
      if (bestVoice) {
        utterance.voice = bestVoice;
        console.log(`🔊 Using voice: ${bestVoice.name} (${bestVoice.lang}) for language: ${language}`);
      }
    }

    // Set language for the utterance
    if (language === "hi") {
      utterance.lang = "hi-IN";
    } else {
      utterance.lang = "en-US";
    }

    utterance.rate = rate;
    utterance.pitch = pitch;
    utterance.volume = volume;

    utterance.onstart = () => {
      setIsReading(true);
      onStart?.();
    };

    utterance.onend = () => {
      setIsReading(false);
      onEnd?.();
    };

    utterance.onerror = (event) => {
      console.error("Text-to-speech error:", event);
      setIsReading(false);
      onError?.();
    };

    window.speechSynthesis.speak(utterance);
  };

  const handleStopReading = () => {
    window.speechSynthesis.cancel();
    setIsReading(false);
  };

  return (
    <div className={`flex gap-2 ${className}`}>
      <button
        onClick={() => handleReadText()}
        disabled={disabled || isReading || !text.trim()}
        className="px-2 py-1 md:px-4 md:py-2 text-white rounded disabled:bg-gray-300 "
      >
        {isReading ? (
          <div className="animate-pulse h-6 md:h-8">
            <Volume2 />
          </div>
        ) : (
          <Volume2 className="cursor-pointer h-6" />
        )}
      </button>

      {isReading && (
        <button
          onClick={handleStopReading}
          className="px-2 py-1 md:px-4 md:py-2 bg-red-500 text-white rounded disabled:bg-gray-300"
        >
          <Ban />
        </button>
      )}
    </div>
  );
}
