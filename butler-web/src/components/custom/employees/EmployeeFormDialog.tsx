"use client";

import React, { useEffect, useState } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  <PERSON><PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Loader2 } from "lucide-react";
import { Outlet } from "@/app/type";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";

interface Employee {
  _id: string;
  name: string;
  phone?: string;
  email: string;
  role: string;
  status?: "active" | "inactive";
  outlets?: { _id: string; name: string }[];
  hasPortalAccess?: boolean;
  salary?: number;
  address?: string;
  emergencyContact?: {
    name?: string;
    phone?: string;
    relation?: string;
  };
}

interface EmployeeFormDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  employee: Employee | null;
  outlets: Outlet[];
  onSubmit: (data: {
    name: string;
    email: string;
    phone?: string;
    role: string;
    status?: "active" | "inactive";
    outletIds: string[];
    hasPortalAccess?: boolean;
    salary?: number;
    address?: string;
    emergencyContact?: {
      name?: string;
      phone?: string;
      relation?: string;
    };
  }) => void;
  isCreating: boolean;
}

const EmployeeFormDialog = ({
  open,
  onOpenChange,
  employee,
  outlets,
  onSubmit,
  isCreating,
}: EmployeeFormDialogProps) => {
  const [name, setName] = useState("");
  const [email, setEmail] = useState("");
  const [phone, setPhone] = useState("");
  const [role, setRole] = useState("waiter");
  const [status, setStatus] = useState("active");
  const [selectedOutlets, setSelectedOutlets] = useState<string[]>([]);
  const [hasPortalAccess, setHasPortalAccess] = useState(false);
  const [salary, setSalary] = useState("");
  const [address, setAddress] = useState("");
  const [emergencyContactName, setEmergencyContactName] = useState("");
  const [emergencyContactPhone, setEmergencyContactPhone] = useState("");
  const [emergencyContactRelation, setEmergencyContactRelation] = useState("");
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (open) {
      if (employee) {
        setName(employee.name || "");
        setEmail(employee.email || "");
        setPhone(employee.phone || "");
        setRole(employee.role || "waiter");
        setStatus(employee.status || "active");
        setHasPortalAccess(employee.hasPortalAccess || false);
        setSalary(employee.salary?.toString() || "");
        setAddress(employee.address || "");
        setEmergencyContactName(employee.emergencyContact?.name || "");
        setEmergencyContactPhone(employee.emergencyContact?.phone || "");
        setEmergencyContactRelation(employee.emergencyContact?.relation || "");

        // Set selected outlets
        if (employee.outlets && employee.outlets.length > 0) {
          setSelectedOutlets(employee.outlets.map((outlet) => outlet._id));
        } else {
          setSelectedOutlets([]);
        }
      } else {
        // Reset form for new employee
        setName("");
        setEmail("");
        setPhone("");
        setRole("waiter");
        setStatus("active");
        setSelectedOutlets([]);
        setHasPortalAccess(false);
        setSalary("");
        setAddress("");
        setEmergencyContactName("");
        setEmergencyContactPhone("");
        setEmergencyContactRelation("");
      }
    }
  }, [open, employee]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      const data: {
        name: string;
        email: string;
        phone: string;
        role: string;
        outletIds: string[];
        hasPortalAccess: boolean;
        salary?: number;
        address?: string;
        emergencyContact?: {
          name?: string;
          phone?: string;
          relation?: string;
        };
        status?: "active" | "inactive";
      } = {
        name,
        email,
        phone,
        role,
        outletIds: selectedOutlets,
        hasPortalAccess,
      };

      // Add optional fields
      if (salary) {
        data.salary = parseFloat(salary);
      }
      if (address) {
        data.address = address;
      }
      if (
        emergencyContactName ||
        emergencyContactPhone ||
        emergencyContactRelation
      ) {
        data.emergencyContact = {
          name: emergencyContactName,
          phone: emergencyContactPhone,
          relation: emergencyContactRelation,
        };
      }

      if (!isCreating) {
        data.status = status as "active" | "inactive";
      }

      await onSubmit(data);
    } catch (error) {
      console.error("Error submitting form:", error);
    } finally {
      setLoading(false);
    }
  };

  const toggleOutlet = (outletId: string) => {
    setSelectedOutlets((prev) =>
      prev.includes(outletId)
        ? prev.filter((id) => id !== outletId)
        : [...prev, outletId]
    );
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {isCreating ? "Add New Employee" : "Edit Employee"}
          </DialogTitle>
          <DialogDescription>
            {isCreating
              ? "Create a new employee account"
              : "Update employee information"}
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid gap-2">
            <Label htmlFor="name">Name</Label>
            <Input
              id="name"
              value={name}
              onChange={(e) => setName(e.target.value)}
              placeholder="Enter employee name"
              required
            />
          </div>

          <div className="grid gap-2">
            <Label htmlFor="email">Email</Label>
            <Input
              id="email"
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              placeholder="Enter employee email"
              required
            />
          </div>

          <div className="grid gap-2">
            <Label htmlFor="phone">Phone (optional)</Label>
            <Input
              id="phone"
              value={phone}
              onChange={(e) => setPhone(e.target.value)}
              placeholder="Enter employee phone"
            />
          </div>

          <div className="grid gap-2">
            <Label htmlFor="role">Role</Label>
            <Select value={role} onValueChange={setRole}>
              <SelectTrigger>
                <SelectValue placeholder="Select role" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="manager">Manager</SelectItem>
                <SelectItem value="chef">Chef</SelectItem>
                <SelectItem value="delivery">Delivery</SelectItem>
                <SelectItem value="cashier">Cashier</SelectItem>
                <SelectItem value="waiter">Waiter</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Portal Access */}
          <div className="flex items-center space-x-2">
            <Checkbox
              id="hasPortalAccess"
              checked={hasPortalAccess}
              onCheckedChange={(checked) =>
                setHasPortalAccess(checked === true)
              }
            />
            <Label htmlFor="hasPortalAccess" className="text-sm font-medium">
              Grant access to portal/platform
            </Label>
          </div>
          {hasPortalAccess && (
            <div className="text-xs text-muted-foreground bg-blue-50 p-2 rounded">
              This employee will receive login credentials and can access the
              system.
              {role === "manager" && " Managers get admin-level access."}
            </div>
          )}

          {/* Additional Employee Details */}
          <div className="grid gap-2">
            <Label htmlFor="salary">Salary (optional)</Label>
            <Input
              id="salary"
              type="number"
              value={salary}
              onChange={(e) => setSalary(e.target.value)}
              placeholder="Enter salary amount"
            />
          </div>

          <div className="grid gap-2">
            <Label htmlFor="address">Address (optional)</Label>
            <Input
              id="address"
              value={address}
              onChange={(e) => setAddress(e.target.value)}
              placeholder="Enter employee address"
            />
          </div>

          {/* Emergency Contact */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">
              Emergency Contact (optional)
            </Label>
            <div className="grid grid-cols-2 gap-2">
              <Input
                placeholder="Contact name"
                value={emergencyContactName}
                onChange={(e) => setEmergencyContactName(e.target.value)}
              />
              <Input
                placeholder="Contact phone"
                value={emergencyContactPhone}
                onChange={(e) => setEmergencyContactPhone(e.target.value)}
              />
            </div>
            <Input
              placeholder="Relationship"
              value={emergencyContactRelation}
              onChange={(e) => setEmergencyContactRelation(e.target.value)}
            />
          </div>

          {!isCreating && (
            <div className="grid gap-2">
              <Label htmlFor="status">Status</Label>
              <Select value={status} onValueChange={setStatus}>
                <SelectTrigger>
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="inactive">Inactive</SelectItem>
                </SelectContent>
              </Select>
            </div>
          )}

          <div className="grid gap-2">
            <Label>Assign to Outlets</Label>
            <div className="border rounded-md p-3 max-h-[200px] overflow-y-auto">
              {outlets.length === 0 ? (
                <p className="text-sm text-gray-500">No outlets available</p>
              ) : (
                <div className="space-y-2">
                  {outlets.map((outlet) => (
                    <div
                      key={outlet._id}
                      className="flex items-center space-x-2"
                    >
                      <Checkbox
                        id={`outlet-${outlet._id}`}
                        checked={selectedOutlets.includes(outlet._id || "")}
                        onCheckedChange={() => toggleOutlet(outlet._id || "")}
                      />
                      <Label
                        htmlFor={`outlet-${outlet._id}`}
                        className="text-sm font-normal cursor-pointer"
                      >
                        {outlet.name}, {outlet.address}
                      </Label>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  {isCreating ? "Creating..." : "Updating..."}
                </>
              ) : (
                <>{isCreating ? "Create Employee" : "Update Employee"}</>
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default EmployeeFormDialog;
