/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useState, useEffect } from "react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { getAllInventoryItems } from "@/server/inventory";
import { linkInventoryToDish } from "@/server/inventory";
import { toast } from "sonner";
import { Loader2, Plus, Trash2 } from "lucide-react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

interface DishIngredientsDialogProps {
  dish: any;
  onClose: (refresh: boolean) => void;
  open: boolean;
}

interface Ingredient {
  id: string;
  inventoryItemId: string | null;
  name: string;
  quantity: number;
  unit: string;
  isOptional: boolean;
  canBeExcluded: boolean;
}

interface InventoryItem {
  _id: string;
  name: string;
  unit: string;
  outletId: {
    _id: string;
    name: string;
  };
}

const DishIngredientsDialog: React.FC<DishIngredientsDialogProps> = ({
  dish,
  onClose,
  open,
}) => {
  const [ingredients, setIngredients] = useState<Ingredient[]>([]);

  // Helper function to handle empty values
  const getInventoryItemId = (value: string) => {
    return value === "none" ? null : value;
  };
  const [inventoryItems, setInventoryItems] = useState<InventoryItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [fetchingItems, setFetchingItems] = useState(true);

  useEffect(() => {
    fetchInventoryItems();
    if (dish && dish.ingredients && dish.ingredients.length > 0) {
      const formattedIngredients = dish.ingredients.map(
        (ing: any, index: number) => ({
          id: `existing-${index}`,
          inventoryItemId: ing.inventoryItemId || null,
          name: ing.name,
          quantity: ing.quantity,
          unit: ing.unit,
          isOptional: ing.isOptional || false,
          canBeExcluded: ing.canBeExcluded || false,
        })
      );
      setIngredients(formattedIngredients);
    }
  }, [dish]);

  const fetchInventoryItems = async () => {
    setFetchingItems(true);
    try {
      const response = await getAllInventoryItems(1, 100);
      setInventoryItems(response.data.items);
    } catch (error) {
      console.error("Error fetching inventory items:", error);
      toast.error("Failed to load inventory items");
    } finally {
      setFetchingItems(false);
    }
  };

  const addIngredient = () => {
    const newIngredient: Ingredient = {
      id: `new-${Date.now()}`,
      inventoryItemId: null,
      name: "",
      quantity: 1,
      unit: "",
      isOptional: false,
      canBeExcluded: false,
    };
    setIngredients([...ingredients, newIngredient]);
  };

  const removeIngredient = (id: string) => {
    setIngredients(ingredients.filter((ing) => ing.id !== id));
  };

  const updateIngredient = (id: string, field: string, value: any) => {
    setIngredients(
      ingredients.map((ing) => {
        if (ing.id === id) {
          if (field === "inventoryItemId" && value) {
            const selectedItem = inventoryItems.find(
              (item) => item._id === value
            );
            if (selectedItem) {
              return {
                ...ing,
                [field]: value,
                name: selectedItem.name,
                unit: selectedItem.unit,
              };
            }
          }
          return { ...ing, [field]: value };
        }
        return ing;
      })
    );
  };

  const handleSubmit = async () => {
    // Validate ingredients
    const invalidIngredients = ingredients.filter(
      (ing) => !ing.name || ing.quantity <= 0 || !ing.unit
    );

    if (invalidIngredients.length > 0) {
      toast.error("All ingredients must have a name, quantity, and unit");
      return;
    }

    setLoading(true);
    try {
      // Format ingredients for API
      const formattedIngredients = ingredients.map((ing) => ({
        inventoryItemId: ing.inventoryItemId || undefined,
        name: ing.name,
        quantity: ing.quantity,
        unit: ing.unit,
        isOptional: ing.isOptional,
        canBeExcluded: ing.canBeExcluded,
      }));

      await linkInventoryToDish(dish._id, formattedIngredients);
      toast.success("Dish ingredients updated successfully");
      onClose(true);
    } catch (error) {
      console.error("Error updating dish ingredients:", error);
      toast.error("Failed to update dish ingredients");
    } finally {
      setLoading(false);
    }
  };

  const handleOpenChange = (isOpen: boolean) => {
    if (!isOpen) {
      onClose(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent className="sm:max-w-[700px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Manage Ingredients for {dish.name}</DialogTitle>
          <DialogDescription>
            Add or update ingredients and link them to inventory items
          </DialogDescription>
        </DialogHeader>

        <div className="py-4">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-medium">Ingredients</h3>
            <Button onClick={addIngredient} size="sm">
              <Plus className="h-4 w-4 mr-2" />
              Add Ingredient
            </Button>
          </div>

          {fetchingItems ? (
            <div className="flex justify-center py-4">
              <Loader2 className="h-6 w-6 animate-spin text-primary" />
            </div>
          ) : ingredients.length === 0 ? (
            <div className="text-center py-6 text-gray-500">
              <p>No ingredients added yet</p>
              <Button
                variant="outline"
                className="mt-2"
                onClick={addIngredient}
              >
                <Plus className="h-4 w-4 mr-2" />
                Add First Ingredient
              </Button>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Inventory Item</TableHead>
                  <TableHead>Name</TableHead>
                  <TableHead>Quantity</TableHead>
                  <TableHead>Unit</TableHead>
                  <TableHead>Options</TableHead>
                  <TableHead></TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {ingredients.map((ingredient) => (
                  <TableRow key={ingredient.id}>
                    <TableCell>
                      <Select
                        value={ingredient.inventoryItemId || "none"}
                        onValueChange={(value) =>
                          updateIngredient(
                            ingredient.id,
                            "inventoryItemId",
                            getInventoryItemId(value)
                          )
                        }
                      >
                        <SelectTrigger className="w-full">
                          <SelectValue placeholder="Link to inventory (optional)" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="none">No link</SelectItem>
                          {inventoryItems.map((item) => (
                            <SelectItem key={item._id} value={item._id}>
                              {item.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </TableCell>
                    <TableCell>
                      <Input
                        value={ingredient.name}
                        onChange={(e) =>
                          updateIngredient(
                            ingredient.id,
                            "name",
                            e.target.value
                          )
                        }
                        placeholder="Ingredient name"
                        disabled={!!ingredient.inventoryItemId}
                      />
                    </TableCell>
                    <TableCell>
                      <Input
                        type="number"
                        min="0.01"
                        step="0.01"
                        value={ingredient.quantity}
                        onChange={(e) =>
                          updateIngredient(
                            ingredient.id,
                            "quantity",
                            parseFloat(e.target.value) || 0
                          )
                        }
                        placeholder="Qty"
                      />
                    </TableCell>
                    <TableCell>
                      <Input
                        value={ingredient.unit}
                        onChange={(e) =>
                          updateIngredient(
                            ingredient.id,
                            "unit",
                            e.target.value
                          )
                        }
                        placeholder="Unit"
                        disabled={!!ingredient.inventoryItemId}
                      />
                    </TableCell>
                    <TableCell>
                      <div className="flex space-x-4">
                        <div className="flex items-center space-x-2">
                          <Checkbox
                            id={`optional-${ingredient.id}`}
                            checked={ingredient.isOptional}
                            onCheckedChange={(checked) =>
                              updateIngredient(
                                ingredient.id,
                                "isOptional",
                                !!checked
                              )
                            }
                          />
                          <Label htmlFor={`optional-${ingredient.id}`}>
                            Optional
                          </Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Checkbox
                            id={`exclude-${ingredient.id}`}
                            checked={ingredient.canBeExcluded}
                            onCheckedChange={(checked) =>
                              updateIngredient(
                                ingredient.id,
                                "canBeExcluded",
                                !!checked
                              )
                            }
                          />
                          <Label htmlFor={`exclude-${ingredient.id}`}>
                            Can exclude
                          </Label>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => removeIngredient(ingredient.id)}
                      >
                        <Trash2 className="h-4 w-4 text-red-500" />
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onClose(false)}>
            Cancel
          </Button>
          <Button onClick={handleSubmit} disabled={loading}>
            {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            Save Ingredients
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default DishIngredientsDialog;
