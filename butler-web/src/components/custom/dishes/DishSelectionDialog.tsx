"use client";

import React, { useState, useEffect, useCallback, useMemo } from "react";
import Image from "next/image";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Plus, Minus, Star } from "lucide-react";
import { Dish, SelectedAddOn } from "@/app/type";
import SimpleAddOnSelector from "../addons/SimpleAddOnSelector";

interface DishSelectionDialogProps {
  dish: Dish | null;
  isOpen: boolean;
  onClose: () => void;
  onAddToCart: (dish: Dish, quantity: number, addOns: SelectedAddOn[]) => void;
  isInCart?: boolean;
  currentQuantity?: number;
  currentAddOns?: SelectedAddOn[];
}

const DishSelectionDialog: React.FC<DishSelectionDialogProps> = ({
  dish,
  isOpen,
  onClose,
  onAddToCart,
  isInCart = false,
  currentQuantity = 1,
  currentAddOns = [],
}) => {
  const [quantity, setQuantity] = useState(1);
  const [selectedAddOns, setSelectedAddOns] = useState<SelectedAddOn[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  // Reset state when dialog opens with a new dish
  useEffect(() => {
    if (isOpen && dish) {
      setQuantity(currentQuantity || 1);
      setSelectedAddOns(currentAddOns || []);
    }
  }, [isOpen, dish, currentQuantity, currentAddOns]);

  const handleQuantityChange = (newQuantity: number) => {
    if (newQuantity >= 1) {
      setQuantity(newQuantity);
    }
  };

  const handleAddOnChange = (addOns: SelectedAddOn[]) => {
    setSelectedAddOns(addOns);
  };

  const totalPrice = useMemo((): number => {
    if (!dish) return 0;

    const dishTotal = dish.price * quantity;
    const addOnsTotal = selectedAddOns.reduce(
      (total, addOn) => total + addOn.price * addOn.quantity,
      0
    );

    return dishTotal + addOnsTotal;
  }, [dish, quantity, selectedAddOns]);

  const handleAddToCart = async () => {
    if (!dish) return;
    
    setIsLoading(true);
    try {
      await onAddToCart(dish, quantity, selectedAddOns);
      onClose();
    } catch (error) {
      console.error("Error adding to cart:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleClose = useCallback(() => {
    // Reset state immediately
    setQuantity(1);
    setSelectedAddOns([]);
    setIsLoading(false);
    // Call onClose after a small delay to prevent race conditions
    setTimeout(() => {
      onClose();
    }, 0);
  }, [onClose]);

  const handleOpenChange = useCallback((open: boolean) => {
    if (!open) {
      handleClose();
    }
  }, [handleClose]);

  if (!dish) return null;

  return (
    <Dialog open={isOpen} onOpenChange={handleOpenChange}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            {dish.name}
            {dish.isFeatured && <Star className="h-4 w-4 text-yellow-500 fill-current" />}
          </DialogTitle>
          {dish.description && (
            <DialogDescription className="text-sm text-gray-600">
              {dish.description}
            </DialogDescription>
          )}
        </DialogHeader>

        <div className="space-y-4">
          {/* Dish Image */}
          {dish.image && (
            <div className="w-full h-32 bg-gray-100 rounded-md overflow-hidden relative">
              <Image
                src={dish.image}
                alt={dish.name}
                fill
                className="object-cover"
              />
            </div>
          )}

          {/* Price and Quantity */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Badge variant="outline" className="font-bold">
                ₹{dish.price}
              </Badge>
              {dish.isVeg !== undefined && (
                <Badge
                  variant="outline"
                  className={`text-xs ${
                    dish.isVeg
                      ? "text-green-600 border-green-600"
                      : "text-red-600 border-red-600"
                  }`}
                >
                  {dish.isVeg ? "Veg" : "Non-Veg"}
                </Badge>
              )}
            </div>

            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="icon"
                className="h-8 w-8"
                onClick={() => handleQuantityChange(quantity - 1)}
                disabled={quantity <= 1}
              >
                <Minus className="h-4 w-4" />
              </Button>
              <span className="text-lg font-medium w-8 text-center">{quantity}</span>
              <Button
                variant="outline"
                size="icon"
                className="h-8 w-8"
                onClick={() => handleQuantityChange(quantity + 1)}
              >
                <Plus className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* Add-ons Selection */}
          {dish.enabledAddOns && dish.enabledAddOns.length > 0 && (
            <div className="border-t pt-4">
              <SimpleAddOnSelector
                availableAddOns={dish.enabledAddOns}
                selectedAddOns={selectedAddOns}
                onAddOnChange={handleAddOnChange}
                showPrices={true}
                compact={false}
              />
            </div>
          )}

          {/* Total Price */}
          <div className="border-t pt-4">
            <div className="flex justify-between items-center">
              <span className="text-lg font-medium">Total:</span>
              <Badge variant="outline" className="text-lg font-bold px-3 py-1">
                ₹{totalPrice.toFixed(2)}
              </Badge>
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={handleClose}>
            Cancel
          </Button>
          <Button
            onClick={handleAddToCart}
            disabled={isLoading}
            className="min-w-[120px]"
          >
            {isLoading ? (
              "Adding..."
            ) : isInCart ? (
              "Update Cart"
            ) : (
              `Add to Cart`
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default DishSelectionDialog;
