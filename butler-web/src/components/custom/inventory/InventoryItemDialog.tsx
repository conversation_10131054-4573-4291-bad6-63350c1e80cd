/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useState, useEffect } from "react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { createInventoryItem, updateInventoryItem } from "@/server/inventory";
import { toast } from "sonner";
import { Loader2 } from "lucide-react";
import { format } from "date-fns";

interface InventoryItemDialogProps {
  item: any | null;
  outlets: Array<{ _id: string; name: string; address: string }>;
  categories: string[];
  onClose: (refresh: boolean) => void;
}

const InventoryItemDialog: React.FC<InventoryItemDialogProps> = ({
  item,
  outlets,
  categories,
  onClose,
}) => {
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    category: "ingredient",
    unit: "",
    quantity: 0,
    minQuantity: 10,
    costPerUnit: 0,
    supplier: "",
    supplierContact: "",
    location: "",
    expiryDate: "",
    outletId: "",
  });
  const [loading, setLoading] = useState(false);
  const [customCategory, setCustomCategory] = useState("");
  const [showCustomCategory, setShowCustomCategory] = useState(false);

  useEffect(() => {
    if (item) {
      setFormData({
        name: item.name || "",
        description: item.description || "",
        category: item.category || "ingredient",
        unit: item.unit || "",
        quantity: item.quantity || 0,
        minQuantity: item.minQuantity || 10,
        costPerUnit: item.costPerUnit || 0,
        supplier: item.supplier || "",
        supplierContact: item.supplierContact || "",
        location: item.location || "",
        expiryDate: item.expiryDate
          ? format(new Date(item.expiryDate), "yyyy-MM-dd")
          : "",
        outletId: item.outletId?._id || "",
      });

      if (!categories.includes(item.category)) {
        setShowCustomCategory(true);
        setCustomCategory(item.category);
      }
    }
  }, [item, categories]);

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleNumberChange = (
    e: React.ChangeEvent<HTMLInputElement>,
    field: string
  ) => {
    const value = parseFloat(e.target.value);
    setFormData((prev) => ({
      ...prev,
      [field]: isNaN(value) ? 0 : value,
    }));
  };

  const handleSelectChange = (value: string, field: string) => {
    if (field === "category" && value === "custom") {
      setShowCustomCategory(true);
      return;
    }

    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleSubmit = async () => {
    if (!formData.name || !formData.unit || !formData.outletId) {
      toast.error("Please fill in all required fields");
      return;
    }

    setLoading(true);
    try {
      const finalCategory = showCustomCategory
        ? customCategory
        : formData.category;
      const payload = {
        ...formData,
        category: finalCategory,
      };

      if (item) {
        await updateInventoryItem(item._id, payload);
        toast.success("Inventory item updated successfully");
      } else {
        await createInventoryItem(payload);
        toast.success("Inventory item created successfully");
      }
      onClose(true);
    } catch (error) {
      console.error("Error saving inventory item:", error);
      toast.error("Failed to save inventory item");
    } finally {
      setLoading(false);
    }
  };

  const handleOpenChange = (isOpen: boolean) => {
    if (!isOpen) {
      onClose(false);
    }
  };

  return (
    <Dialog open={true} onOpenChange={handleOpenChange}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {item ? "Edit Inventory Item" : "Add Inventory Item"}
          </DialogTitle>
          <DialogDescription>
            {item
              ? "Update the details of this inventory item"
              : "Add a new item to your inventory"}
          </DialogDescription>
        </DialogHeader>

        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="name">Name *</Label>
              <Input
                id="name"
                name="name"
                value={formData.name}
                onChange={handleChange}
                placeholder="Item name"
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="outletId">Outlet *</Label>
              <Select
                value={formData.outletId}
                onValueChange={(value) => handleSelectChange(value, "outletId")}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select outlet" />
                </SelectTrigger>
                <SelectContent>
                  {outlets.map((outlet) => (
                    <SelectItem key={outlet._id} value={outlet._id}>
                      {outlet.name}, {outlet.address}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              name="description"
              value={formData.description}
              onChange={handleChange}
              placeholder="Item description"
              rows={2}
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="category">Category</Label>
              {!showCustomCategory ? (
                <Select
                  value={formData.category}
                  onValueChange={(value) =>
                    handleSelectChange(value, "category")
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="ingredient">Ingredient</SelectItem>
                    <SelectItem value="packaging">Packaging</SelectItem>
                    <SelectItem value="equipment">Equipment</SelectItem>
                    <SelectItem value="other">Other</SelectItem>
                    {categories
                      .filter(
                        (cat) =>
                          ![
                            "ingredient",
                            "packaging",
                            "equipment",
                            "other",
                          ].includes(cat)
                      )
                      .map((cat) => (
                        <SelectItem key={cat} value={cat}>
                          {cat}
                        </SelectItem>
                      ))}
                    <SelectItem value="custom">Custom Category...</SelectItem>
                  </SelectContent>
                </Select>
              ) : (
                <div className="flex space-x-2">
                  <Input
                    value={customCategory}
                    onChange={(e) => setCustomCategory(e.target.value)}
                    placeholder="Enter custom category"
                  />
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      setShowCustomCategory(false);
                      setFormData((prev) => ({
                        ...prev,
                        category: "ingredient",
                      }));
                    }}
                  >
                    Cancel
                  </Button>
                </div>
              )}
            </div>
            <div className="space-y-2">
              <Label htmlFor="location">Storage Location</Label>
              <Input
                id="location"
                name="location"
                value={formData.location}
                onChange={handleChange}
                placeholder="Storage location"
              />
            </div>
          </div>

          <div className="grid grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="quantity">Quantity *</Label>
              <Input
                id="quantity"
                type="number"
                min="0"
                step="0.01"
                value={formData.quantity}
                onChange={(e) => handleNumberChange(e, "quantity")}
                placeholder="Current quantity"
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="unit">Unit *</Label>
              <Input
                id="unit"
                name="unit"
                value={formData.unit}
                onChange={handleChange}
                placeholder="e.g., kg, liters, pieces"
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="minQuantity">Min Quantity</Label>
              <Input
                id="minQuantity"
                type="number"
                min="0"
                step="0.01"
                value={formData.minQuantity}
                onChange={(e) => handleNumberChange(e, "minQuantity")}
                placeholder="Low stock threshold"
              />
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="costPerUnit">Cost Per Unit (₹)</Label>
              <Input
                id="costPerUnit"
                type="number"
                min="0"
                step="0.01"
                value={formData.costPerUnit}
                onChange={(e) => handleNumberChange(e, "costPerUnit")}
                placeholder="Cost per unit"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="expiryDate">Expiry Date</Label>
              <Input
                id="expiryDate"
                name="expiryDate"
                type="date"
                value={formData.expiryDate}
                onChange={handleChange}
              />
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="supplier">Supplier</Label>
              <Input
                id="supplier"
                name="supplier"
                value={formData.supplier}
                onChange={handleChange}
                placeholder="Supplier name"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="supplierContact">Supplier Contact</Label>
              <Input
                id="supplierContact"
                name="supplierContact"
                value={formData.supplierContact}
                onChange={handleChange}
                placeholder="Phone or email"
              />
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onClose(false)}>
            Cancel
          </Button>
          <Button onClick={handleSubmit} disabled={loading}>
            {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            {item ? "Update Item" : "Add Item"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default InventoryItemDialog;
