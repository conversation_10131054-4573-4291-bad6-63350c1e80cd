/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useState } from "react";
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { updateInventoryQuantity } from "@/server/inventory";
import { toast } from "sonner";
import { Loader2 } from "lucide-react";

interface InventoryQuantityDialogProps {
  item: any;
  outlets: Array<{ _id: string; name: string }>;
  onClose: (refresh: boolean) => void;
}

const InventoryQuantityDialog: React.FC<InventoryQuantityDialogProps> = ({
  item,
  outlets,
  onClose,
}) => {
  const [quantity, setQuantity] = useState<number>(0);
  const [type, setType] = useState<string>("addition");
  const [reason, setReason] = useState<string>("");
  const [transferToOutletId, setTransferToOutletId] = useState<string>("");
  const [loading, setLoading] = useState(false);

  const handleQuantityChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseFloat(e.target.value);
    setQuantity(isNaN(value) ? 0 : value);
  };

  const handleTypeChange = (value: string) => {
    setType(value);
    if (value !== "transfer") {
      setTransferToOutletId("");
    }
  };

  const handleSubmit = async () => {
    if (quantity <= 0) {
      toast.error("Quantity must be greater than zero");
      return;
    }

    if (type === "transfer" && !transferToOutletId) {
      toast.error("Please select a destination outlet for transfer");
      return;
    }

    setLoading(true);
    try {
      const payload: any = {
        quantity,
        type,
        reason: reason || undefined,
      };

      if (type === "transfer") {
        payload.transferToOutletId = transferToOutletId;
      }

      await updateInventoryQuantity(
        item._id,
        quantity,
        type,
        reason || undefined,
        type === "transfer" ? transferToOutletId : undefined
      );

      toast.success(
        type === "addition"
          ? "Stock added successfully"
          : type === "deduction"
          ? "Stock deducted successfully"
          : type === "adjustment"
          ? "Stock adjusted successfully"
          : type === "waste"
          ? "Waste recorded successfully"
          : "Stock transferred successfully"
      );
      onClose(true);
    } catch (error) {
      console.error("Error updating inventory quantity:", error);
      toast.error("Failed to update inventory");
    } finally {
      setLoading(false);
    }
  };

  const handleOpenChange = (isOpen: boolean) => {
    if (!isOpen) {
      onClose(false);
    }
  };

  return (
    <Dialog open={true} onOpenChange={handleOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Update Inventory Quantity</DialogTitle>
          <DialogDescription>
            Update the quantity of {item.name}
          </DialogDescription>
        </DialogHeader>

        <div className="grid gap-4 py-4">
          <div className="space-y-2">
            <Label>Current Quantity</Label>
            <div className="text-lg font-medium">
              {item.quantity} {item.unit}
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="type">Action Type</Label>
            <Select value={type} onValueChange={handleTypeChange}>
              <SelectTrigger>
                <SelectValue placeholder="Select action type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="addition">Add Stock</SelectItem>
                <SelectItem value="deduction">Deduct Stock</SelectItem>
                <SelectItem value="adjustment">
                  Adjust Stock (Set Value)
                </SelectItem>
                <SelectItem value="waste">Record Waste</SelectItem>
                <SelectItem value="transfer">
                  Transfer to Another Outlet
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="quantity">
              {type === "adjustment" ? "New Quantity" : "Quantity"} ({item.unit}
              )
            </Label>
            <Input
              id="quantity"
              type="number"
              min="0"
              step="0.01"
              value={quantity}
              onChange={handleQuantityChange}
              placeholder={`Enter ${
                type === "adjustment" ? "new quantity" : "quantity"
              }`}
            />
          </div>

          {type === "transfer" && (
            <div className="space-y-2">
              <Label htmlFor="transferToOutletId">Destination Outlet</Label>
              <Select
                value={transferToOutletId}
                onValueChange={setTransferToOutletId}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select destination outlet" />
                </SelectTrigger>
                <SelectContent>
                  {outlets
                    .filter((outlet) => outlet._id !== item.outletId?._id)
                    .map((outlet) => (
                      <SelectItem key={outlet._id} value={outlet._id}>
                        {outlet.name}
                      </SelectItem>
                    ))}
                </SelectContent>
              </Select>
            </div>
          )}

          <div className="space-y-2">
            <Label htmlFor="reason">Reason (Optional)</Label>
            <Textarea
              id="reason"
              value={reason}
              onChange={(e) => setReason(e.target.value)}
              placeholder="Enter reason for this update"
              rows={2}
            />
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onClose(false)}>
            Cancel
          </Button>
          <Button onClick={handleSubmit} disabled={loading}>
            {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            {type === "addition"
              ? "Add Stock"
              : type === "deduction"
              ? "Deduct Stock"
              : type === "adjustment"
              ? "Set Quantity"
              : type === "waste"
              ? "Record Waste"
              : "Transfer Stock"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default InventoryQuantityDialog;
