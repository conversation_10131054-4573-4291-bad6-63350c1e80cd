"use client";

import React, { useState } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { toast } from "sonner";
import {
  Star,
  MessageSquare,
  ThumbsUp,
  ExternalLink,
  Loader2,
} from "lucide-react";

import { Separator } from "@/components/ui/separator";

interface CustomerFeedbackDialogProps {
  isOpen: boolean;
  onClose: () => void;
  orderId?: string;
  outletName?: string;
  onFeedbackSubmitted?: () => void;
}

interface FeedbackData {
  rating: number;
  comment: string;
  categories: string[];
}

const feedbackCategories = [
  { id: "food_quality", label: "Food Quality", icon: "🍽️" },
  { id: "service", label: "Service", icon: "👥" },
  { id: "delivery_time", label: "Delivery Time", icon: "⏰" },
  { id: "packaging", label: "Packaging", icon: "📦" },
  { id: "value_for_money", label: "Value for Money", icon: "💰" },
  { id: "overall_experience", label: "Overall Experience", icon: "⭐" },
];

export default function CustomerFeedbackDialog({
  isOpen,
  onClose,
  orderId,
  outletName,
  onFeedbackSubmitted,
}: CustomerFeedbackDialogProps) {
  const [feedback, setFeedback] = useState<FeedbackData>({
    rating: 0,
    comment: "",
    categories: [],
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showGoogleReview, setShowGoogleReview] = useState(false);

  const handleRatingClick = (rating: number) => {
    setFeedback((prev) => ({ ...prev, rating }));
  };

  const handleCategoryToggle = (categoryId: string) => {
    setFeedback((prev) => ({
      ...prev,
      categories: prev.categories.includes(categoryId)
        ? prev.categories.filter((id) => id !== categoryId)
        : [...prev.categories, categoryId],
    }));
  };

  const handleSubmitFeedback = async () => {
    if (feedback.rating === 0) {
      toast.error("Please provide a rating");
      return;
    }

    setIsSubmitting(true);
    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_BASE_URL}/api/v1/user/feedback/submit`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${localStorage.getItem("user-token")}`,
          },
          body: JSON.stringify({
            orderId,
            rating: feedback.rating,
            comment: feedback.comment,
            categories: feedback.categories,
            feedbackType: "order_experience",
            context: {
              page: "order_completion",
              outletName,
            },
          }),
        }
      );

      const result = await response.json();

      if (result.success) {
        toast.success("Thank you for your feedback!");
        
        // Show Google Review option for high ratings
        if (feedback.rating >= 4) {
          setShowGoogleReview(true);
        } else {
          handleClose();
        }
        
        if (onFeedbackSubmitted) {
          onFeedbackSubmitted();
        }
      } else {
        toast.error("Failed to submit feedback. Please try again.");
      }
    } catch (error) {
      console.error("Error submitting feedback:", error);
      toast.error("An error occurred while submitting feedback");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleGoogleReview = () => {
    // Open Google review page - this would need the actual Google Business profile URL
    const googleReviewUrl = `https://search.google.com/local/writereview?placeid=${process.env.NEXT_PUBLIC_GOOGLE_PLACE_ID || ''}`;
    window.open(googleReviewUrl, '_blank');
    handleClose();
  };

  const handleClose = () => {
    setFeedback({ rating: 0, comment: "", categories: [] });
    setShowGoogleReview(false);
    onClose();
  };

  if (showGoogleReview) {
    return (
      <Dialog open={isOpen} onOpenChange={handleClose}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <ThumbsUp className="h-5 w-5 text-green-500" />
              Thank You!
            </DialogTitle>
            <DialogDescription>
              We&apos;re thrilled you had a great experience! Would you mind sharing your experience on Google to help others discover us?
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            <div className="bg-green-50 p-4 rounded-lg text-center">
              <div className="flex justify-center mb-2">
                {[1, 2, 3, 4, 5].map((star) => (
                  <Star
                    key={star}
                    className="h-5 w-5 text-yellow-500 fill-current"
                  />
                ))}
              </div>
              <p className="text-sm text-green-700">
                Your {feedback.rating}-star rating means a lot to us!
              </p>
            </div>
          </div>

          <DialogFooter className="flex-col gap-2">
            <Button onClick={handleGoogleReview} className="w-full">
              <ExternalLink className="h-4 w-4 mr-2" />
              Leave Google Review
            </Button>
            <Button variant="outline" onClick={handleClose} className="w-full">
              Maybe Later
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-md max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <MessageSquare className="h-5 w-5 text-blue-500" />
            How was your experience?
          </DialogTitle>
          <DialogDescription>
            {outletName && `Your feedback helps ${outletName} improve their service.`}
            Your feedback helps us serve you better.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Rating Section */}
          <div className="space-y-2">
            <Label>Overall Rating *</Label>
            <div className="flex justify-center gap-1">
              {[1, 2, 3, 4, 5].map((star) => (
                <button
                  key={star}
                  type="button"
                  onClick={() => handleRatingClick(star)}
                  className="p-1 hover:scale-110 transition-transform"
                >
                  <Star
                    className={`h-8 w-8 ${
                      star <= feedback.rating
                        ? "text-yellow-500 fill-current"
                        : "text-gray-300"
                    }`}
                  />
                </button>
              ))}
            </div>
            {feedback.rating > 0 && (
              <p className="text-center text-sm text-gray-600">
                {feedback.rating === 1 && "Poor"}
                {feedback.rating === 2 && "Fair"}
                {feedback.rating === 3 && "Good"}
                {feedback.rating === 4 && "Very Good"}
                {feedback.rating === 5 && "Excellent"}
              </p>
            )}
          </div>

          <Separator />

          {/* Category Selection */}
          <div className="space-y-3">
            <Label>What would you like to comment on? (Optional)</Label>
            <div className="grid grid-cols-2 gap-2">
              {feedbackCategories.map((category) => (
                <button
                  key={category.id}
                  type="button"
                  onClick={() => handleCategoryToggle(category.id)}
                  className={`p-2 rounded-lg border text-left transition-colors ${
                    feedback.categories.includes(category.id)
                      ? "border-blue-500 bg-blue-50"
                      : "border-gray-200 hover:border-gray-300"
                  }`}
                >
                  <div className="flex items-center gap-2">
                    <span>{category.icon}</span>
                    <span className="text-sm">{category.label}</span>
                  </div>
                </button>
              ))}
            </div>
          </div>

          {/* Comment Section */}
          <div className="space-y-2">
            <Label htmlFor="comment">Additional Comments (Optional)</Label>
            <Textarea
              id="comment"
              placeholder="Tell us more about your experience..."
              value={feedback.comment}
              onChange={(e) =>
                setFeedback((prev) => ({ ...prev, comment: e.target.value }))
              }
              rows={3}
            />
          </div>
        </div>

        <DialogFooter className="flex-col gap-2">
          <Button
            onClick={handleSubmitFeedback}
            disabled={feedback.rating === 0 || isSubmitting}
            className="w-full"
          >
            {isSubmitting ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Submitting...
              </>
            ) : (
              "Submit Feedback"
            )}
          </Button>
          <Button variant="outline" onClick={handleClose} className="w-full">
            Skip for Now
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
