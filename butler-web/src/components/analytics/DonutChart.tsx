/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";
import React from "react";
import {
  <PERSON><PERSON><PERSON>,
  Pie,
  Cell,
  ResponsiveContainer,
  <PERSON><PERSON><PERSON>,
  Legend,
} from "recharts";

interface DonutChartProps {
  data: any[];
  dataKey: string;
  nameKey: string;
  colors?: string[];
  height?: number;
  innerRadius?: number;
  outerRadius?: number;
  showLegend?: boolean;
  showLabels?: boolean;
  formatTooltip?: (value: any, name: string) => [string, string];
  centerContent?: React.ReactNode;
}

const DEFAULT_COLORS = [
  "#8884d8",
  "#82ca9d",
  "#ffc658",
  "#ff7c7c",
  "#8dd1e1",
  "#d084d0",
  "#ffb347",
  "#87ceeb",
  "#dda0dd",
  "#98fb98",
];

const DonutChart: React.FC<DonutChartProps> = ({
  data,
  dataKey,
  nameKey,
  colors = DEFAULT_COLORS,
  height = 300,
  innerRadius = 60,
  outerRadius = 100,
  showLegend = true,
  showLabels = false,
  formatTooltip,
  centerContent,
}) => {
  // const isDark = theme === "dark";
  const isDark = false;

  const textColor = isDark ? "#d1d5db" : "#374151";

  const renderCustomLabel = (entry: any) => {
    if (!showLabels) return null;
    const percent = (
      (entry.value / data.reduce((sum, item) => sum + item[dataKey], 0)) *
      100
    ).toFixed(1);
    return `${percent}%`;
  };

  const total = data.reduce((sum, item) => sum + item[dataKey], 0);
  console.log(total, nameKey);

  return (
    <div style={{ height, position: "relative" }}>
      <ResponsiveContainer width="100%" height="100%">
        <PieChart>
          <Pie
            data={data}
            cx="50%"
            cy="50%"
            labelLine={false}
            label={renderCustomLabel}
            outerRadius={outerRadius}
            innerRadius={innerRadius}
            fill="#8884d8"
            dataKey={dataKey}
          >
            {data.map((_, index) => (
              <Cell
                key={`cell-${index}`}
                fill={colors[index % colors.length]}
              />
            ))}
          </Pie>

          <Tooltip
            formatter={
              formatTooltip || ((value: any, name: any) => [value, name])
            }
            contentStyle={{
              backgroundColor: isDark ? "#1f2937" : "#ffffff",
              border: `1px solid ${isDark ? "#374151" : "#e5e7eb"}`,
              borderRadius: "8px",
              color: textColor,
            }}
          />

          {showLegend && (
            <Legend
              verticalAlign="bottom"
              height={36}
              wrapperStyle={{ color: textColor }}
            />
          )}
        </PieChart>
      </ResponsiveContainer>

      {centerContent && (
        <div
          style={{
            position: "absolute",
            top: "50%",
            left: "50%",
            transform: "translate(-50%, -50%)",
            textAlign: "center",
            pointerEvents: "none",
          }}
        >
          {centerContent}
        </div>
      )}
    </div>
  );
};

export default DonutChart;
