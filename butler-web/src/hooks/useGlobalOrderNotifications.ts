/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";
import { useEffect } from "react";
import { useSocket } from "@/contexts/SocketContext";
import { toast } from "sonner";
import { usePathname } from "next/navigation";

interface OrderNotificationData {
  orderId: string;
  orderNumber: string;
  customerName: string;
  outletName: string;
  totalAmount: number;
  timestamp: string;
  message: string;
}

interface OrderUpdateData {
  order: {
    _id: string;
    orderNumber: string;
    userId?: { name: string };
    outletId?: { name: string };
    totalAmount: number;
    finalAmount: number;
  };
  updatedBy: string;
  timestamp: string;
}

export const useGlobalOrderNotifications = () => {
  const { adminSocket } = useSocket();
  const pathname = usePathname();
  
  // Check if we're on the orders page to avoid duplicate notifications
  const isOnOrdersPage = pathname?.includes('/admin/orders');

  useEffect(() => {
    if (!adminSocket || isOnOrdersPage) return;

    // Listen for new orders
    const handleNewOrder = (data: { type: string; data: any }) => {
      console.log("Global notification: New order received", data);
      if (data && data.data) {
        toast.success(`🆕 New Order #${data.data.orderNumber}`, {
          description: `From ${data.data.userId?.name || 'Customer'} at ${data.data.outletId?.name || 'Outlet'}`,
          action: {
            label: "View Orders",
            onClick: () => {
              window.open('/admin/orders', '_blank');
            },
          },
          duration: 8000,
        });

        // Play notification sound
        try {
          const audio = new Audio('/sounds/notification.mp3');
          audio.volume = 0.5;
          audio.play().catch(console.error);
        } catch (error) {
          console.error('Error playing notification sound:', error);
        }

        // Show browser notification if permission granted
        if (window.Notification && Notification.permission === "granted") {
          new Notification("New Order Received", {
            body: `Order #${data.data.orderNumber} from ${data.data.userId?.name || 'Customer'}`,
            icon: "/favicon.ico",
            tag: `new-order-${data.data._id}`,
          });
        }
      }
    };

    // Listen for order status updates
    const handleOrderStatusUpdate = (data: { data: any }) => {
      console.log("Global notification: Order status updated", data);
      if (data && data.data) {
        toast.info(`📋 Order #${data.data.orderNumber} Status Updated`, {
          description: `Status changed to: ${data.data.status}`,
          action: {
            label: "View Orders",
            onClick: () => {
              window.open('/admin/orders', '_blank');
            },
          },
          duration: 5000,
        });
      }
    };

    // Listen for order items updates
    const handleOrderItemsUpdate = (data: {
      type: string;
      data: OrderUpdateData;
    }) => {
      console.log("Global notification: Order items updated", data);
      if (data && data.data && data.data.order) {
        const { order, updatedBy } = data.data;
        const updatedByText = updatedBy === 'user' ? 'customer' : 'admin';
        
        toast.info(`🔄 Order #${order.orderNumber} Updated`, {
          description: `Modified by ${updatedByText}. Total: ₹${order.finalAmount || order.totalAmount}`,
          action: {
            label: "View Orders",
            onClick: () => {
              window.open('/admin/orders', '_blank');
            },
          },
          duration: 6000,
        });

        // Play notification sound for customer updates
        if (updatedBy === 'user') {
          try {
            const audio = new Audio('/sounds/notification.mp3');
            audio.volume = 0.3;
            audio.play().catch(console.error);
          } catch (error) {
            console.error('Error playing notification sound:', error);
          }
        }
      }
    };

    // Listen for order update notifications (customer updates)
    const handleOrderUpdateNotification = (data: {
      type: string;
      data: OrderNotificationData;
    }) => {
      console.log("Global notification: Order update notification", data);
      if (data && data.data) {
        toast.warning(`⚠️ Customer Updated Order`, {
          description: `${data.data.message} - Total: ₹${data.data.totalAmount}`,
          action: {
            label: "View Orders",
            onClick: () => {
              window.open('/admin/orders', '_blank');
            },
          },
          duration: 10000,
        });

        // Play notification sound
        try {
          const audio = new Audio('/sounds/notification.mp3');
          audio.volume = 0.7;
          audio.play().catch(console.error);
        } catch (error) {
          console.error('Error playing notification sound:', error);
        }

        // Show browser notification
        if (window.Notification && Notification.permission === "granted") {
          new Notification("Order Updated by Customer", {
            body: data.data.message,
            icon: "/favicon.ico",
            tag: `order-update-${data.data.orderId}`,
          });
        }
      }
    };

    // Listen for admin order update notifications
    const handleAdminOrderUpdateNotification = (data: {
      type: string;
      data: OrderNotificationData;
    }) => {
      console.log("Global notification: Admin order update notification", data);
      if (data && data.data) {
        toast.info(`👨‍💼 Admin Updated Order`, {
          description: `${data.data.message} - Total: ₹${data.data.totalAmount}`,
          action: {
            label: "View Orders",
            onClick: () => {
              window.open('/admin/orders', '_blank');
            },
          },
          duration: 5000,
        });
      }
    };

    // Listen for payment updates
    const handlePaymentUpdate = (data: {
      type: string;
      data: { order: any; payment: any };
    }) => {
      console.log("Global notification: Payment updated", data);
      if (data && data.data && data.data.order) {
        toast.success(`💳 Payment Updated`, {
          description: `Order #${data.data.order.orderNumber} - Status: ${data.data.order.paymentStatus}`,
          action: {
            label: "View Orders",
            onClick: () => {
              window.open('/admin/orders', '_blank');
            },
          },
          duration: 5000,
        });
      }
    };

    // Register event listeners
    adminSocket.on("new-order", handleNewOrder);
    adminSocket.on("order-status-update", handleOrderStatusUpdate);
    adminSocket.on("order-items-update", handleOrderItemsUpdate);
    adminSocket.on("order-update-notification", handleOrderUpdateNotification);
    adminSocket.on("admin-order-update-notification", handleAdminOrderUpdateNotification);
    adminSocket.on("payment-update", handlePaymentUpdate);

    // Cleanup function
    return () => {
      adminSocket.off("new-order", handleNewOrder);
      adminSocket.off("order-status-update", handleOrderStatusUpdate);
      adminSocket.off("order-items-update", handleOrderItemsUpdate);
      adminSocket.off("order-update-notification", handleOrderUpdateNotification);
      adminSocket.off("admin-order-update-notification", handleAdminOrderUpdateNotification);
      adminSocket.off("payment-update", handlePaymentUpdate);
    };
  }, [adminSocket, isOnOrdersPage, pathname]);

  // Request notification permission on mount
  useEffect(() => {
    if (typeof window !== 'undefined' && window.Notification && Notification.permission === "default") {
      Notification.requestPermission().then((permission) => {
        console.log("Notification permission:", permission);
      });
    }
  }, []);
};
