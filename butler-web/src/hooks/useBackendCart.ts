/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";

import { Dish, Offer } from "@/app/type";
import { useEffect, useState, useCallback, useMemo } from "react";
import { useSearchParams } from "next/navigation";
import {
  getCart,
  addToCart as addToCartAPI,
  removeFromCart as removeFromCartAPI,
  updateCartItemQuantity as updateCartItemQuantityAPI,
  clearCart as clearCartAPI,
  migrateLocalStorageCart,
  isAuthenticated,
} from "@/server/cart";
import { toast } from "sonner";

interface CartData {
  items: Dish[];
  appliedOffers: Offer[];
  subtotal: number;
  totalOfferDiscount: number;
  totalCouponDiscount: number;
  totalDiscount: number;
  finalTotal: number;
  taxAmount: number;
  deliveryFee: number;
  packagingFee: number;
  updatedAt: string;
}

const useBackendCart = () => {
  const [cart, setCart] = useState<Dish[]>([]);
  const [appliedOffers, setAppliedOffers] = useState<Offer[]>([]);
  const [cartTotals, setCartTotals] = useState({
    subtotal: 0,
    totalOfferDiscount: 0,
    totalCouponDiscount: 0,
    totalDiscount: 0,
    finalTotal: 0,
    taxAmount: 0,
    deliveryFee: 0,
    packagingFee: 0,
  });

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const params = useSearchParams();

  const foodChainId = useMemo(() => {
    return params?.get("chainId") || localStorage.getItem("chainId") || "";
  }, [params]);

  const outletId = useMemo(() => {
    return params?.get("outletId") || localStorage.getItem("outletId") || "";
  }, [params]);

  // --------------------------------------------------------
  // 🧠 Transform backend cart → frontend cart
  // --------------------------------------------------------

  const transformItems = (items: any[]): Dish[] => {
    return items.map((item) => {
      const dishData =
        item.dishId && typeof item.dishId === "object"
          ? item.dishId
          : item;

      return {
        _id: dishData._id || item.dishId,
        name: dishData.name || item.dishName,
        price: dishData.price || item.price,
        quantity: item.quantity,
        image: dishData.image,
        isVeg: dishData.isVeg,
        category: dishData.category,
        description: dishData.description || "",
        foodChain: dishData.foodChain || "",
        outlets: dishData.outlets || [],
        isAvailable:
          dishData.isAvailable !== undefined ? dishData.isAvailable : true,

        selectedAddOns: (item.addOns || []).map((a: any) => ({
          addOnId: a.addOnId || a._id,
          name: a.name,
          price: a.price,
          quantity: a.quantity || 1,
        })),

        enabledAddOns: dishData.enabledAddOns || [],
      };
    });
  };

  const updateCartState = useCallback((cartData: CartData) => {
    setCart(transformItems(cartData.items || []));
    setAppliedOffers(cartData.appliedOffers || []);

    setCartTotals({
      subtotal: cartData.subtotal ?? 0,
      totalOfferDiscount: cartData.totalOfferDiscount ?? 0,
      totalCouponDiscount: cartData.totalCouponDiscount ?? 0,
      totalDiscount: cartData.totalDiscount ?? 0,
      finalTotal: cartData.finalTotal ?? 0,
      taxAmount: cartData.taxAmount ?? 0,
      deliveryFee: cartData.deliveryFee ?? 0,
      packagingFee: cartData.packagingFee ?? 0,
    });
  }, []);

  // --------------------------------------------------------
  // 🔄 Load Cart
  // --------------------------------------------------------

  const loadCart = useCallback(
    async (silent = false) => {
      if (!isAuthenticated()) return;
      if (!foodChainId || !outletId) return;

      if (!silent) setLoading(true);
      setError(null);

      try {
        const migrated = await migrateLocalStorageCart(foodChainId, outletId);

        if (migrated.success && migrated.data) {
          updateCartState(migrated.data);
          return;
        }

        const result = await getCart(foodChainId, outletId);
        if (result.success && result.data) {
          updateCartState(result.data);
        } else {
          setError(result.message || "Failed to load cart");
        }
      } catch (err:any) {
        console.log(err);
        setError("Failed to load cart");
      } finally {
        if (!silent) setLoading(false);
      }
    },
    [foodChainId, outletId, updateCartState]
  );

  useEffect(() => {
    loadCart();
  }, [loadCart]);

  // --------------------------------------------------------
  // ➕ Add to Cart
  // --------------------------------------------------------

  const addToCart = useCallback(
    async (dish: Dish, quantity = 1, addOns?: any[]) => {
      if (!isAuthenticated()) return toast.error("Please login"), false;
      if (!foodChainId || !outletId)
        return toast.error("Missing chain/outlet info"), false;

      setLoading(true);
      setError(null);

      try {
        const result = await addToCartAPI(
          dish._id!,
          quantity,
          foodChainId,
          outletId,
          addOns
        );

        if (result.success && result.data) {
          updateCartState(result.data);
          return true;
        }

        toast.error(result.message);
        return false;
      } catch {
        toast.error("Failed to add item");
        return false;
      } finally {
        setLoading(false);
      }
    },
    [foodChainId, outletId, updateCartState]
  );

  // --------------------------------------------------------
  // ❌ Remove From Cart
  // --------------------------------------------------------

  const removeFromCart = useCallback(
    async (dishId: string) => {
      if (!isAuthenticated()) return toast.error("Please login"), false;
      if (!foodChainId || !outletId)
        return toast.error("Missing chain/outlet info"), false;

      setLoading(true);

      try {
        const result = await removeFromCartAPI(dishId, foodChainId, outletId);
        if (result.success && result.data) {
          updateCartState(result.data);
          toast.success("Item removed");
          return true;
        }
        toast.error(result.message);
        return false;
      } finally {
        setLoading(false);
      }
    },
    [foodChainId, outletId, updateCartState]
  );

  // --------------------------------------------------------
  // 🔁 Update Quantity
  // --------------------------------------------------------

  const updateItemQuantity = useCallback(
    async (dishId: string, quantity: number) => {
      if (!isAuthenticated()) return toast.error("Please login"), false;
      if (!foodChainId || !outletId)
        return toast.error("Missing chain/outlet info"), false;

      setLoading(true);

      try {
        const result = await updateCartItemQuantityAPI(
          dishId,
          quantity,
          foodChainId,
          outletId
        );

        if (result.success && result.data) {
          updateCartState(result.data);
          return true;
        }

        toast.error(result.message);
        return false;
      } finally {
        setLoading(false);
      }
    },
    [foodChainId, outletId, updateCartState]
  );

  // --------------------------------------------------------
  // 🗑 Clear Cart
  // --------------------------------------------------------

  const clearCart = useCallback(async () => {
    if (!isAuthenticated()) return toast.error("Please login"), false;
    if (!foodChainId || !outletId)
      return toast.error("Missing chain/outlet info"), false;

    setLoading(true);

    try {
      const result = await clearCartAPI(foodChainId, outletId);
      if (result.success) {
        setCart([]);
        setAppliedOffers([]);
        setCartTotals({
          subtotal: 0,
          totalOfferDiscount: 0,
          totalCouponDiscount: 0,
          totalDiscount: 0,
          finalTotal: 0,
          taxAmount: 0,
          deliveryFee: 0,
          packagingFee: 0,
        });
        toast.success("Cart cleared");
        return true;
      }
      toast.error(result.message);
      return false;
    } finally {
      setLoading(false);
    }
  }, [foodChainId, outletId]);

  // --------------------------------------------------------
  // 🔄 Auto-Refresh & Page Visibility Refresh
  // --------------------------------------------------------

  const refreshCart = useCallback((silent = false) => {
    return loadCart(silent);
  }, [loadCart]);

  useEffect(() => {
    const interval = setInterval(() => {
      if (document.visibilityState === "visible" && isAuthenticated()) {
        refreshCart(true);
      }
    }, 30000);

    return () => clearInterval(interval);
  }, [refreshCart]);

  useEffect(() => {
    const handler = () => {
      if (document.visibilityState === "visible" && isAuthenticated()) {
        refreshCart(true);
      }
    };
    document.addEventListener("visibilitychange", handler);
    return () => document.removeEventListener("visibilitychange", handler);
  }, [refreshCart]);

  // --------------------------------------------------------
  // 📦 Helpers
  // --------------------------------------------------------

  const getCartItemCount = useMemo(
    () => cart.reduce((t, i) => t + (i.quantity || 1), 0),
    [cart]
  );

  const isItemInCart = useCallback(
    (id: string) => cart.some((i) => i._id === id),
    [cart]
  );

  const getItemQuantity = useCallback(
    (id: string) => cart.find((i) => i._id === id)?.quantity || 0,
    [cart]
  );

  return {
    cart,
    appliedOffers,
    cartTotals,
    loading,
    error,
    addToCart,
    removeFromCart,
    updateItemQuantity,
    clearCart,
    refreshCart,
    getCartItemCount,
    isItemInCart,
    getItemQuantity,
    // Legacy
    setCart: () => console.warn("setCart is deprecated."),
    setAppliedOffers: () => console.warn("setAppliedOffers is deprecated."),
  };
};

export default useBackendCart;
