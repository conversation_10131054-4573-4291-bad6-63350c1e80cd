"use client";
import React, { useState, useEffect } from "react";
import { useParams, useRouter } from "next/navigation";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  PhoneCall,
  Mail,
  Calendar,
  Store,
  MapPin,
  Users,
  Edit,
  Trash,
  ArrowLeft,
  Share2,
  Menu,
  Globe,
  MessageSquare,
  UserPlus,
  Loader2,
  X,
  RotateCcw,
  Save,
} from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { format } from "date-fns";
import {
  createChainAdmin,
  getSingle<PERSON>hain,
  update<PERSON>hain,
} from "@/server/super-admin";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  SheetFooter,
} from "@/components/ui/sheet";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { toast } from "sonner";
import { FoodChain } from "@/app/type";

const ChainViewPage = () => {
  const router = useRouter();
  const { chainId } = useParams() || {};
  const [newAdmin, setNewAdmin] = useState(false);
  const [loading, setLoading] = useState(true);
  const [enableEdit, setEnableEdit] = useState(false);
  const [chain, setChain] = useState<FoodChain>({} as FoodChain);
  const [resetChain, setResetChain] = useState<FoodChain>({} as FoodChain);
  const [newAdminDetails, setNewAdminDetails] = useState({
    name: "",
    email: "",
    password: "",
    phone: "",
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleChainInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { id, value } = e.target;
    setChain((prev) => ({
      ...prev,
      [id]: value,
    }));
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { id, value } = e.target;
    setNewAdminDetails((prev) => ({
      ...prev,
      [id]: value,
    }));
  };

  const handleCreateAdmin = async () => {
    // Validate inputs
    if (
      !newAdminDetails.name ||
      !newAdminDetails.email ||
      !newAdminDetails.password
    ) {
      return toast.error("Please fill in all required fields");
    }

    if (!newAdminDetails.email.includes("@")) {
      return toast.error("Please enter a valid email address");
    }

    if (newAdminDetails.password.length < 6) {
      return toast.error("Password must be at least 6 characters long");
    }

    try {
      setIsSubmitting(true);

      // Add your API call here
      const response = await createChainAdmin({
        ...newAdminDetails,
        foodChainId: chainId as string,
      });

      if (!response.success) {
        return toast.error(response.message || "Failed to create admin");
      }

      toast.success("Admin created successfully");
      setNewAdmin(false);
      setNewAdminDetails({
        name: "",
        email: "",
        phone: "",
        password: "",
      });

      // Optionally refresh the page or update the admin list
      // router.refresh();
    } catch (error) {
      console.error(error);
      toast.error("Failed to create admin");
    } finally {
      setIsSubmitting(false);
    }
  };

  const fetchChainData = async () => {
    try {
      const res = await getSingleChain(chainId as string);
      setChain({ ...res.data, _id: chainId as string });
      setResetChain({ ...res.data, _id: chainId as string });
      setLoading(false);
    } catch (error) {
      console.error("Error fetching chain data:", error);
      setLoading(false);
    }
  };

  const handleSave = async () => {
    setLoading(true);
    const res = await updateChain(chain);
    if (res.success) {
      toast.success("Chain updated successfully");
      setResetChain(chain);
      setEnableEdit(false);
    } else {
      toast.error(res.response.data.error || "Failed to update chain");
    }
    setLoading(false);
  };

  useEffect(() => {
    if (chainId) fetchChainData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [chainId]);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-center">
          <div className="animate-spin h-12 w-12 border-4 border-gray-200 rounded-full border-t-primary mx-auto mb-4"></div>
          <p>Loading chain details...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6 px-4 max-w-6xl">
      {/* Header with back button */}
      <div className="flex items-center gap-4 mb-6">
        <Button
          variant="ghost"
          size="icon"
          className="rounded-full"
          onClick={() => router.back()}
        >
          <ArrowLeft className="h-5 w-5" />
        </Button>
        <h1 className="text-3xl font-bold">Chain Details</h1>
      </div>

      {/* Main info card */}
      <Card
        className="mb-6 overflow-hidden border-t-4"
        style={{ borderTopColor: chain.theme?.primaryColor }}
      >
        <CardHeader className="p-6 pb-3">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <div className="flex items-center gap-4">
              <div
                className="h-16 w-16 rounded-lg flex items-center justify-center text-white text-2xl font-bold shadow-md"
                style={{ backgroundColor: chain.theme?.primaryColor }}
              >
                {
                  // chain.theme.favIcon ||
                  chain?.name?.charAt(0).toUpperCase()
                }
              </div>
              <div>
                <CardTitle className="text-2xl">
                  {enableEdit ? (
                    <Input
                      placeholder="Chain Name..."
                      className="h-12"
                      value={chain.name}
                      onChange={handleChainInputChange}
                      id="name"
                    />
                  ) : (
                    chain.name
                  )}
                </CardTitle>
                <CardDescription className="text-base mt-1">
                  {enableEdit ? (
                    <Input
                      placeholder="Chain Tagline..."
                      value={chain.tagline || ""}
                      onChange={handleChainInputChange}
                      id="tagline"
                    />
                  ) : (
                    chain.tagline || "No tagline provided"
                  )}
                </CardDescription>
              </div>
            </div>
            {enableEdit ? (
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  className="flex items-center gap-2"
                  onClick={() => {
                    setChain(resetChain);
                  }}
                >
                  <RotateCcw className="h-4 w-4" />
                  <span>Reset</span>
                </Button>
                <Button
                  variant="outline"
                  className="flex items-center gap-2"
                  onClick={() => {
                    setChain(resetChain);
                    setEnableEdit(false);
                  }}
                >
                  <X className="h-4 w-4" />
                  <span>Cancel</span>
                </Button>
                <Button
                  className="flex items-center gap-2"
                  onClick={() => handleSave()}
                >
                  <Save className="h-4 w-4" />
                  <span>Save</span>
                </Button>
              </div>
            ) : (
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  className="flex items-center gap-2"
                  onClick={() => setNewAdmin(true)}
                >
                  <UserPlus className="h-4 w-4" />
                  <span>Create</span>
                </Button>
                <Button
                  variant="outline"
                  className="flex items-center gap-2"
                  onClick={() => setEnableEdit(true)}
                >
                  <Edit className="h-4 w-4" />
                  <span>Edit</span>
                </Button>
                <Button
                  variant="outline"
                  className="flex items-center gap-2 text-destructive hover:bg-destructive/10"
                >
                  <Trash className="h-4 w-4" />
                  <span>Delete</span>
                </Button>
              </div>
            )}
          </div>
        </CardHeader>

        <CardContent className="p-6 pt-3">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Contact Information</h3>
              <div className="space-y-3">
                <div className="flex items-center gap-3">
                  <div className="bg-muted h-8 w-8 rounded-full flex items-center justify-center">
                    <PhoneCall className="h-4 w-4 text-muted-foreground" />
                  </div>
                  <div>
                    <div className="text-sm text-muted-foreground">Phone</div>
                    <div>
                      {enableEdit ? (
                        <Input
                          placeholder="Phone..."
                          type="number"
                          value={chain.contact}
                          onChange={handleChainInputChange}
                          id="contact"
                        />
                      ) : (
                        chain.contact
                      )}
                    </div>
                  </div>
                </div>

                <div className="flex items-center gap-3">
                  <div className="bg-muted h-8 w-8 rounded-full flex items-center justify-center">
                    <Mail className="h-4 w-4 text-muted-foreground" />
                  </div>
                  <div>
                    <div className="text-sm text-muted-foreground">Email</div>
                    <div>
                      {enableEdit ? (
                        <Input
                          placeholder="Email..."
                          value={chain.email}
                          onChange={handleChainInputChange}
                          id="email"
                        />
                      ) : (
                        chain.email
                      )}
                    </div>
                  </div>
                </div>

                {
                  <div className="flex items-center gap-3">
                    <div className="bg-muted h-8 w-8 rounded-full flex items-center justify-center">
                      <Globe className="h-4 w-4 text-muted-foreground" />
                    </div>
                    <div>
                      <div className="text-sm text-muted-foreground">
                        Website
                      </div>
                      <div>
                        {enableEdit ? (
                          <Input
                            placeholder="Website..."
                            id="website"
                            onChange={handleChainInputChange}
                            value={chain.website}
                          />
                        ) : (
                          chain?.website ||
                          window.location.origin + "/" + chain._id
                        )}
                      </div>
                    </div>
                  </div>
                }

                <div className="flex items-center gap-3">
                  <div className="bg-muted h-8 w-8 rounded-full flex items-center justify-center">
                    <Calendar className="h-4 w-4 text-muted-foreground" />
                  </div>
                  <div>
                    <div className="text-sm text-muted-foreground">
                      Created On
                    </div>
                    <div>
                      {format(new Date(chain.createdAt), "MMMM d, yyyy")}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Overview</h3>
              <div className="grid grid-cols-2 gap-4">
                <Card className="bg-muted/30">
                  <CardContent className="p-4 flex flex-col items-center justify-center text-center">
                    <Store className="h-6 w-6 mb-2 text-primary" />
                    <div className="text-2xl font-bold">
                      {chain.outlets.length}
                    </div>
                    <div className="text-sm text-muted-foreground">Outlets</div>
                  </CardContent>
                </Card>

                <Card className="bg-muted/30">
                  <CardContent className="p-4 flex flex-col items-center justify-center text-center">
                    <Users className="h-6 w-6 mb-2 text-primary" />
                    <div className="text-2xl font-bold">
                      {chain.stats?.totalCustomers || "N/A"}
                    </div>
                    <div className="text-sm text-muted-foreground">
                      Customers
                    </div>
                  </CardContent>
                </Card>

                <Card className="bg-muted/30">
                  <CardContent className="p-4 flex flex-col items-center justify-center text-center">
                    <Menu className="h-6 w-6 mb-2 text-primary" />
                    <div className="text-2xl font-bold">
                      {chain.stats?.totalOrders || "N/A"}
                    </div>
                    <div className="text-sm text-muted-foreground">Orders</div>
                  </CardContent>
                </Card>

                <Card className="bg-muted/30">
                  <CardContent className="p-4 flex flex-col items-center justify-center text-center">
                    <MessageSquare className="h-6 w-6 mb-2 text-primary" />
                    <div className="text-2xl font-bold">
                      {chain.stats?.avgOrderValue || "N/A"}
                    </div>
                    <div className="text-sm text-muted-foreground">
                      Avg. Order
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>

          <Separator className="my-6" />

          <div>
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold">Brand Colors</h3>
              <Badge variant="outline" className="font-mono">
                Theme ID
              </Badge>
            </div>
            {enableEdit ? (
              <div className="p-4 border rounded-lg shadow-sm bg-white">
                <h3 className="text-lg font-medium mb-3">Theme Colors</h3>
                <div className="space-y-4">
                  <div className="flex items-center gap-4">
                    <label className="w-24 text-sm font-medium text-gray-600">
                      Primary
                    </label>
                    <div className="flex items-center gap-2">
                      <Input
                        type="color"
                        className="w-12 h-12 p-1 rounded-md cursor-pointer border border-gray-200 hover:border-gray-300 transition-colors"
                        value={chain.theme.primaryColor}
                        onChange={(e) =>
                          setChain((prev) => ({
                            ...prev,
                            theme: {
                              ...prev.theme,
                              primaryColor: e.target.value,
                            },
                          }))
                        }
                        aria-label="Primary color"
                      />
                      <div className="text-sm text-gray-500">
                        {chain.theme.primaryColor}
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center gap-4">
                    <label className="w-24 text-sm font-medium text-gray-600">
                      Secondary
                    </label>
                    <div className="flex items-center gap-2">
                      <Input
                        type="color"
                        className="w-12 h-12 p-1 rounded-md cursor-pointer border border-gray-200 hover:border-gray-300 transition-colors"
                        value={chain.theme.secondaryColor}
                        onChange={(e) =>
                          setChain((prev) => ({
                            ...prev,
                            theme: {
                              ...prev.theme,
                              secondaryColor: e.target.value,
                            },
                          }))
                        }
                        aria-label="Secondary color"
                      />
                      <div className="text-sm text-gray-500">
                        {chain.theme.secondaryColor}
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center gap-4">
                    <label className="w-24 text-sm font-medium text-gray-600">
                      Accent
                    </label>
                    <div className="flex items-center gap-2">
                      <Input
                        type="color"
                        className="w-12 h-12 p-1 rounded-md cursor-pointer border border-gray-200 hover:border-gray-300 transition-colors"
                        defaultValue={chain.theme.accentColor}
                        onChange={(e) =>
                          setChain((prev) => ({
                            ...prev,
                            theme: {
                              ...prev.theme,
                              accentColor: e.target.value,
                            },
                          }))
                        }
                        aria-label="Accent color"
                      />
                      <div className="text-sm text-gray-500">
                        {chain.theme.accentColor}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ) : (
              <div className="flex items-center gap-2">
                <div
                  className="h-8 w-8 rounded-full"
                  style={{ backgroundColor: chain.theme.primaryColor }}
                ></div>
                <div
                  className="h-8 w-8 rounded-full"
                  style={{ backgroundColor: chain.theme.secondaryColor }}
                ></div>
                <div
                  className="h-8 w-8 rounded-full"
                  style={{ backgroundColor: chain.theme.accentColor }}
                ></div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Outlet cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {chain.outlets.map((outlet) => (
          <Card key={outlet._id} className="overflow-hidden">
            <CardHeader className="p-4">
              <div className="flex items-center gap-3">
                <div className="bg-muted h-12 w-12 rounded-lg flex items-center justify-center">
                  <MapPin className="h-6 w-6 text-muted-foreground" />
                </div>
                <div>
                  <div className="text-sm text-muted-foreground">Outlet</div>
                  <div>{outlet.name}</div>
                </div>
              </div>
              <CardFooter className="p-4 flex justify-end gap-2">
                <Button variant="ghost" size="icon" className="h-8 w-8">
                  <Share2 className="h-4 w-4" />
                </Button>
                <Button variant="ghost" size="icon" className="h-8 w-8">
                  <Edit className="h-4 w-4" />
                </Button>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-8 w-8 text-destructive"
                >
                  <Trash className="h-4 w-4" />
                </Button>
              </CardFooter>
            </CardHeader>
          </Card>
        ))}
      </div>

      {/* New admin creation */}
      <Sheet open={newAdmin} onOpenChange={setNewAdmin}>
        <SheetContent>
          <SheetHeader>
            <SheetTitle>Add New Admin</SheetTitle>
            <SheetDescription>
              Create a new admin account for this food chain
            </SheetDescription>
          </SheetHeader>

          <div className="grid gap-4 py-4 px-3">
            <div className="grid gap-2">
              <Label htmlFor="name">Name</Label>
              <Input
                id="name"
                value={newAdminDetails.name}
                onChange={handleInputChange}
                placeholder="Enter admin name"
              />
            </div>

            <div className="grid gap-2">
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                type="email"
                value={newAdminDetails.email}
                onChange={handleInputChange}
                placeholder="<EMAIL>"
              />
            </div>

            <div className="grid gap-2">
              <Label htmlFor="phone">Phone</Label>
              <Input
                id="phone"
                type="tel"
                value={newAdminDetails.phone}
                onChange={handleInputChange}
                placeholder="+91 98765 43210"
              />
            </div>

            <div className="grid gap-2">
              <Label htmlFor="password">Password</Label>
              <Input
                id="password"
                type="password"
                value={newAdminDetails.password}
                onChange={handleInputChange}
                placeholder="Enter password"
              />
            </div>
          </div>

          <SheetFooter>
            <Button
              type="submit"
              onClick={handleCreateAdmin}
              disabled={isSubmitting}
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Creating...
                </>
              ) : (
                "Create Admin"
              )}
            </Button>
          </SheetFooter>
        </SheetContent>
      </Sheet>
    </div>
  );
};

export default ChainViewPage;
