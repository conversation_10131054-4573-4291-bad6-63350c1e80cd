"use client";
import {
  getAllCustomers,
  updateCustomerStatus,
  createCustomer,
} from "@/server/admin";
import React, { useEffect, useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Indian } from "@/lib/currency";
import { format } from "date-fns";
import {
  Search,
  UserCog,
  ShieldAlert,
  ShieldCheck,
  Loader2,
  UserPlus,
  Filter,
  SortAsc,
  SortDesc,
} from "lucide-react";
import { Input } from "@/components/ui/input";
import { toast } from "sonner";
import CustomerDetailsDialog from "@/components/custom/customers/CustomerDetailsDialog";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface Customer {
  _id: string;
  name: string;
  phone?: string;
  email: string;
  totalOrders?: number;
  totalSpent?: number;
  averageOrderValue?: number;
  lastOrderDate?: string;
  customerStatus?: "active" | "at-risk" | "inactive" | "new";
  daysSinceLastOrder?: number;
  status?: "active" | "blocked";
  createdAt?: string;
}

const Customers = () => {
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [loading, setLoading] = useState(true);
  const [selectedCustomer, setSelectedCustomer] = useState<string | null>(null);
  const [showDetailsDialog, setShowDetailsDialog] = useState(false);
  const [statusUpdating, setStatusUpdating] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalCustomers, setTotalCustomers] = useState(0);
  const [limit, setLimit] = useState(20);
  const [debouncedSearch, setDebouncedSearch] = useState("");
  const [sortBy, setSortBy] = useState<string>("createdAt");
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc");
  const [secondarySortBy, setSecondarySortBy] = useState<string>("");
  const [secondarySortOrder, setSecondarySortOrder] = useState<"asc" | "desc">(
    "desc"
  );
  const [statusFilter, setStatusFilter] = useState<string>("all");

  // Customer creation dialog state
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [newCustomer, setNewCustomer] = useState({
    name: "",
    email: "",
    phone: "",
  });
  const [creatingCustomer, setCreatingCustomer] = useState(false);

  // Debounce search term
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearch(searchTerm);
    }, 500);

    return () => clearTimeout(timer);
  }, [searchTerm]);

  // Fetch customers when page, limit, or search changes
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        const response = await getAllCustomers({
          page: currentPage,
          limit,
          search: debouncedSearch,
          sortBy,
          sortOrder,
          secondarySortBy: secondarySortBy || undefined,
          secondarySortOrder,
          status: statusFilter !== "all" ? statusFilter : undefined,
        });
        console.log("Customer response:", response);

        if (response.success) {
          // Handle the new response format
          if (response.data && response.data.customers) {
            setCustomers(response.data.customers);
            if (response.data.pagination) {
              setTotalPages(response.data.pagination.pages);
              setTotalCustomers(response.data.pagination.total);
            }
          } else if (Array.isArray(response.data)) {
            // Handle old format for backward compatibility
            setCustomers(response.data);
            if (response.pagination) {
              setTotalPages(response.pagination.pages);
              setTotalCustomers(response.pagination.total);
            }
          } else {
            console.error("Unexpected data format:", response.data);
            setCustomers([]);
          }
        } else {
          toast.error(response.message || "Failed to fetch customers");
        }
      } catch (error) {
        console.error("Error fetching customers:", error);
        toast.error("An error occurred while fetching customers");
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [
    currentPage,
    limit,
    debouncedSearch,
    sortBy,
    sortOrder,
    secondarySortBy,
    secondarySortOrder,
    statusFilter,
  ]);

  // Initial fetch
  useEffect(() => {
    // Initial fetch is handled by the other useEffect
  }, []);

  const handleViewDetails = (customerId: string) => {
    setSelectedCustomer(customerId);
    setShowDetailsDialog(true);
  };

  const handleStatusChange = async (
    customerId: string,
    newStatus: "active" | "blocked"
  ) => {
    try {
      setStatusUpdating(customerId);
      const response = await updateCustomerStatus(customerId, newStatus);

      if (response.success) {
        toast.success(
          `Customer ${
            newStatus === "blocked" ? "blocked" : "activated"
          } successfully`
        );

        // Update the customer in the local state
        setCustomers(
          customers.map((customer) =>
            customer._id === customerId
              ? { ...customer, status: newStatus }
              : customer
          )
        );
      } else {
        toast.error(
          response.message ||
            `Failed to ${
              newStatus === "blocked" ? "block" : "activate"
            } customer`
        );
      }
    } catch (error) {
      console.error(
        `Error ${
          newStatus === "blocked" ? "blocking" : "activating"
        } customer:`,
        error
      );
      toast.error(
        `An error occurred while ${
          newStatus === "blocked" ? "blocking" : "activating"
        } customer`
      );
    } finally {
      setStatusUpdating(null);
    }
  };

  const getStatusColor = (status: string) => {
    const colors = {
      active: "bg-green-100 text-green-800",
      "at-risk": "bg-yellow-100 text-yellow-800",
      inactive: "bg-red-100 text-red-800",
      new: "bg-blue-100 text-blue-800",
    };
    return colors[status as keyof typeof colors] || "bg-gray-100 text-gray-800";
  };

  // Function to handle page change
  const handlePageChange = (newPage: number) => {
    setCurrentPage(newPage);
  };

  // Function to handle limit change
  const handleLimitChange = (newLimit: number) => {
    setLimit(newLimit);
    setCurrentPage(1); // Reset to first page when changing limit
  };

  // Function to refresh data after customer update
  const refreshCustomers = () => {
    // This will trigger the useEffect that fetches customers
    const currentTimestamp = new Date().getTime();
    setDebouncedSearch(debouncedSearch + `?refresh=${currentTimestamp}`);
    setTimeout(() => setDebouncedSearch(debouncedSearch), 100);
  };

  // Handle customer creation
  const handleCreateCustomer = async () => {
    if (!newCustomer.name || !newCustomer.email) {
      toast.error("Name and email are required");
      return;
    }

    setCreatingCustomer(true);
    try {
      const response = await createCustomer({
        name: newCustomer.name,
        email: newCustomer.email,
        phone: newCustomer.phone,
      });

      if (response.success) {
        toast.success("Customer created successfully");
        setShowCreateDialog(false);
        setNewCustomer({ name: "", email: "", phone: "" });
        refreshCustomers();
      } else {
        toast.error(response.message || "Failed to create customer");
      }
    } catch (error) {
      console.error("Error creating customer:", error);
      toast.error("An error occurred while creating the customer");
    } finally {
      setCreatingCustomer(false);
    }
  };

  // Handle input change for new customer form
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setNewCustomer((prev) => ({ ...prev, [name]: value }));
  };

  // Server-side filtering and sorting is now handled by the API

  return (
    <div className="p-4">
      <div className="mb-6">
        <div className="flex justify-between items-center mb-4">
          <h1 className="text-2xl font-bold">Customers</h1>
          <div className="flex items-center gap-2">
            <div className="text-sm text-gray-500">
              {totalCustomers > 0 && `Total: ${totalCustomers} customers`}
            </div>
            <Button
              onClick={() => setShowCreateDialog(true)}
              size="sm"
              className="ml-2"
            >
              <UserPlus className="h-4 w-4 mr-1" />
              Add Customer
            </Button>
          </div>
        </div>

        <div className="flex flex-col md:flex-row gap-4 items-start md:items-center">
          <div className="relative w-full md:w-96">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="Search customers..."
              className="pl-10"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>

          <div className="flex gap-2 items-center">
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-[130px]">
                <Filter className="h-4 w-4 mr-1" />
                <SelectValue placeholder="Filter" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Customers</SelectItem>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="blocked">Blocked</SelectItem>
              </SelectContent>
            </Select>

            <Select value={sortBy} onValueChange={setSortBy}>
              <SelectTrigger className="w-[160px]">
                <SelectValue placeholder="Sort by" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="createdAt">Registration Date</SelectItem>
                <SelectItem value="name">Name</SelectItem>
                <SelectItem value="totalSpent">Total Spending</SelectItem>
                <SelectItem value="totalOrders">Order Frequency</SelectItem>
                <SelectItem value="lastOrderDate">Last Order Date</SelectItem>
                <SelectItem value="averageOrderValue">
                  Average Order Value
                </SelectItem>
                <SelectItem value="status">Status</SelectItem>
              </SelectContent>
            </Select>

            <Select
              value={sortOrder}
              onValueChange={(value) => setSortOrder(value as "asc" | "desc")}
            >
              <SelectTrigger className="w-[100px]">
                {sortOrder === "asc" ? (
                  <SortAsc className="h-4 w-4 mr-1" />
                ) : (
                  <SortDesc className="h-4 w-4 mr-1" />
                )}
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="desc">High to Low</SelectItem>
                <SelectItem value="asc">Low to High</SelectItem>
              </SelectContent>
            </Select>

            {/* Secondary Sort */}
            <Select
              value={secondarySortBy || "none"}
              onValueChange={(value) => {
                setSecondarySortBy(value === "none" ? "" : value);
              }}
            >
              <SelectTrigger className="w-[140px]">
                <SelectValue placeholder="Then by" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="none">None</SelectItem>
                <SelectItem value="createdAt">Registration Date</SelectItem>
                <SelectItem value="name">Name</SelectItem>
                <SelectItem value="totalSpent">Total Spending</SelectItem>
                <SelectItem value="totalOrders">Order Frequency</SelectItem>
                <SelectItem value="lastOrderDate">Last Order Date</SelectItem>
                <SelectItem value="averageOrderValue">
                  Average Order Value
                </SelectItem>
                <SelectItem value="status">Status</SelectItem>
              </SelectContent>
            </Select>

            {secondarySortBy && (
              <Select
                value={secondarySortOrder}
                onValueChange={(value) =>
                  setSecondarySortOrder(value as "asc" | "desc")
                }
              >
                <SelectTrigger className="w-[100px]">
                  {secondarySortOrder === "asc" ? (
                    <SortAsc className="h-4 w-4 mr-1" />
                  ) : (
                    <SortDesc className="h-4 w-4 mr-1" />
                  )}
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="desc">High to Low</SelectItem>
                  <SelectItem value="asc">Low to High</SelectItem>
                </SelectContent>
              </Select>
            )}
          </div>
        </div>
      </div>

      {loading ? (
        <div className="text-center py-10">
          <Loader2 className="h-8 w-8 animate-spin text-primary mx-auto" />
          <p className="mt-2">Loading customers...</p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {customers.map((customer) => (
            <Card key={customer._id} className="overflow-hidden">
              <CardHeader>
                <div className="flex justify-between items-start">
                  <div>
                    <CardTitle>{customer.name}</CardTitle>
                    <CardDescription>{customer.email}</CardDescription>
                  </div>
                  <div className="flex flex-col gap-1 items-end">
                    {customer.customerStatus && (
                      <Badge
                        className={getStatusColor(customer.customerStatus)}
                      >
                        {customer.customerStatus.toUpperCase()}
                      </Badge>
                    )}
                    {customer.status && (
                      <Badge
                        className={
                          customer.status === "active"
                            ? "bg-green-100 text-green-800"
                            : "bg-red-100 text-red-800"
                        }
                      >
                        {customer.status === "active" ? (
                          <ShieldCheck className="h-3 w-3 mr-1" />
                        ) : (
                          <ShieldAlert className="h-3 w-3 mr-1" />
                        )}
                        {customer.status.toUpperCase()}
                      </Badge>
                    )}
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-500">Phone</span>
                    <span>{customer.phone || "Not provided"}</span>
                  </div>

                  {customer.totalOrders !== undefined ? (
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-500">
                        Total Orders
                      </span>
                      <span>{customer.totalOrders}</span>
                    </div>
                  ) : (
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-500">Joined</span>
                      <span>
                        {customer.createdAt
                          ? format(new Date(customer.createdAt), "PP")
                          : "Recently"}
                      </span>
                    </div>
                  )}

                  {customer.totalSpent !== undefined && (
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-500">Total Spent</span>
                      <span>{Indian(customer.totalSpent)}</span>
                    </div>
                  )}

                  {customer.averageOrderValue !== undefined && (
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-500">
                        Average Order
                      </span>
                      <span>{Indian(customer.averageOrderValue)}</span>
                    </div>
                  )}

                  {customer.lastOrderDate && (
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-500">Last Order</span>
                      <span>
                        {format(new Date(customer.lastOrderDate), "PP")}
                      </span>
                    </div>
                  )}

                  {customer.daysSinceLastOrder !== undefined && (
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-500">
                        Days Since Order
                      </span>
                      <span>
                        {Math.round(customer.daysSinceLastOrder)} days
                      </span>
                    </div>
                  )}
                </div>
                <div className="mt-4 pt-4 border-t flex justify-between">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleViewDetails(customer._id)}
                  >
                    <UserCog className="h-4 w-4 mr-1" />
                    Details
                  </Button>
                  {customer.status !== "blocked" ? (
                    <Button
                      variant="destructive"
                      size="sm"
                      onClick={() =>
                        handleStatusChange(customer._id, "blocked")
                      }
                      disabled={statusUpdating === customer._id}
                    >
                      {statusUpdating === customer._id ? (
                        <Loader2 className="h-4 w-4 animate-spin mr-1" />
                      ) : (
                        <ShieldAlert className="h-4 w-4 mr-1" />
                      )}
                      Block
                    </Button>
                  ) : (
                    <Button
                      variant="outline"
                      size="sm"
                      className="border-green-500 text-green-600 hover:bg-green-50"
                      onClick={() => handleStatusChange(customer._id, "active")}
                      disabled={statusUpdating === customer._id}
                    >
                      {statusUpdating === customer._id ? (
                        <Loader2 className="h-4 w-4 animate-spin mr-1" />
                      ) : (
                        <ShieldCheck className="h-4 w-4 mr-1" />
                      )}
                      Activate
                    </Button>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {selectedCustomer && (
        <CustomerDetailsDialog
          customerId={selectedCustomer}
          open={showDetailsDialog}
          onOpenChange={(open) => {
            setShowDetailsDialog(open);
            if (!open) {
              setSelectedCustomer(null);
            }
          }}
          onCustomerUpdated={refreshCustomers}
        />
      )}

      {/* Customer Creation Dialog */}
      <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Create New Customer</DialogTitle>
            <DialogDescription>
              Add a new customer to your database
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="name">Full Name</Label>
              <Input
                id="name"
                name="name"
                placeholder="Enter customer name"
                value={newCustomer.name}
                onChange={handleInputChange}
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="email">Email Address</Label>
              <Input
                id="email"
                name="email"
                type="email"
                placeholder="Enter customer email"
                value={newCustomer.email}
                onChange={handleInputChange}
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="phone">Phone Number (Optional)</Label>
              <Input
                id="phone"
                name="phone"
                placeholder="Enter customer phone"
                value={newCustomer.phone}
                onChange={handleInputChange}
              />
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowCreateDialog(false)}
            >
              Cancel
            </Button>
            <Button
              onClick={handleCreateCustomer}
              disabled={
                creatingCustomer || !newCustomer.name || !newCustomer.email
              }
            >
              {creatingCustomer ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Creating...
                </>
              ) : (
                "Create Customer"
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Pagination */}
      {!loading && totalPages > 1 && (
        <div className="mt-6 flex flex-col md:flex-row items-center justify-between gap-4">
          <div className="text-sm text-gray-500 order-3 md:order-1">
            Showing {customers.length} of {totalCustomers} customers
          </div>
          <div className="flex items-center space-x-2 order-1 md:order-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => handlePageChange(currentPage - 1)}
              disabled={currentPage === 1}
            >
              Previous
            </Button>
            <div className="flex items-center space-x-1">
              {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                // Show pages around current page
                let pageNum;
                if (totalPages <= 5) {
                  pageNum = i + 1;
                } else if (currentPage <= 3) {
                  pageNum = i + 1;
                } else if (currentPage >= totalPages - 2) {
                  pageNum = totalPages - 4 + i;
                } else {
                  pageNum = currentPage - 2 + i;
                }

                return (
                  <Button
                    key={pageNum}
                    variant={currentPage === pageNum ? "default" : "outline"}
                    size="sm"
                    className="w-8 h-8 p-0"
                    onClick={() => handlePageChange(pageNum)}
                  >
                    {pageNum}
                  </Button>
                );
              })}
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={() => handlePageChange(currentPage + 1)}
              disabled={currentPage === totalPages}
            >
              Next
            </Button>
          </div>
          <div className="flex items-center space-x-2 order-2 md:order-3">
            <span className="text-sm text-gray-500">Show</span>
            <select
              className="border rounded p-1 text-sm"
              value={limit}
              onChange={(e) => handleLimitChange(Number(e.target.value))}
            >
              <option value="10">10</option>
              <option value="20">20</option>
              <option value="50">50</option>
              <option value="100">100</option>
            </select>
            <span className="text-sm text-gray-500">per page</span>
          </div>
        </div>
      )}
    </div>
  );
};

export default Customers;
