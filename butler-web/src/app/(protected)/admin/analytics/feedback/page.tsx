"use client";

import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Star,

  MessageSquare,

  Filter,
  Download,
  RefreshCw,
  BarChart3,
  <PERSON><PERSON><PERSON>,
  AlertTriangle,
  CheckCircle,
  Clock,
} from "lucide-react";
import { toast } from "sonner";
import { getFoodChainFeedbackAnalytics, getAllOutlets } from "@/server/admin";
import FeedbackViewDialog from "@/components/admin/FeedbackViewDialog";

interface FeedbackAnalytics {
  overview: {
    totalFeedbacks: number;
    averageRating: number;
    responseRate: number;
    pendingResponses: number;
  };
  trends: {
    ratingTrend: Array<{
      period: string;
      rating: number;
      count: number;
    }>;
    categoryBreakdown: Array<{
      category: string;
      count: number;
      averageRating: number;
    }>;
  };
  outlets: Array<{
    outletId: string;
    outletName: string;
    totalFeedbacks: number;
    averageRating: number;
    pendingResponses: number;
  }>;
}

export default function FeedbackAnalyticsPage() {
  const [analytics, setAnalytics] = useState<FeedbackAnalytics | null>(null);
  const [outlets, setOutlets] = useState<Array<{ _id: string; name: string }>>([]);
  const [loading, setLoading] = useState(true);
  const [selectedOutlet, setSelectedOutlet] = useState<string>("all");
  const [dateRange, setDateRange] = useState<string>("30");
  const [showFeedbackDialog, setShowFeedbackDialog] = useState(false);
  const [selectedOutletForFeedback, setSelectedOutletForFeedback] = useState<string>("");

  useEffect(() => {
    fetchInitialData();
  }, []);

  useEffect(() => {
    fetchAnalytics();
  }, [selectedOutlet, dateRange]);

  const fetchInitialData = async () => {
    setLoading(true);
    try {
      const [analyticsResponse, outletsResponse] = await Promise.all([
        getFoodChainFeedbackAnalytics(),
        getAllOutlets(),
      ]);

      if (analyticsResponse.success) {
        setAnalytics(analyticsResponse.data);
      }

      if (outletsResponse.success) {
        setOutlets(outletsResponse.data);
      }
    } catch (error) {
      console.error("Error fetching initial data:", error);
      toast.error("Failed to load feedback analytics");
    } finally {
      setLoading(false);
    }
  };

  const fetchAnalytics = async () => {
    try {
      const response = await getFoodChainFeedbackAnalytics();
      if (response.success) {
        setAnalytics(response.data);
      }
    } catch (error) {
      console.error("Error fetching analytics:", error);
      toast.error("Failed to refresh analytics");
    }
  };

  const handleViewOutletFeedback = (outletId: string) => {
    setSelectedOutletForFeedback(outletId);
    setShowFeedbackDialog(true);
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`h-4 w-4 ${
          i < rating ? "text-yellow-400 fill-current" : "text-gray-300"
        }`}
      />
    ));
  };

  const getRatingColor = (rating: number) => {
    if (rating >= 4.5) return "text-green-600";
    if (rating >= 4.0) return "text-yellow-600";
    if (rating >= 3.0) return "text-orange-600";
    return "text-red-600";
  };



  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="text-center">
          <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p>Loading feedback analytics...</p>
        </div>
      </div>
    );
  }

  if (!analytics) {
    return (
      <div className="text-center py-8">
        <AlertTriangle className="h-12 w-12 text-yellow-500 mx-auto mb-4" />
        <h3 className="text-lg font-semibold mb-2">No Analytics Available</h3>
        <p className="text-gray-600 mb-4">Unable to load feedback analytics data.</p>
        <Button onClick={fetchInitialData}>
          <RefreshCw className="mr-2 h-4 w-4" />
          Retry
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Feedback Analytics</h1>
          <p className="text-gray-600">Monitor customer feedback and satisfaction across your outlets</p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={fetchAnalytics}>
            <RefreshCw className="mr-2 h-4 w-4" />
            Refresh
          </Button>
          <Button variant="outline">
            <Download className="mr-2 h-4 w-4" />
            Export
          </Button>
        </div>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Filters
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label>Outlet</Label>
              <Select value={selectedOutlet} onValueChange={setSelectedOutlet}>
                <SelectTrigger>
                  <SelectValue placeholder="Select outlet" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Outlets</SelectItem>
                  {outlets.map((outlet) => (
                    <SelectItem key={outlet._id} value={outlet._id}>
                      {outlet.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label>Date Range</Label>
              <Select value={dateRange} onValueChange={setDateRange}>
                <SelectTrigger>
                  <SelectValue placeholder="Select date range" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="7">Last 7 days</SelectItem>
                  <SelectItem value="30">Last 30 days</SelectItem>
                  <SelectItem value="90">Last 3 months</SelectItem>
                  <SelectItem value="365">Last year</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Feedback</CardTitle>
            <MessageSquare className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{analytics.overview.totalFeedbacks}</div>
            <p className="text-xs text-muted-foreground">
              +12% from last month
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Average Rating</CardTitle>
            <Star className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-2">
              <span className={`text-2xl font-bold ${getRatingColor(analytics.overview.averageRating)}`}>
                {analytics.overview.averageRating.toFixed(1)}
              </span>
              <div className="flex">
                {renderStars(Math.round(analytics.overview.averageRating))}
              </div>
            </div>
            <p className="text-xs text-muted-foreground">
              +0.2 from last month
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Response Rate</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{analytics.overview.responseRate.toFixed(1)}%</div>
            <p className="text-xs text-muted-foreground">
              +5% from last month
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending Responses</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-600">
              {analytics.overview.pendingResponses}
            </div>
            <p className="text-xs text-muted-foreground">
              Requires attention
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Outlet Performance */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            Outlet Performance
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {analytics.outlets.map((outlet) => (
              <div
                key={outlet.outletId}
                className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50"
              >
                <div className="flex-1">
                  <h3 className="font-medium">{outlet.outletName}</h3>
                  <div className="flex items-center gap-4 mt-1 text-sm text-gray-600">
                    <span>{outlet.totalFeedbacks} feedback(s)</span>
                    <div className="flex items-center gap-1">
                      <span className={getRatingColor(outlet.averageRating)}>
                        {outlet.averageRating.toFixed(1)}
                      </span>
                      <div className="flex">
                        {renderStars(Math.round(outlet.averageRating))}
                      </div>
                    </div>
                    {outlet.pendingResponses > 0 && (
                      <Badge variant="destructive" className="text-xs">
                        {outlet.pendingResponses} pending
                      </Badge>
                    )}
                  </div>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleViewOutletFeedback(outlet.outletId)}
                >
                  View Details
                </Button>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Category Breakdown */}
      {analytics.trends.categoryBreakdown.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <PieChart className="h-5 w-5" />
              Feedback Categories
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {analytics.trends.categoryBreakdown.map((category) => (
                <div key={category.category} className="p-4 border rounded-lg">
                  <h4 className="font-medium capitalize">
                    {category.category.replace(/_/g, " ")}
                  </h4>
                  <div className="flex items-center justify-between mt-2">
                    <span className="text-sm text-gray-600">{category.count} mentions</span>
                    <div className="flex items-center gap-1">
                      <span className={`text-sm ${getRatingColor(category.averageRating)}`}>
                        {category.averageRating.toFixed(1)}
                      </span>
                      <Star className="h-3 w-3 text-yellow-400 fill-current" />
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Feedback Dialog */}
      <FeedbackViewDialog
        isOpen={showFeedbackDialog}
        onClose={() => setShowFeedbackDialog(false)}
        outletId={selectedOutletForFeedback}
        mode="outlet"
      />
    </div>
  );
}
