/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";
import { useState, useEffect, useCallback } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { DatePicker } from "@/components/ui/date-picker";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { toast } from "sonner";
import { ArrowLeft, Loader2, Save, } from "lucide-react";
import { getOfferById, updateOffer } from "@/server/marketing";
import { getAllOutlets, getAllDishes, getAllCategories } from "@/server/admin";
import { useRouter, useParams } from "next/navigation";

export default function OfferEditPage() {
  const { id } = useParams() || {};
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [offer, setOffer] = useState<any>(null);
  const [outlets, setOutlets] = useState([]);
  const [dishes, setDishes] = useState([]);
  const [categories, setCategories] = useState([]);

  // Fetch offer details
  const fetchOfferDetails = useCallback(async () => {
    setLoading(true);
    try {
      const response = await getOfferById(id as string);
      if (response.success) {
        setOffer({
          ...response.data,
          startDate: new Date(response.data.startDate),
          endDate: new Date(response.data.endDate),
          applicableOutlets:
            response.data.applicableOutlets?.map((o: any) => o._id) || [],
          applicableDishes:
            response.data.applicableDishes?.map((d: any) => d._id) || [],
        });
      } else {
        toast.error(response.message || "Failed to fetch offer details");
        router.push("/admin/marketing/offers");
      }
    } catch (error) {
      console.error("Error fetching offer details:", error);
      toast.error("An error occurred while fetching offer details");
      router.push("/admin/marketing/offers");
    } finally {
      setLoading(false);
    }
  }, [id, router]);

  // Fetch outlets, dishes, and categories
  const fetchOutletsAndDishes = useCallback(async () => {
    try {
      const outletsResponse = await getAllOutlets();
      if (outletsResponse.success) {
        setOutlets(outletsResponse.data);
      }

      const dishesResponse = await getAllDishes();
      if (dishesResponse.success) {
        setDishes(dishesResponse.data);
      }

      const categoriesResponse = await getAllCategories();
      if (categoriesResponse.success) {
        setCategories(categoriesResponse.data);
      }
    } catch (error) {
      console.error("Error fetching outlets, dishes, and categories:", error);
    }
  }, []);

  useEffect(() => {
    fetchOfferDetails();
    fetchOutletsAndDishes();
  }, [fetchOfferDetails, fetchOutletsAndDishes]);

  // Handle input changes
  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setOffer({ ...offer, [name]: value });
  };

  // Handle select changes
  const handleSelectChange = (name: string, value: string) => {
    setOffer({ ...offer, [name]: value });
  };

  // Handle date changes
  const handleDateChange = (name: string, date: Date | undefined) => {
    if (date) {
      setOffer({ ...offer, [name]: date });
    }
  };

  // Handle checkbox changes
  const handleCheckboxChange = (name: string, checked: boolean) => {
    setOffer({ ...offer, [name]: checked });
  };

  // Handle outlet selection
  const handleOutletSelection = (outletId: string, checked: boolean) => {
    setOffer((prev: any) => {
      const outlets = checked
        ? [...prev.applicableOutlets, outletId]
        : prev.applicableOutlets.filter((id: string) => id !== outletId);
      return { ...prev, applicableOutlets: outlets };
    });
  };

  const handleDayOfWeekSelection = (day: number, checked: boolean) => {
    setOffer((prev: any) => {
      const days = checked
        ? [...(prev.discountDetails?.timeRestrictions?.daysOfWeek || []), day]
        : (prev.discountDetails?.timeRestrictions?.daysOfWeek || []).filter(
            (d: number) => d !== day
          );
      return {
        ...prev,
        discountDetails: {
          ...prev.discountDetails,
          timeRestrictions: {
            ...prev.discountDetails?.timeRestrictions,
            daysOfWeek: days,
          },
        },
      };
    });
  };

  // Handle dish selection
  const handleDishSelection = (dishId: string, checked: boolean) => {
    setOffer((prev: any) => {
      const dishes = checked
        ? [...prev.applicableDishes, dishId]
        : prev.applicableDishes.filter((id: string) => id !== dishId);
      return { ...prev, applicableDishes: dishes };
    });
  };

  const handleNestedChange = (path: string, value: any) => {
    const keys = path.split(".");
    const newOffer = { ...offer };
    let current = newOffer;

    for (let i = 0; i < keys.length - 1; i++) {
      current = current[keys[i]];
    }
    current[keys[keys.length - 1]] = value;

    setOffer(newOffer);
  };

  // Add combo item
  const addComboItem = () => {
    setOffer((prev: any) => ({
      ...prev,
      discountDetails: {
        ...prev.discountDetails,
        comboItems: [
          ...(prev.discountDetails.comboItems || []),
          { dishId: "", quantity: 1 },
        ],
      },
    }));
  };

  // Remove combo item
  const removeComboItem = (index: number) => {
    setOffer((prev: any) => ({
      ...prev,
      discountDetails: {
        ...prev.discountDetails,
        comboItems: prev.discountDetails.comboItems.filter(
          (_: any, i: number) => i !== index
        ),
      },
    }));
  };

  // Update combo item
  const updateComboItem = (index: number, field: string, value: any) => {
    setOffer((prev: any) => {
      const newComboItems = [...prev.discountDetails.comboItems];
      newComboItems[index] = { ...newComboItems[index], [field]: value };
      return {
        ...prev,
        discountDetails: {
          ...prev.discountDetails,
          comboItems: newComboItems,
        },
      };
    });
  };

  // Handle customer tier selection
  const handleCustomerTierSelection = (tier: string, checked: boolean) => {
    setOffer((prev: any) => {
      const tiers = checked
        ? [...(prev.discountDetails.customerTiers || []), tier]
        : (prev.discountDetails.customerTiers || []).filter((t: string) => t !== tier);
      return {
        ...prev,
        discountDetails: {
          ...prev.discountDetails,
          customerTiers: tiers,
        },
      };
    });
  };

  // Handle form submission
  const handleSubmit = async () => {
    if (!offer.name) {
      toast.error("Offer name is required");
      return;
    }

    setSaving(true);
    try {
      const response = await updateOffer(id as string, offer);
      if (response.success) {
        toast.success("Offer updated successfully");
        router.push("/admin/marketing/offers");
      } else {
        toast.error(response.message || "Failed to update offer");
      }
    } catch (error) {
      console.error("Error updating offer:", error);
      toast.error("An error occurred while updating the offer");
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Loader2 className="h-8 w-8 animate-spin text-gray-500" />
      </div>
    );
  }

  if (!offer) {
    return (
      <div className="text-center py-10">
        <h3 className="text-lg font-medium">Offer not found</h3>
        <Button
          className="mt-4"
          onClick={() => router.push("/admin/marketing/offers")}
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Offers
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <Button
          variant="outline"
          onClick={() => router.push("/admin/marketing/offers")}
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Offers
        </Button>
        <Button onClick={handleSubmit} disabled={saving}>
          {saving ? (
            <>
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              Saving...
            </>
          ) : (
            <>
              <Save className="h-4 w-4 mr-2" />
              Save Changes
            </>
          )}
        </Button>
      </div>

      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-xl font-semibold mb-6">Edit Offer</h2>

        <div className="grid gap-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="name">Offer Name</Label>
              <Input
                id="name"
                name="name"
                placeholder="e.g., Summer Special"
                value={offer.name}
                onChange={handleInputChange}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="offerType">Offer Type</Label>
              <Select
                value={offer.offerType}
                onValueChange={(value) =>
                  handleSelectChange("offerType", value)
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select offer type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="BOGO">Buy One Get One</SelectItem>
                  <SelectItem value="combo">Combo Offer</SelectItem>
                  <SelectItem value="discount">Discount</SelectItem>
                  <SelectItem value="freeItem">Free Item</SelectItem>
                  <SelectItem value="quantityDiscount">
                        Quantity Discount
                      </SelectItem>
                      <SelectItem value="multiDishType">
                        Multi-Dish Type
                      </SelectItem>
                      <SelectItem value="minimumAmount">
                        Minimum Amount
                      </SelectItem>
                      <SelectItem value="dayOfWeek">Day of Week</SelectItem>
                      <SelectItem value="dateRange">Date Range</SelectItem>
                      <SelectItem value="customerTier">
                        Customer Tier
                      </SelectItem>
                      <SelectItem value="firstTime">First Time</SelectItem>
                      <SelectItem value="timeBasedSpecial">
                        Time Based Special
                      </SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              name="description"
              placeholder="Describe the offer"
              value={offer.description || ""}
              onChange={handleInputChange}
              className="min-h-[100px]"
            />
          </div>

          {/* Discount Details */}
          {offer.offerType && (
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Discount Details</h3>

              {/* Common discount fields for applicable offer types */}
              {[
                "discount",
                "minimumAmount",
                "quantityDiscount",
                "dateRange",
                "customerTier",
                "firstTime",
                "timeBasedSpecial",
                "dayOfWeek",
              ].includes(offer.offerType) && (
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <Label>Discount Type</Label>
                    <Select
                      value={
                        offer.discountDetails?.discountType || "percentage"
                      }
                      onValueChange={(value) =>
                        setOffer({
                          ...offer,
                          discountDetails: {
                            ...offer.discountDetails,
                            discountType: value,
                          },
                        })
                      }
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="percentage">Percentage</SelectItem>
                        <SelectItem value="fixed">Fixed Amount</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label>Discount Value</Label>
                    <Input
                      type="number"
                      placeholder={
                        offer.discountDetails?.discountType === "percentage"
                          ? "e.g., 10"
                          : "e.g., 100"
                      }
                      value={offer.discountDetails?.discountValue || ""}
                      onChange={(e) =>
                        setOffer({
                          ...offer,
                          discountDetails: {
                            ...offer.discountDetails,
                            discountValue: Number(e.target.value),
                          },
                        })
                      }
                    />
                  </div>
                  {offer.discountDetails?.discountType === "percentage" && (
                    <div className="space-y-2">
                      <Label>Max Discount (₹)</Label>
                      <Input
                        type="number"
                        placeholder="e.g., 500"
                        value={offer.discountDetails?.maxDiscount || ""}
                        onChange={(e) =>
                          setOffer({
                            ...offer,
                            discountDetails: {
                              ...offer.discountDetails,
                              maxDiscount: Number(e.target.value),
                            },
                          })
                        }
                      />
                    </div>
                  )}
                </div>
              )}

              {/* BOGO specific fields */}
              {offer.offerType === "BOGO" && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>Buy Quantity *</Label>
                    <Input
                      type="number"
                      min="1"
                      placeholder="e.g., 2"
                      value={offer.discountDetails?.buyQuantity || ""}
                      onChange={(e) =>
                        handleNestedChange(
                          "discountDetails.buyQuantity",
                          Number(e.target.value)
                        )
                      }
                    />
                  </div>
                  <div className="space-y-2">
                    <Label>Get Quantity *</Label>
                    <Input
                      type="number"
                      min="1"
                      placeholder="e.g., 1"
                      value={offer.discountDetails?.getQuantity || ""}
                      onChange={(e) =>
                        handleNestedChange(
                          "discountDetails.getQuantity",
                          Number(e.target.value)
                        )
                      }
                    />
                  </div>
                </div>
              )}

              {/* Free Item specific fields */}
              {offer.offerType === "freeItem" && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>Free Item *</Label>
                    <Select
                      value={offer.discountDetails?.freeItemId || ""}
                      onValueChange={(value) =>
                        handleNestedChange("discountDetails.freeItemId", value)
                      }
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select free item" />
                      </SelectTrigger>
                      <SelectContent>
                        {dishes.map((dish: any) => (
                          <SelectItem key={dish._id} value={dish._id}>
                            {dish.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label>Free Item Quantity</Label>
                    <Input
                      type="number"
                      min="1"
                      placeholder="e.g., 1"
                      value={offer.discountDetails?.freeItemQuantity || ""}
                      onChange={(e) =>
                        handleNestedChange(
                          "discountDetails.freeItemQuantity",
                          Number(e.target.value)
                        )
                      }
                    />
                  </div>
                </div>
              )}

              {/* Combo offer fields */}
              {offer.offerType === "combo" && (
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <Label>Combo Items *</Label>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={addComboItem}
                    >
                      Add Item
                    </Button>
                  </div>
                  {(offer.discountDetails?.comboItems || []).map(
                    (item: any, index: number) => (
                      <div
                        key={index}
                        className="grid grid-cols-1 md:grid-cols-3 gap-4 p-4 border rounded-lg"
                      >
                        <div className="space-y-2">
                          <Label>Dish</Label>
                          <Select
                            value={item._id}
                            onValueChange={(value) =>
                              updateComboItem(index, "dishId", value)
                            }
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="Select dish" />
                            </SelectTrigger>
                            <SelectContent>
                              {dishes.map((dish: any) => (
                                <SelectItem key={dish._id} value={dish._id}>
                                  {dish.name} {dish._id}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>
                        <div className="space-y-2">
                          <Label>Quantity</Label>
                          <Input
                            type="number"
                            min="1"
                            value={item.quantity}
                            onChange={(e) =>
                              updateComboItem(
                                index,
                                "quantity",
                                Number(e.target.value)
                              )
                            }
                          />
                        </div>
                        <div className="flex items-end">
                          <Button
                            type="button"
                            variant="destructive"
                            size="sm"
                            onClick={() => removeComboItem(index)}
                          >
                            Remove
                          </Button>
                        </div>
                      </div>
                    )
                  )}
                  <div className="space-y-2">
                    <Label>Combo Price (₹) *</Label>
                    <Input
                      type="number"
                      placeholder="e.g., 299"
                      value={offer.discountDetails?.comboPrice || ""}
                      onChange={(e) =>
                        handleNestedChange(
                          "discountDetails.comboPrice",
                          Number(e.target.value)
                        )
                      }
                    />
                  </div>
                </div>
              )}

              {/* Minimum amount fields */}
              {offer.offerType === "minimumAmount" && (
                <div className="space-y-2">
                  <Label>Minimum Order Value (₹)</Label>
                  <Input
                    type="number"
                    placeholder="e.g., 500"
                    value={offer.discountDetails?.minimumOrderValue || ""}
                    onChange={(e) =>
                      handleNestedChange(
                        "discountDetails.minimumOrderValue",
                        Number(e.target.value)
                      )
                    }
                  />
                </div>
              )}

              {/* Multi-dish type specific fields */}
              {offer.offerType === "multiDishType" && (
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label>Required Categories *</Label>
                    <div className="grid grid-cols-2 md:grid-cols-3 gap-2 max-h-40 overflow-y-auto border rounded-md p-2">
                      {categories.map((category: any) => (
                        <div key={category._id} className="flex items-center space-x-2">
                          <Checkbox
                            id={`category-${category._id}`}
                            checked={(offer.discountDetails?.requiredCategories || []).includes(category._id)}
                            onCheckedChange={(checked) => {
                              const categories = checked
                                ? [...(offer.discountDetails?.requiredCategories || []), category._id]
                                : (offer.discountDetails?.requiredCategories || []).filter((id: string) => id !== category._id);
                              handleNestedChange("discountDetails.requiredCategories", categories);
                            }}
                          />
                          <Label htmlFor={`category-${category._id}`} className="text-sm">
                            {category.name}
                          </Label>
                        </div>
                      ))}
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label>Minimum Categories Count</Label>
                    <Input
                      type="number"
                      min="1"
                      placeholder="e.g., 2"
                      value={offer.discountDetails?.minimumCategoriesCount || ""}
                      onChange={(e) =>
                        handleNestedChange(
                          "discountDetails.minimumCategoriesCount",
                          Number(e.target.value)
                        )
                      }
                    />
                  </div>
                </div>
              )}

              {/* Customer tier specific fields */}
              {offer.offerType === "customerTier" && (
                <div className="space-y-2">
                  <Label>Customer Tiers</Label>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                    {["new", "regular", "vip", "premium"].map((tier) => (
                      <div key={tier} className="flex items-center space-x-2">
                        <Checkbox
                          id={`tier-${tier}`}
                          checked={(offer.discountDetails?.customerTiers || []).includes(tier)}
                          onCheckedChange={(checked) =>
                            handleCustomerTierSelection(tier, checked === true)
                          }
                        />
                        <Label htmlFor={`tier-${tier}`} className="text-sm capitalize">
                          {tier}
                        </Label>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label>Start Date</Label>
              <DatePicker
                date={offer.startDate}
                setDate={(date) => handleDateChange("startDate", date)}
              />
            </div>
            <div className="space-y-2">
              <Label>End Date</Label>
              <DatePicker
                date={offer.endDate}
                setDate={(date) => handleDateChange("endDate", date)}
              />
            </div>
          </div>
           {["dayOfWeek", "timeBasedSpecial"].includes(
                  offer.offerType
                ) && (
                  <div className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label>Start Time</Label>
                        <Input
                          type="time"
                          value={
                            offer.discountDetails.timeRestrictions.startTime
                          }
                          onChange={(e) =>
                            handleNestedChange(
                              "discountDetails.timeRestrictions.startTime",
                              e.target.value
                            )
                          }
                        />
                      </div>
                      <div className="space-y-2">
                        <Label>End Time</Label>
                        <Input
                          type="time"
                          value={offer.discountDetails.timeRestrictions.endTime}
                          onChange={(e) =>
                            handleNestedChange(
                              "discountDetails.timeRestrictions.endTime",
                              e.target.value
                            )
                          }
                        />
                      </div>
                    </div>
                    <div className="space-y-2">
                      <Label>Days of Week</Label>
                      <div className="grid grid-cols-7 gap-2">
                        {["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"].map(
                          (day, index) => (
                            <div
                              key={day}
                              className="flex items-center space-x-2"
                            >
                              <Checkbox
                                id={`day-${index}`}
                                checked={offer.discountDetails.timeRestrictions.daysOfWeek.includes(
                                  index
                                )}
                                onCheckedChange={(checked) =>
                                  handleDayOfWeekSelection(
                                    index,
                                    checked === true
                                  )
                                }
                              />
                              <Label
                                htmlFor={`day-${index}`}
                                className="text-sm"
                              >
                                {day}
                              </Label>
                            </div>
                          )
                        )}
                      </div>
                    </div>
                  </div>
                )}

          <div className="flex items-center space-x-2">
            <Checkbox
              id="isActive"
              checked={offer.isActive}
              onCheckedChange={(checked) =>
                handleCheckboxChange("isActive", checked === true)
              }
            />
            <Label htmlFor="isActive">Active</Label>
          </div>

          <div className="flex items-center space-x-2">
            <Checkbox
              id="displayOnApp"
              checked={offer.displayOnApp}
              onCheckedChange={(checked) =>
                handleCheckboxChange("displayOnApp", checked === true)
              }
            />
            <Label htmlFor="displayOnApp">Display on App</Label>
          </div>

          <div className="flex items-center space-x-2">
            <Checkbox
              id="autoApply"
              checked={offer.autoApply}
              onCheckedChange={(checked) =>
                handleCheckboxChange("autoApply", checked === true)
              }
            />
            <Label htmlFor="autoApply">Auto Apply</Label>
            <p className="text-xs text-gray-500 ml-2">
              Automatically apply this offer when conditions are met
            </p>
          </div>

          <div className="space-y-2">
            <Label>Applicable Outlets</Label>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-2 max-h-40 overflow-y-auto border rounded-md p-2">
              {outlets.map((outlet: any) => (
                <div key={outlet._id} className="flex items-center space-x-2">
                  <Checkbox
                    id={`outlet-${outlet._id}`}
                    checked={offer.applicableOutlets.includes(outlet._id)}
                    onCheckedChange={(checked) =>
                      handleOutletSelection(outlet._id, checked === true)
                    }
                  />
                  <Label htmlFor={`outlet-${outlet._id}`} className="text-sm">
                    {outlet.address}
                  </Label>
                </div>
              ))}
            </div>
            <p className="text-xs text-gray-500">
              If none selected, offer applies to all outlets
            </p>
          </div>

          <div className="space-y-2">
            <Label>Applicable Dishes</Label>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-2 max-h-40 overflow-y-auto border rounded-md p-2">
              {dishes.map((dish: any) => (
                <div key={dish._id} className="flex items-center space-x-2">
                  <Checkbox
                    id={`dish-${dish._id}`}
                    checked={offer.applicableDishes.includes(dish._id)}
                    onCheckedChange={(checked) =>
                      handleDishSelection(dish._id, checked === true)
                    }
                  />
                  <Label htmlFor={`dish-${dish._id}`} className="text-sm">
                    {dish.name}
                  </Label>
                </div>
              ))}
            </div>
            <p className="text-xs text-gray-500">
              If none selected, offer applies to all dishes
            </p>
          </div>

          {/* Stacking Rules */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Stacking Rules</h3>
            <div className="space-y-4">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="canStackWithOthers"
                  checked={offer.stackingRules?.canStackWithOthers || false}
                  onCheckedChange={(checked) =>
                    setOffer({
                      ...offer,
                      stackingRules: {
                        ...offer.stackingRules,
                        canStackWithOthers: checked === true,
                      },
                    })
                  }
                />
                <Label htmlFor="canStackWithOthers">Can stack with other offers</Label>
              </div>
              <div className="space-y-2">
                <Label>Priority (Higher number = Higher priority)</Label>
                <Input
                  type="number"
                  placeholder="e.g., 1"
                  value={offer.stackingRules?.priority || ""}
                  onChange={(e) =>
                    setOffer({
                      ...offer,
                      stackingRules: {
                        ...offer.stackingRules,
                        priority: Number(e.target.value),
                      },
                    })
                  }
                />
              </div>
            </div>
          </div>

          {/* Usage Rules */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Usage Rules</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label>Usage Limit (0 = unlimited)</Label>
                <Input
                  type="number"
                  placeholder="e.g., 100"
                  value={offer.usageRules?.usageLimit || ""}
                  onChange={(e) =>
                    setOffer({
                      ...offer,
                      usageRules: {
                        ...offer.usageRules,
                        usageLimit: Number(e.target.value),
                      },
                    })
                  }
                />
              </div>
              <div className="space-y-2">
                <Label>Per Customer Limit (0 = unlimited)</Label>
                <Input
                  type="number"
                  placeholder="e.g., 1"
                  value={offer.usageRules?.perCustomerLimit || ""}
                  onChange={(e) =>
                    setOffer({
                      ...offer,
                      usageRules: {
                        ...offer.usageRules,
                        perCustomerLimit: Number(e.target.value),
                      },
                    })
                  }
                />
              </div>
              <div className="space-y-2">
                <Label>Per Order Limit</Label>
                <Input
                  type="number"
                  placeholder="e.g., 1"
                  value={offer.usageRules?.perOrderLimit || ""}
                  onChange={(e) =>
                    setOffer({
                      ...offer,
                      usageRules: {
                        ...offer.usageRules,
                        perOrderLimit: Number(e.target.value),
                      },
                    })
                  }
                />
              </div>
            </div>
          </div>

          {/* Banner Image */}
          <div className="space-y-2">
            <Label htmlFor="bannerImage">Banner Image URL</Label>
            <Input
              id="bannerImage"
              name="bannerImage"
              placeholder="Enter banner image URL"
              value={offer.bannerImage || ""}
              onChange={handleInputChange}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="termsAndConditions">Terms and Conditions</Label>
            <Textarea
              id="termsAndConditions"
              name="termsAndConditions"
              placeholder="Enter terms and conditions"
              value={offer.termsAndConditions || ""}
              onChange={handleInputChange}
              className="min-h-[100px]"
            />
          </div>
        </div>
      </div>
    </div>
  );
}
