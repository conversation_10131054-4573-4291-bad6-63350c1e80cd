"use client";
import { useState, useEffect, useCallback } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

import { toast } from "sonner";
import { format } from "date-fns";
import {
  Search,
  Plus,
  Percent,
  DollarSign,
  Calendar,
  Tag,
  Trash2,
  Edit,
  Loader2,
  CheckCircle,
  XCircle,
  Clock,
} from "lucide-react";
import { getAllCoupons, createCoupon, deleteCoupon } from "@/server/marketing";
import { getAllOutlets } from "@/server/admin";
import { getAllDishes } from "@/server/admin";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import { DatePicker } from "@/components/ui/date-picker";
import { useRouter } from "next/navigation";

interface Coupon {
  _id: string;
  code: string;
  description: string;
  discountType: "percentage" | "fixed";
  discountValue: number;
  minOrderValue: number;
  maxDiscount?: number;
  startDate: string;
  endDate: string;
  usageLimit: number;
  usedCount: number;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  applicableOutlets: { _id: string; name: string }[];
  applicableDishes: { _id: string; name: string }[];
  customerRestrictions: {
    firstTimeOnly: boolean;
    specificCustomers: string[];
  };
}

interface Outlet {
  _id: string;
  name: string;
}

interface Dish {
  _id: string;
  name: string;
}

export default function CouponsPage() {
  const router = useRouter();
  const [coupons, setCoupons] = useState<Coupon[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [isCreating, setIsCreating] = useState(false);
  const [isDeleting, setIsDeleting] = useState<string | null>(null);
  const [outlets, setOutlets] = useState<Outlet[]>([]);
  const [dishes, setDishes] = useState<Dish[]>([]);

  // New coupon form state
  const [newCoupon, setNewCoupon] = useState({
    code: "",
    description: "",
    discountType: "percentage",
    discountValue: 0,
    minOrderValue: 0,
    maxDiscount: 0,
    startDate: new Date(),
    endDate: new Date(new Date().setMonth(new Date().getMonth() + 1)),
    usageLimit: 0,
    isActive: true,
    applicableOutlets: [] as string[],
    applicableDishes: [] as string[],
    customerRestrictions: {
      firstTimeOnly: false,
      specificCustomers: [] as string[],
    },
  });

  // Fetch coupons
  const fetchCoupons = useCallback(async () => {
    setLoading(true);
    try {
      const response = await getAllCoupons({
        status: statusFilter !== "all" ? statusFilter : undefined,
      });
      if (response.success) {
        setCoupons(response.data);
      } else {
        toast.error(response.message || "Failed to fetch coupons");
      }
    } catch (error) {
      console.error("Error fetching coupons:", error);
      toast.error("An error occurred while fetching coupons");
    } finally {
      setLoading(false);
    }
  }, [statusFilter]);

  // Fetch outlets and dishes
  const fetchOutletsAndDishes = async () => {
    try {
      const outletsResponse = await getAllOutlets();
      if (outletsResponse.success) {
        setOutlets(outletsResponse.data);
      }

      const dishesResponse = await getAllDishes();
      if (dishesResponse.success) {
        setDishes(dishesResponse.data);
      }
    } catch (error) {
      console.error("Error fetching outlets and dishes:", error);
    }
  };

  useEffect(() => {
    fetchCoupons();
    fetchOutletsAndDishes();
  }, [statusFilter, fetchCoupons]);

  // Handle input change for new coupon form
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setNewCoupon((prev) => ({ ...prev, [name]: value }));
  };

  // Handle select change for new coupon form
  const handleSelectChange = (name: string, value: string) => {
    setNewCoupon((prev) => ({ ...prev, [name]: value }));
  };

  // Handle number input change for new coupon form
  const handleNumberChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setNewCoupon((prev) => ({ ...prev, [name]: parseFloat(value) || 0 }));
  };

  // Handle date change for new coupon form
  const handleDateChange = (name: string, date: Date | undefined) => {
    if (date) {
      setNewCoupon((prev) => ({ ...prev, [name]: date }));
    }
  };

  // Handle checkbox change for new coupon form
  const handleCheckboxChange = (name: string, checked: boolean) => {
    if (name === "firstTimeOnly") {
      setNewCoupon((prev) => ({
        ...prev,
        customerRestrictions: {
          ...prev.customerRestrictions,
          firstTimeOnly: checked,
        },
      }));
    } else {
      setNewCoupon((prev) => ({ ...prev, [name]: checked }));
    }
  };

  // Handle outlet selection
  const handleOutletSelection = (outletId: string, checked: boolean) => {
    setNewCoupon((prev) => {
      const outlets = checked
        ? [...prev.applicableOutlets, outletId]
        : prev.applicableOutlets.filter((id) => id !== outletId);
      return { ...prev, applicableOutlets: outlets };
    });
  };

  // Handle dish selection
  const handleDishSelection = (dishId: string, checked: boolean) => {
    setNewCoupon((prev) => {
      const dishes = checked
        ? [...prev.applicableDishes, dishId]
        : prev.applicableDishes.filter((id) => id !== dishId);
      return { ...prev, applicableDishes: dishes };
    });
  };

  // Create new coupon
  const handleCreateCoupon = async () => {
    if (!newCoupon.code) {
      toast.error("Coupon code is required");
      return;
    }

    if (!newCoupon.discountValue || newCoupon.discountValue <= 0) {
      toast.error("Discount value must be greater than 0");
      return;
    }

    setIsCreating(true);
    try {
      const response = await createCoupon(newCoupon);
      if (response.success) {
        toast.success("Coupon created successfully");
        setShowCreateDialog(false);
        // Reset form
        setNewCoupon({
          code: "",
          description: "",
          discountType: "percentage",
          discountValue: 0,
          minOrderValue: 0,
          maxDiscount: 0,
          startDate: new Date(),
          endDate: new Date(new Date().setMonth(new Date().getMonth() + 1)),
          usageLimit: 0,
          isActive: true,
          applicableOutlets: [],
          applicableDishes: [],
          customerRestrictions: {
            firstTimeOnly: false,
            specificCustomers: [],
          },
        });
        fetchCoupons();
      } else {
        toast.error(response.message || "Failed to create coupon");
      }
    } catch (error) {
      console.error("Error creating coupon:", error);
      toast.error("An error occurred while creating the coupon");
    } finally {
      setIsCreating(false);
    }
  };

  // Delete coupon
  const handleDeleteCoupon = async (id: string) => {
    setIsDeleting(id);
    try {
      const response = await deleteCoupon(id);
      if (response.success) {
        toast.success("Coupon deleted successfully");
        fetchCoupons();
      } else {
        toast.error(response.message || "Failed to delete coupon");
      }
    } catch (error) {
      console.error("Error deleting coupon:", error);
      toast.error("An error occurred while deleting the coupon");
    } finally {
      setIsDeleting(null);
    }
  };

  // Filter coupons by search term
  const filteredCoupons = coupons.filter(
    (coupon) =>
      coupon.code.toLowerCase().includes(searchTerm.toLowerCase()) ||
      coupon.description.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Get coupon status
  const getCouponStatus = (coupon: Coupon) => {
    const now = new Date();
    const startDate = new Date(coupon.startDate);
    const endDate = new Date(coupon.endDate);

    if (!coupon.isActive) {
      return "inactive";
    } else if (now < startDate) {
      return "upcoming";
    } else if (now > endDate) {
      return "expired";
    } else {
      return "active";
    }
  };

  // Get status badge
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "active":
        return (
          <Badge className="bg-green-100 text-green-800 hover:bg-green-200">
            <CheckCircle className="h-3 w-3 mr-1" />
            Active
          </Badge>
        );
      case "inactive":
        return (
          <Badge className="bg-gray-100 text-gray-800 hover:bg-gray-200">
            <XCircle className="h-3 w-3 mr-1" />
            Inactive
          </Badge>
        );
      case "upcoming":
        return (
          <Badge className="bg-blue-100 text-blue-800 hover:bg-blue-200">
            <Clock className="h-3 w-3 mr-1" />
            Upcoming
          </Badge>
        );
      case "expired":
        return (
          <Badge className="bg-red-100 text-red-800 hover:bg-red-200">
            <XCircle className="h-3 w-3 mr-1" />
            Expired
          </Badge>
        );
      default:
        return null;
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row justify-between gap-4">
        <div className="relative w-full md:w-96">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <Input
            placeholder="Search coupons..."
            className="pl-10"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        <div className="flex gap-2">
          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Filter by status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Coupons</SelectItem>
              <SelectItem value="active">Active</SelectItem>
              <SelectItem value="inactive">Inactive</SelectItem>
              <SelectItem value="upcoming">Upcoming</SelectItem>
              <SelectItem value="expired">Expired</SelectItem>
            </SelectContent>
          </Select>
          <Button onClick={() => setShowCreateDialog(true)}>
            <Plus className="h-4 w-4 mr-2" />
            Create Coupon
          </Button>
        </div>
      </div>

      {loading ? (
        <div className="flex justify-center items-center h-64">
          <Loader2 className="h-8 w-8 animate-spin text-gray-500" />
        </div>
      ) : filteredCoupons.length === 0 ? (
        <div className="flex flex-col items-center justify-center h-64 bg-gray-50 rounded-lg border border-dashed border-gray-300">
          <Tag className="h-10 w-10 text-gray-400 mb-2" />
          <h3 className="text-lg font-medium text-gray-900">
            No coupons found
          </h3>
          <p className="text-sm text-gray-500 mb-4">
            {searchTerm
              ? "No coupons match your search criteria"
              : "Get started by creating a new coupon"}
          </p>
          <Button onClick={() => setShowCreateDialog(true)}>
            <Plus className="h-4 w-4 mr-2" />
            Create Coupon
          </Button>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {filteredCoupons.map((coupon) => {
            const status = getCouponStatus(coupon);
            return (
              <Card key={coupon._id} className="overflow-hidden">
                <CardHeader className="pb-2">
                  <div className="flex justify-between items-start">
                    <div>
                      <CardTitle className="text-lg font-bold">
                        {coupon.code}
                      </CardTitle>
                      <CardDescription className="mt-1">
                        {coupon.description || "No description"}
                      </CardDescription>
                    </div>
                    {getStatusBadge(status)}
                  </div>
                </CardHeader>
                <CardContent className="pb-2">
                  <div className="space-y-2 text-sm">
                    <div className="flex items-center">
                      {coupon.discountType === "percentage" ? (
                        <Percent className="h-4 w-4 mr-2 text-gray-500" />
                      ) : (
                        <DollarSign className="h-4 w-4 mr-2 text-gray-500" />
                      )}
                      <span>
                        {coupon.discountType === "percentage"
                          ? `${coupon.discountValue}% off`
                          : `₹${coupon.discountValue} off`}
                        {coupon.maxDiscount &&
                          coupon.maxDiscount > 0 &&
                          coupon.discountType === "percentage" &&
                          ` (up to ₹${coupon.maxDiscount})`}
                      </span>
                    </div>
                    {coupon.minOrderValue > 0 && (
                      <div className="flex items-center">
                        <DollarSign className="h-4 w-4 mr-2 text-gray-500" />
                        <span>Min. order: ₹{coupon.minOrderValue}</span>
                      </div>
                    )}
                    <div className="flex items-center">
                      <Calendar className="h-4 w-4 mr-2 text-gray-500" />
                      <span>
                        {format(new Date(coupon.startDate), "MMM d, yyyy")} -{" "}
                        {format(new Date(coupon.endDate), "MMM d, yyyy")}
                      </span>
                    </div>
                    {coupon.usageLimit > 0 && (
                      <div className="flex items-center">
                        <Tag className="h-4 w-4 mr-2 text-gray-500" />
                        <span>
                          Used {coupon.usedCount} of {coupon.usageLimit} times
                        </span>
                      </div>
                    )}
                  </div>
                </CardContent>
                <CardFooter className="flex justify-between pt-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() =>
                      router.push(`/admin/marketing/coupons/${coupon._id}`)
                    }
                  >
                    <Edit className="h-4 w-4 mr-1" />
                    Edit
                  </Button>
                  <Button
                    variant="destructive"
                    size="sm"
                    onClick={() => handleDeleteCoupon(coupon._id)}
                    disabled={isDeleting === coupon._id}
                  >
                    {isDeleting === coupon._id ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : (
                      <>
                        <Trash2 className="h-4 w-4 mr-1" />
                        Delete
                      </>
                    )}
                  </Button>
                </CardFooter>
              </Card>
            );
          })}
        </div>
      )}

      {/* Create Coupon Dialog */}
      <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
        <DialogContent className="sm:max-w-[600px] max-h-4/5 overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Create New Coupon</DialogTitle>
            <DialogDescription>
              Create a new coupon code for your customers
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="code">Coupon Code</Label>
                <Input
                  id="code"
                  name="code"
                  placeholder="e.g., SUMMER20"
                  value={newCoupon.code}
                  onChange={handleInputChange}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="discountType">Discount Type</Label>
                <Select
                  value={newCoupon.discountType}
                  onValueChange={(value) =>
                    handleSelectChange("discountType", value)
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select discount type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="percentage">Percentage</SelectItem>
                    <SelectItem value="fixed">Fixed Amount</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="discountValue">
                  {newCoupon.discountType === "percentage"
                    ? "Discount Percentage"
                    : "Discount Amount (₹)"}
                </Label>
                <Input
                  id="discountValue"
                  name="discountValue"
                  type="number"
                  min="0"
                  step={newCoupon.discountType === "percentage" ? "1" : "0.01"}
                  value={newCoupon.discountValue}
                  onChange={handleNumberChange}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="minOrderValue">Minimum Order Value (₹)</Label>
                <Input
                  id="minOrderValue"
                  name="minOrderValue"
                  type="number"
                  min="0"
                  step="0.01"
                  value={newCoupon.minOrderValue}
                  onChange={handleNumberChange}
                />
              </div>
            </div>

            {newCoupon.discountType === "percentage" && (
              <div className="space-y-2">
                <Label htmlFor="maxDiscount">
                  Maximum Discount Amount (₹) (0 for no limit)
                </Label>
                <Input
                  id="maxDiscount"
                  name="maxDiscount"
                  type="number"
                  min="0"
                  step="0.01"
                  value={newCoupon.maxDiscount}
                  onChange={handleNumberChange}
                />
              </div>
            )}

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label>Start Date</Label>
                <DatePicker
                  date={newCoupon.startDate}
                  setDate={(date) => handleDateChange("startDate", date)}
                />
              </div>
              <div className="space-y-2">
                <Label>End Date</Label>
                <DatePicker
                  date={newCoupon.endDate}
                  setDate={(date) => handleDateChange("endDate", date)}
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="usageLimit">Usage Limit (0 for unlimited)</Label>
              <Input
                id="usageLimit"
                name="usageLimit"
                type="number"
                min="0"
                step="1"
                value={newCoupon.usageLimit}
                onChange={handleNumberChange}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Description (Optional)</Label>
              <Input
                id="description"
                name="description"
                placeholder="e.g., Summer sale discount"
                value={newCoupon.description}
                onChange={handleInputChange}
              />
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="isActive"
                checked={newCoupon.isActive}
                onCheckedChange={(checked) =>
                  handleCheckboxChange("isActive", checked as boolean)
                }
              />
              <Label htmlFor="isActive">Active</Label>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="firstTimeOnly"
                checked={newCoupon.customerRestrictions.firstTimeOnly}
                onCheckedChange={(checked) =>
                  handleCheckboxChange("firstTimeOnly", checked as boolean)
                }
              />
              <Label htmlFor="firstTimeOnly">First-time customers only</Label>
            </div>

            <div className="space-y-2">
              <Label>Applicable Outlets (Optional)</Label>
              <div className="grid grid-cols-2 gap-2 max-h-32 overflow-y-auto border rounded-md p-2">
                {outlets.map((outlet) => (
                  <div key={outlet._id} className="flex items-center space-x-2">
                    <Checkbox
                      id={`outlet-${outlet._id}`}
                      checked={newCoupon.applicableOutlets.includes(outlet._id)}
                      onCheckedChange={(checked) =>
                        handleOutletSelection(outlet._id, checked as boolean)
                      }
                    />
                    <Label htmlFor={`outlet-${outlet._id}`}>
                      {outlet.name}
                    </Label>
                  </div>
                ))}
              </div>
              <p className="text-xs text-gray-500">
                If none selected, coupon applies to all outlets
              </p>
            </div>

            <div className="space-y-2">
              <Label>Applicable Dishes (Optional)</Label>
              <div className="grid grid-cols-2 gap-2 max-h-32 overflow-y-auto border rounded-md p-2">
                {dishes.map((dish) => (
                  <div key={dish._id} className="flex items-center space-x-2">
                    <Checkbox
                      id={`dish-${dish._id}`}
                      checked={newCoupon.applicableDishes.includes(dish._id)}
                      onCheckedChange={(checked) =>
                        handleDishSelection(dish._id, checked as boolean)
                      }
                    />
                    <Label htmlFor={`dish-${dish._id}`}>{dish.name}</Label>
                  </div>
                ))}
              </div>
              <p className="text-xs text-gray-500">
                If none selected, coupon applies to all dishes
              </p>
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowCreateDialog(false)}
            >
              Cancel
            </Button>
            <Button
              onClick={handleCreateCoupon}
              disabled={
                isCreating || !newCoupon.code || newCoupon.discountValue <= 0
              }
            >
              {isCreating ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Creating...
                </>
              ) : (
                "Create Coupon"
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
