/* eslint-disable react-hooks/exhaustive-deps */
"use client";

import React, { useEffect, useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  getAllInventoryItems,
  getInventoryCategories,
  getInventorySummary,
  getLowStockItems,
} from "@/server/inventory";
import { getAllOutlets } from "@/server/admin";
import {
  Loader2,
  Plus,
  Search,
  AlertTriangle,
  Package,
  History,
} from "lucide-react";
import { toast } from "sonner";
import InventoryItemDialog from "@/components/custom/inventory/InventoryItemDialog";

import { format } from "date-fns";
import InventoryQuantityDialog from "@/components/custom/inventory/InventoryQuantityDialog";
import Link from "next/link";

interface InventoryItem {
  _id: string;
  name: string;
  description?: string;
  category: string;
  unit: string;
  quantity: number;
  minQuantity: number;
  costPerUnit?: number;
  supplier?: string;
  supplierContact?: string;
  location?: string;
  expiryDate?: string;
  lastRestocked?: string;
  outletId: {
    _id: string;
    address: string;
  };
  isLowStock: boolean;
  isExpired: boolean;
  dishesUsedIn?: Array<{
    _id: string;
    name: string;
  }>;
}

interface Outlet {
  _id: string;
  name: string;
  address: string;
}

interface InventorySummary {
  totalItems: number;
  lowStockItems: number;
  expiredItems: number;
  categories: Array<{
    name: string;
    count: number;
  }>;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  recentTransactions: Array<any>;
  totalValue: number;
  averageCost: number;
}

const InventoryPage = () => {
  const [inventoryItems, setInventoryItems] = useState<InventoryItem[]>([]);
  const [lowStockItems, setLowStockItems] = useState<InventoryItem[]>([]);
  const [categories, setCategories] = useState<string[]>([]);
  const [outlets, setOutlets] = useState<Outlet[]>([]);
  const [summary, setSummary] = useState<InventorySummary | null>(null);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("all");
  const [selectedOutlet, setSelectedOutlet] = useState("all");
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [showAddDialog, setShowAddDialog] = useState(false);
  const [showQuantityDialog, setShowQuantityDialog] = useState(false);
  const [selectedItem, setSelectedItem] = useState<InventoryItem | null>(null);
  const [activeTab, setActiveTab] = useState("all");

  // Convert select values for API calls
  const getApiValue = (value: string | null): string => {
    if (value === null) return "";
    return value === "all" ? "" : value;
  };

  useEffect(() => {
    fetchInventoryData();
    fetchCategories();
    fetchOutlets();
    fetchSummary();
  }, []);

  useEffect(() => {
    fetchInventoryData();
  }, [page, searchTerm, selectedCategory, selectedOutlet, activeTab]);

  const fetchInventoryData = async () => {
    setLoading(true);
    try {
      if (activeTab === "low-stock") {
        const response = await getLowStockItems(getApiValue(selectedOutlet));
        setLowStockItems(response.data);
      } else {
        const response = await getAllInventoryItems(
          page,
          20,
          searchTerm,
          getApiValue(selectedCategory),
          getApiValue(selectedOutlet),
          false
        );
        setInventoryItems(response.data.items);
        setTotalPages(response.data.pagination.pages);
      }
    } catch (error) {
      console.error("Error fetching inventory data:", error);
      toast.error("Failed to load inventory data");
    } finally {
      setLoading(false);
    }
  };

  const fetchCategories = async () => {
    try {
      const response = await getInventoryCategories();
      setCategories(response.data);
    } catch (error) {
      console.error("Error fetching categories:", error);
    }
  };

  const fetchOutlets = async () => {
    try {
      const response = await getAllOutlets();
      setOutlets(response.data);
    } catch (error) {
      console.error("Error fetching outlets:", error);
    }
  };

  const fetchSummary = async () => {
    try {
      const response = await getInventorySummary(getApiValue(selectedOutlet));
      setSummary(response.data);
    } catch (error) {
      console.error("Error fetching inventory summary:", error);
    }
  };

  const handleAddItem = () => {
    setSelectedItem(null);
    setShowAddDialog(true);
  };

  const handleEditItem = (item: InventoryItem) => {
    setSelectedItem(item);
    setShowAddDialog(true);
  };

  const handleUpdateQuantity = (item: InventoryItem) => {
    setSelectedItem(item);
    setShowQuantityDialog(true);
  };

  const handleDialogClose = (refresh: boolean) => {
    setShowAddDialog(false);
    setShowQuantityDialog(false);
    if (refresh) {
      fetchInventoryData();
      fetchSummary();
    }
  };

  const renderInventoryTable = (items: InventoryItem[]) => {
    if (loading) {
      return (
        <div className="flex justify-center items-center py-8">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </div>
      );
    }

    if (items.length === 0) {
      return (
        <div className="text-center py-8 text-gray-500">
          <Package className="mx-auto h-12 w-12 text-gray-400" />
          <p className="mt-2">No inventory items found</p>
          <Button variant="outline" className="mt-4" onClick={handleAddItem}>
            <Plus className="mr-2 h-4 w-4" />
            Add your first inventory item
          </Button>
        </div>
      );
    }

    return (
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Name</TableHead>
            <TableHead>Category</TableHead>
            <TableHead>Quantity</TableHead>
            <TableHead>Outlet</TableHead>
            <TableHead>Status</TableHead>
            <TableHead>Last Restocked</TableHead>
            <TableHead>Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {items.map((item) => (
            <TableRow key={item._id}>
              <TableCell className="font-medium">{item.name}</TableCell>
              <TableCell>
                <Badge variant="outline">{item.category}</Badge>
              </TableCell>
              <TableCell>
                {item.quantity} {item.unit}
              </TableCell>
              <TableCell>{item.outletId?.address}</TableCell>
              <TableCell>
                {item.isLowStock && (
                  <Badge variant="destructive" className="mr-1">
                    Low Stock
                  </Badge>
                )}
                {item.isExpired && <Badge variant="destructive">Expired</Badge>}
                {!item.isLowStock && !item.isExpired && (
                  <Badge variant="outline" className="bg-green-50">
                    In Stock
                  </Badge>
                )}
              </TableCell>
              <TableCell>
                {item.lastRestocked
                  ? format(new Date(item.lastRestocked), "PPP")
                  : "—"}
              </TableCell>
              <TableCell>
                <div className="flex space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleUpdateQuantity(item)}
                  >
                    Update Qty
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleEditItem(item)}
                  >
                    Edit
                  </Button>
                </div>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    );
  };

  const renderSummaryCards = () => {
    if (!summary) return null;

    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Total Items</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{summary.totalItems}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">
              Low Stock Items
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-amber-500">
              {summary.lowStockItems}
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Expired Items</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-500">
              {summary.expiredItems}
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Total Value</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              ₹{summary.totalValue.toFixed(2)}
            </div>
          </CardContent>
        </Card>
      </div>
    );
  };

  return (
    <div className="p-4">
      <div className="mb-6">
        <div className="flex justify-between items-center mb-4">
          <h1 className="text-2xl font-bold">Inventory Management</h1>
          <div className="flex gap-2">
            <Link
              href="/admin/inventory/transactions"
              className="bg-gray-100 text-gray-800 hover:bg-gray-200 px-4 py-2 rounded-md flex items-center"
            >
              <History className="h-4 w-4 mr-2" />
              Transactions
            </Link>
            <Button onClick={handleAddItem}>
              <Plus className="h-4 w-4 mr-2" />
              Add Item
            </Button>
          </div>
        </div>
        {renderSummaryCards()}
      </div>

      <Tabs defaultValue="all" className="mb-6" onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="all">All Items</TabsTrigger>
          <TabsTrigger value="low-stock">
            Low Stock
            {summary?.lowStockItems && summary.lowStockItems > 0 && (
              <Badge variant="destructive" className="ml-2">
                {summary.lowStockItems}
              </Badge>
            )}
          </TabsTrigger>
        </TabsList>
        <TabsContent value="all">
          <Card>
            <CardHeader>
              <CardTitle>Inventory Items</CardTitle>
              <CardDescription>
                Manage your inventory items across all outlets
              </CardDescription>
              <div className="flex flex-col sm:flex-row gap-4 mt-4">
                <div className="relative flex-1">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="Search items..."
                    className="pl-10"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </div>
                <div className="flex gap-2">
                  <Select
                    value={selectedCategory}
                    onValueChange={setSelectedCategory}
                  >
                    <SelectTrigger className="w-[180px]">
                      <SelectValue placeholder="All Categories" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Categories</SelectItem>
                      {categories.map((category) => (
                        <SelectItem key={category} value={category}>
                          {category}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <Select
                    value={selectedOutlet}
                    onValueChange={setSelectedOutlet}
                  >
                    <SelectTrigger className="w-[180px]">
                      <SelectValue placeholder="All Outlets" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Outlets</SelectItem>
                      {outlets.map((outlet) => (
                        <SelectItem key={outlet._id} value={outlet._id}>
                          {outlet.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              {renderInventoryTable(inventoryItems)}
              {totalPages > 1 && (
                <div className="flex justify-center mt-4">
                  <Button
                    variant="outline"
                    onClick={() => setPage(page - 1)}
                    disabled={page === 1}
                    className="mr-2"
                  >
                    Previous
                  </Button>
                  <span className="flex items-center mx-2">
                    Page {page} of {totalPages}
                  </span>
                  <Button
                    variant="outline"
                    onClick={() => setPage(page + 1)}
                    disabled={page === totalPages}
                    className="ml-2"
                  >
                    Next
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="low-stock">
          <Card>
            <CardHeader>
              <CardTitle>Low Stock Items</CardTitle>
              <CardDescription>
                Items that need to be restocked soon
              </CardDescription>
              <div className="flex gap-2 mt-4">
                <Select
                  value={selectedOutlet}
                  onValueChange={setSelectedOutlet}
                >
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="All Outlets" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Outlets</SelectItem>
                    {outlets.map((outlet) => (
                      <SelectItem key={outlet._id} value={outlet._id}>
                        {outlet.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </CardHeader>
            <CardContent>
              {lowStockItems.length > 0 ? (
                <div className="bg-amber-50 p-4 rounded-md mb-4 flex items-center">
                  <AlertTriangle className="h-5 w-5 text-amber-500 mr-2" />
                  <p className="text-amber-700">
                    {lowStockItems.length} items are running low on stock and
                    need attention.
                  </p>
                </div>
              ) : null}
              {renderInventoryTable(lowStockItems)}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {showAddDialog && (
        <InventoryItemDialog
          item={selectedItem}
          outlets={outlets}
          categories={categories}
          onClose={handleDialogClose}
        />
      )}

      {showQuantityDialog && selectedItem && (
        <InventoryQuantityDialog
          item={selectedItem}
          outlets={outlets}
          onClose={handleDialogClose}
        />
      )}
    </div>
  );
};

export default InventoryPage;
