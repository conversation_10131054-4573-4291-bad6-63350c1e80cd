/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";
import { useEffect, useState, useCallback } from "react";
import { format } from "date-fns";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  CardFooter,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import {
  ChevronLeft,
  LogOut,
  XCircle,
  Edit3,
  Loader2,
  LoaderCircle,
} from "lucide-react";
import { useAuth } from "@/contexts/AuthContext";
import { toast } from "sonner";
import { cancelOrder } from "@/server/user";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import OrderUpdateDialog from "@/components/orders/OrderUpdateDialog";

interface OrderItem {
  dishId?: {
    _id: string;
    name: string;
    price: number;
  } | null;
  dishName?: string; // Stored dish name for deleted dishes
  quantity: number;
  price: number;
  isServed?: boolean;
  servedAt?: string;
  servedBy?: {
    _id: string;
    name: string;
  };
}

interface Order {
  _id: string;
  orderNumber: string;
  items: OrderItem[];
  totalAmount: number;
  finalAmount: number;
  status: string;
  paymentStatus: string;
  createdAt: string;
  couponCode?: string;
  couponDiscount?: number;
  discountType?: string;
  discountValue: number;
  appliedOffers?: Array<{
    offerId: string;
    offerName: string;
    offerType: string;
    discount: number;
    discountType: string;
    freeItems?: Array<{
      dishId: string;
      dishName: string;
      quantity: number;
      price: number;
    }>;
  }>;
  offerDiscount?: number;
  outletId: {
    name: string;
  };
}

const OrdersPage = () => {
  const router = useRouter();
  const { logout } = useAuth();
  const [orders, setOrders] = useState<Order[]>([]);
  const [loading, setLoading] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);
  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null);
  const [showCancelDialog, setShowCancelDialog] = useState(false);
  const [showUpdateDialog, setShowUpdateDialog] = useState(false);
  const [cancellationReason, setCancellationReason] = useState("");
  const [cancelLoading, setCancelLoading] = useState(false);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 20, // Increased from 10 to 20 for better user experience
    total: 0,
    pages: 0,
    hasNext: false,
    hasPrev: false,
  });

  const handleLogout = async () => {
    try {
      await logout();
      toast.success("Logged out successfully");
      // Redirect is handled in the logout function
    } catch (error) {
      console.error("Error signing out:", error);
      toast.error("Failed to log out. Please try again.");
    }
  };

  // Handle order cancellation
  const handleCancelOrder = async () => {
    if (!selectedOrder) return;
    if (!cancellationReason.trim()) {
      toast.error("Please provide a reason for cancellation");
      return;
    }

    setCancelLoading(true);
    try {
      const response = await cancelOrder(selectedOrder._id, cancellationReason);
      if (response.success) {
        toast.success("Order cancelled successfully");
        setShowCancelDialog(false);

        // Update the order in the list
        setOrders(
          orders.map((order) =>
            order._id === selectedOrder._id
              ? { ...order, status: "cancelled" }
              : order
          )
        );
      } else {
        toast.error(response.message || "Failed to cancel order");
      }
    } catch (error) {
      console.error("Error cancelling order:", error);
      toast.error("An error occurred while cancelling the order");
    } finally {
      setCancelLoading(false);
    }
  };

  // Open cancel dialog for an order
  const openCancelDialog = (order: Order) => {
    setSelectedOrder(order);
    setShowCancelDialog(true);
    setCancellationReason("");
  };

  const fetchOrders = useCallback(
    async (page = 1, append = false) => {
      try {
        if (append) {
          setLoadingMore(true);
        } else {
          setLoading(true);
        }

        const response = await fetch(
          `${process.env.NEXT_PUBLIC_BASE_URL}/api/v1/user/orders?page=${page}&limit=${pagination.limit}`,
          {
            headers: {
              Authorization: `Bearer ${localStorage.getItem("user-token")}`,
            },
          }
        );
        const data = await response.json();

        if (data.success) {
          if (append) {
            setOrders((prev) => [...prev, ...data.data]);
          } else {
            setOrders(data.data);
          }
          setPagination(data.pagination);
        }
      } catch (error) {
        console.error("Error fetching orders:", error);
        toast.error("Failed to load orders");
      } finally {
        setLoading(false);
        setLoadingMore(false);
      }
    },
    [pagination.limit]
  );

  const loadMoreOrders = () => {
    if (pagination.hasNext && !loadingMore) {
      fetchOrders(pagination.page + 1, true);
    }
  };

  const openUpdateDialog = (order: Order) => {
    setSelectedOrder(order);
    setShowUpdateDialog(true);
  };

  const handleOrderUpdated = () => {
    // Refresh the orders list
    fetchOrders(1, false);
  };

  useEffect(() => {
    fetchOrders();
  }, [fetchOrders]);

  const getStatusColor = (status: string) => {
    const colors = {
      pending: "bg-yellow-100 text-yellow-800",
      confirmed: "bg-blue-100 text-blue-800",
      preparing: "bg-purple-100 text-purple-800",
      ready: "bg-green-100 text-green-800",
      completed: "bg-indigo-100 text-indigo-800",
      cancelled: "bg-red-100 text-red-800",
    };
    return colors[status as keyof typeof colors] || "bg-gray-100 text-gray-800";
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-screen animate-spin">
        <LoaderCircle />
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-6">
        <Button
          variant="outline"
          onClick={() =>
            localStorage.getItem("to-chat")
              ? router.back()
              : router.push("/conversations")
          }
        >
          <ChevronLeft className="mr-1 h-4 w-4" /> Back
        </Button>
        <Button variant="outline" onClick={handleLogout}>
          <LogOut className="mr-1 h-4 w-4" /> Logout
        </Button>
      </div>
      <h1 className="md:text-2xl text-md font-bold mb-6">Order History</h1>
      <div className="space-y-4">
        {orders.map((order) => (
          <Card
            key={order._id}
            className="group border pt-0 border-gray-200 hover:border-blue-300 shadow-sm hover:shadow-lg transition-all duration-300 rounded-xl overflow-hidden bg-gradient-to-br from-white to-gray-50/30"
          >
            <CardHeader className=" bg-gradient-to-r from-blue-50/50 to-indigo-50/30 border-b border-gray-100">
              <div className="flex justify-between items-center h-full mt-2">
                <div
                  className="cursor-pointer group/title transition-all duration-200 hover:transform hover:translate-x-1"
                  onClick={() => router.push(`/order-tracking/${order._id}`)}
                >
                  <CardTitle className="text-xl font-semibold text-gray-800 group-hover/title:text-blue-600 transition-colors duration-200">
                    Order #{order.orderNumber}
                  </CardTitle>
                  <CardDescription className="text-sm text-gray-500 mt-1 flex items-center gap-1">
                    <svg
                      className="w-4 h-4"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
                      />
                    </svg>
                    {format(new Date(order.createdAt), "PPP")}
                  </CardDescription>
                </div>
                <div className="flex items-center gap-3">
                  {order.paymentStatus !== "paid" && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => openUpdateDialog(order)}
                      className="hover:bg-blue-50 hover:border-blue-300 transition-all duration-200 shadow-sm hover:shadow-md group/update"
                    >
                      <Edit3 className="mr-2 h-4 w-4 group-hover/update:rotate-12 transition-transform duration-200" />
                      <span className="hidden sm:inline">Update</span>
                    </Button>
                  )}
                  <Badge
                    className={`${getStatusColor(
                      order.status
                    )} px-3 py-1 text-xs font-medium rounded-full shadow-sm`}
                  >
                    {order.status.toUpperCase()}
                  </Badge>
                </div>
              </div>
            </CardHeader>

            <CardContent className="space-y-3">
              {/* Outlet Section */}
              <div className="flex items-start gap-3 p-3 bg-gray-50/50 rounded-lg border border-gray-100">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <svg
                    className="w-4 h-4 text-blue-600"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"
                    />
                  </svg>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-700">Outlet</p>
                  <p className="text-sm text-gray-600 font-medium">
                    {order?.outletId?.name}
                  </p>
                </div>
              </div>

              {/* Items Section */}
              <div>
                <div className="flex items-center gap-2 mb-3">
                  <div className="p-1 bg-green-100 rounded">
                    <svg
                      className="w-4 h-4 text-green-600"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"
                      />
                    </svg>
                  </div>
                  <p className="text-sm font-semibold text-gray-700">
                    Order Items
                  </p>
                </div>
                <div className="space-y-2 bg-white rounded-lg border border-gray-100 p-4">
                  {order.items.map((item, index) => (
                    <div
                      key={index}
                      className="flex justify-between items-center py-2 border-b border-gray-100 last:border-b-0"
                    >
                      <div className="flex items-center gap-3">
                        <span className="w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-xs font-medium">
                          {item.quantity}
                        </span>
                        <span className="text-sm font-medium text-gray-700">
                          {item.dishId?.name || item.dishName || (
                            <span className="text-red-500 italic">
                              Deleted Dish
                            </span>
                          )}
                        </span>
                      </div>
                      <span className="text-sm font-semibold text-gray-900">
                        ₹{item.price}
                      </span>
                    </div>
                  ))}
                </div>
              </div>

              {/* Special Instructions */}
              {(order as any)?.specialInstructions && (
                <div>
                  <div className="flex items-center gap-2 mb-3">
                    <div className="p-1 bg-yellow-100 rounded">
                      <svg
                        className="w-4 h-4 text-yellow-600"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"
                        />
                      </svg>
                    </div>
                    <p className="text-sm font-semibold text-gray-700">
                      Special Instructions
                    </p>
                  </div>
                  <div className="bg-gradient-to-r from-yellow-50 to-orange-50 border-l-4 border-yellow-400 p-4 rounded-r-lg shadow-sm">
                    <p className="text-sm text-gray-700 leading-relaxed">
                      {(order as any).specialInstructions}
                    </p>
                  </div>
                </div>
              )}

              {/* Pricing Section */}
              <div className="bg-gradient-to-r from-gray-50 to-blue-50/30 rounded-xl p-4 border border-gray-100">
                <div className="flex justify-between items-end">
                  <div className="flex flex-col gap-2">
                    <span className="text-sm font-semibold text-gray-700 flex items-center gap-2">
                      <svg
                        className="w-8 h-8 text-green-600"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 28 28"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"
                        />
                      </svg>
                      {order.couponCode && (order.couponDiscount || 0) > 0
                        ? "Final Amount"
                        : "Total Amount"}
                    </span>
                  </div>
                  <div className="text-right space-y-1">
                    {order.couponCode && (order.couponDiscount || 0) > 0 && (
                      <div className="space-y-1">
                        <div className="text-sm text-gray-500 line-through">
                          Subtotal: ₹{order.totalAmount}
                        </div>
                        <div className="text-sm text-green-600 font-medium bg-green-50 px-2 py-1 rounded-full">
                          {order.couponCode}: -₹{order.couponDiscount || 0}
                        </div>
                      </div>
                    )}
                    <div className="text-2xl font-bold text-gray-900 bg-white px-3 py-1 rounded-lg shadow-sm border border-gray-200">
                      ₹
                      {order.finalAmount.toFixed(2) ||
                        order.totalAmount -
                          (order.offerDiscount || 0) -
                          (order.couponDiscount || 0)}
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>

            <CardFooter className="flex justify-between gap-2 p-3 pt-2 bg-gray-50/30 border-t border-gray-100">
              <Button
                variant="outline"
                size="sm"
                onClick={() => router.push(`/order-tracking/${order._id}`)}
                className="flex-1 sm:flex-initial hover:bg-blue-50 hover:border-blue-300 transition-all duration-200 text-xs"
              >
                <svg
                  className="mr-1 sm:mr-2 h-3 w-3"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
                Track Order
              </Button>

              {order.status === "pending" && (
                <Button
                  variant="destructive"
                  size="sm"
                  onClick={() => openCancelDialog(order)}
                  className="hover:bg-red-600 transition-all duration-200 text-xs px-3"
                >
                  <XCircle className="mr-1 h-3 w-3" />
                  <span className="hidden sm:inline">Cancel</span>
                </Button>
              )}
            </CardFooter>
          </Card>
        ))}
      </div>

      {/* Load More Button */}
      {pagination.hasNext && (
        <div className="flex justify-center mt-6">
          <Button
            variant="outline"
            onClick={loadMoreOrders}
            disabled={loadingMore}
            className="min-w-32"
          >
            {loadingMore ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Loading...
              </>
            ) : (
              "Load More Orders"
            )}
          </Button>
        </div>
      )}

      {/* Order Update Dialog */}
      {selectedOrder && (
        <OrderUpdateDialog
          order={selectedOrder}
          isOpen={showUpdateDialog}
          onClose={() => setShowUpdateDialog(false)}
          onSuccess={handleOrderUpdated}
        />
      )}

      {/* Cancel Order Dialog */}
      <Dialog open={showCancelDialog} onOpenChange={setShowCancelDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Cancel Order</DialogTitle>
            <DialogDescription>
              Please provide a reason for cancelling this order.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="reason">Reason for cancellation</Label>
              <Textarea
                id="reason"
                placeholder="Please explain why you're cancelling this order"
                value={cancellationReason}
                onChange={(e) => setCancellationReason(e.target.value)}
              />
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowCancelDialog(false)}
              disabled={cancelLoading}
            >
              Cancel
            </Button>
            <Button
              onClick={handleCancelOrder}
              disabled={cancelLoading}
              variant="destructive"
            >
              {cancelLoading ? "Cancelling..." : "Confirm Cancellation"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default OrdersPage;
