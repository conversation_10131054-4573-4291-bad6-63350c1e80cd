"use client";

import React, { useState, useEffect, useCallback } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  MapPin,
  Phone,
  Search,
  Star,
  Truck,
  MessageCircle,
  ArrowLeft,
  Sparkles,
  X,
  ChevronDown,
} from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import ShowMenuDrawer from "@/components/custom/outlet/ShowMenuDrawer";
import { useRouter } from "next/navigation";

interface OutletWithDistance {
  _id: string;
  name: string;
  address: string;
  city: string;
  pincode: string;
  contact?: string;
  isCloudKitchen?: boolean;
  distance?: number;
  deliveryRadius?: number;
  foodChain: {
    _id: string;
    name: string;
    theme: {
      primaryColor: string;
      secondaryColor?: string;
      accentColor?: string;
      logoUrl?: string;
      favIcon?: string;
      name?: string;
    };
  };
}

interface PaginationInfo {
  total: number;
  page: number;
  limit: number;
  pages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

const OutletsPage = () => {
  const router = useRouter();
  const [outlets, setOutlets] = useState<OutletWithDistance[]>([]);
  const [recommendations, setRecommendations] = useState<OutletWithDistance[]>([]);
  const [loading, setLoading] = useState(false);
  const [pagination, setPagination] = useState<PaginationInfo>({
    total: 0,
    page: 1,
    limit: 20,
    pages: 0,
    hasNext: false,
    hasPrev: false,
  });
  
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCity, setSelectedCity] = useState("");
  const [selectedPincode, setSelectedPincode] = useState("");
  const [availableCities, setAvailableCities] = useState<string[]>([]);
  const [isFilterExpanded, setIsFilterExpanded] = useState(false);

  const showToast = (message: string, type: "success" | "error" = "success") => {
    console.log(`[${type}] ${message}`);
  };

  const fetchOutlets = useCallback(
    async (page = 1) => {
      try {
        setLoading(true);
        const params = new URLSearchParams({
          page: page.toString(),
          limit: pagination.limit.toString(),
        });

        if (searchTerm) params.append("search", searchTerm);
        if (selectedCity && selectedCity !== "all-cities")
          params.append("city", selectedCity);
        if (selectedPincode) params.append("pincode", selectedPincode);

        const response = await fetch(
          `${process.env.NEXT_PUBLIC_BASE_URL}/api/v1/user/outlets?${params}`
        );

        if (!response.ok) {
          throw new Error("Failed to fetch outlets");
        }

        const data = await response.json();

        if (data.success) {
          setOutlets(data.data);
          setPagination(data.pagination);

          const cities = [
            ...new Set(
              data.data.map((outlet: OutletWithDistance) => outlet.city)
            ),
          ].filter(Boolean) as string[];
          setAvailableCities(cities);
        } else {
          showToast("Failed to load outlets", "error");
        }
      } catch (error) {
        console.error("Error fetching outlets:", error);
        showToast("Error loading outlets", "error");
      } finally {
        setLoading(false);
      }
    },
    [searchTerm, selectedCity, selectedPincode, pagination.limit]
  );

  const fetchRecommendations = useCallback(async () => {
    try {
      const params = new URLSearchParams();
      if (selectedCity && selectedCity !== "all-cities")
        params.append("city", selectedCity);
      if (selectedPincode) params.append("pincode", selectedPincode);

      if (navigator.geolocation) {
        navigator.geolocation.getCurrentPosition(
          async (position) => {
            params.append("lat", position.coords.latitude.toString());
            params.append("lng", position.coords.longitude.toString());

            const response = await fetch(
              `${process.env.NEXT_PUBLIC_BASE_URL}/api/v1/user/intelligent-outlets?${params}`,
              {
                headers: {
                  Authorization: `Bearer ${localStorage.getItem("user-token")}`,
                },
              }
            );

            if (response.ok) {
              const data = await response.json();
              if (data.success) {
                setRecommendations(data.data.outlets || data.data);
              }
            }
          },
          async () => {
            const response = await fetch(
              `${process.env.NEXT_PUBLIC_BASE_URL}/api/v1/user/intelligent-outlets?${params}`,
              {
                headers: {
                  Authorization: `Bearer ${localStorage.getItem("user-token")}`,
                },
              }
            );

            if (response.ok) {
              const data = await response.json();
              if (data.success) {
                setRecommendations(data.data.outlets || data.data);
              }
            }
          }
        );
      } else {
        const response = await fetch(
          `${process.env.NEXT_PUBLIC_BASE_URL}/api/v1/user/intelligent-outlets?${params}`,
          {
            headers: {
              Authorization: `Bearer ${localStorage.getItem("user-token")}`,
            },
          }
        );

        if (response.ok) {
          const data = await response.json();
          if (data.success) {
            setRecommendations(data.data.outlets || data.data);
          }
        }
      }
    } catch (error) {
      console.error("Error fetching recommendations:", error);
    }
  }, [selectedCity, selectedPincode]);

  useEffect(() => {
    fetchOutlets();
    fetchRecommendations();
  }, [selectedCity, selectedPincode, fetchOutlets, fetchRecommendations]);

  useEffect(() => {
    const delayedSearch = setTimeout(() => {
      if (searchTerm !== "") {
        fetchOutlets(1);
      } else {
        fetchOutlets();
      }
    }, 500);

    return () => clearTimeout(delayedSearch);
  }, [searchTerm, fetchOutlets]);

  const handleSearch = () => {
    fetchOutlets(1);
    const outletsList = document.getElementById("outlets-list");
    if (outletsList) {
      outletsList.scrollIntoView({ behavior: "smooth" });
    }
  };

  const handleClearFilters = () => {
    setSearchTerm("");
    setSelectedCity("all-cities");
    setSelectedPincode("");
  };

  const handlePageChange = (newPage: number) => {
    fetchOutlets(newPage);
    window.scrollTo({ top: 0, behavior: "smooth" });
  };

  const hasActiveFilters = searchTerm || (selectedCity && selectedCity !== "all-cities") || selectedPincode;

  const OutletCard = ({ outlet, index }: { outlet: OutletWithDistance; index: number }) => {
    const themeColor = outlet.foodChain?.theme?.primaryColor || '#212121';
    const accentColor = outlet.foodChain?.theme?.accentColor || outlet.foodChain?.theme?.secondaryColor || themeColor;
    
    return (
      <Card 
        className="group hover:shadow-xl transition-all duration-300 hover:-translate-y-1 border-2 overflow-hidden relative"
        style={{ 
          animation: `fadeInUp 0.5s ease-out ${index * 0.05}s both`,
          borderColor: 'transparent',
        }}
      >
        <style jsx>{`
          @keyframes fadeInUp {
            from {
              opacity: 0;
              transform: translateY(20px);
            }
            to {
              opacity: 1;
              transform: translateY(0);
            }
          }
        `}</style>
        
        {/* Themed top border with gradient */}
        <div 
          className="absolute top-0 left-0 w-full h-1.5 opacity-70 group-hover:opacity-100 transition-opacity duration-300"
          style={{
            background: `linear-gradient(90deg, ${themeColor}, ${accentColor})`
          }}
        />
        
        {/* Themed accent on hover */}
        <div 
          className="absolute inset-0 opacity-0 group-hover:opacity-5 transition-opacity duration-300 pointer-events-none"
          style={{ backgroundColor: themeColor }}
        />
      
      <CardHeader className="pb-3">
        <div className="flex justify-between items-start gap-3">
          <div className="flex-1 min-w-0">
            <CardTitle className="text-lg font-bold mb-1.5 truncate group-hover:text-primary transition-colors duration-200">
              {outlet?.name}
            </CardTitle>
            <p className="text-sm text-gray-600 font-medium">
              {outlet.foodChain?.name}
            </p>
          </div>
          <Button
            size="icon"
            variant="outline"
            className="shrink-0 h-11 w-11 rounded-full hover:bg-primary hover:text-white transition-all duration-200 hover:scale-110 shadow-sm"
            onClick={() =>
              router.push(
                `/chat?chainId=${outlet.foodChain?._id}&outletId=${outlet?._id}`
              )
            }
          >
            <MessageCircle className="h-4 w-4" />
          </Button>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-3">
        <div className="flex items-start gap-2.5 p-2 rounded-lg hover:bg-gray-50 transition-colors duration-200">
          <MapPin className="h-4 w-4 text-primary mt-0.5 flex-shrink-0" />
          <div className="text-sm text-gray-600 flex-1 min-w-0">
            <p className="line-clamp-2">{outlet.address}</p>
            <p className="font-semibold text-gray-900 mt-0.5">
              {outlet.city}, {outlet.pincode}
            </p>
          </div>
        </div>

        {outlet.contact && (
          <div className="flex items-center gap-2.5 p-2 rounded-lg hover:bg-gray-50 transition-colors duration-200">
            <Phone className="h-4 w-4 text-primary flex-shrink-0" />
            <span className="text-sm text-gray-600 font-medium">{outlet.contact}</span>
          </div>
        )}

        {outlet.isCloudKitchen && (
          <Badge variant="secondary" className="gap-1.5 py-1">
            <Truck className="h-3.5 w-3.5" />
            <span>Cloud Kitchen</span>
          </Badge>
        )}

        <Separator className="my-3" />

        <div className="pt-1">
          <ShowMenuDrawer
            outletId={outlet?._id || ""}
            foodChainId={outlet.foodChain?._id || ""}
            text="View Menu"
          />
        </div>
      </CardContent>
    </Card>
  )};

  return (
    <div className="min-h-screen bg-gradient-to-b from-white to-gray-50">
      <div className="container mx-auto px-4 py-6 max-w-7xl">
        {/* Back Button */}
        <Button
          variant="ghost"
          onClick={() => router.push("/conversations")}
          className="group mb-6 hover:bg-primary/10 transition-all duration-200"
        >
          <ArrowLeft className="h-4 w-4 mr-2 transition-transform duration-200 group-hover:-translate-x-1" />
          Back to Conversations
        </Button>

        {/* Header */}
        <div className="mb-8" style={{ animation: "fadeInUp 0.7s ease-out" }}>
          <h1 className="text-4xl md:text-5xl font-bold mb-3 bg-gradient-to-r from-gray-900 to-gray-600 bg-clip-text text-transparent">
            Find Outlets Near You
          </h1>
          <p className="text-lg text-gray-600">
            Discover restaurants and cloud kitchens in your area
          </p>
        </div>

        {/* Search and Filters */}
        <Card className="mb-8 shadow-lg border-2" style={{ animation: "fadeInUp 0.7s ease-out 0.1s both" }}>
          <CardContent className="p-5">
            <div className="space-y-4">
              {/* Main Search Bar */}
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400 pointer-events-none" />
                <Input
                  placeholder="Search outlets by name..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  onKeyDown={(e) => e.key === "Enter" && handleSearch()}
                  className="pl-11 h-12 text-base border-2 focus:border-primary transition-colors duration-200"
                />
                {searchTerm && (
                  <Button
                    size="icon"
                    variant="ghost"
                    className="absolute right-1 top-1/2 transform -translate-y-1/2 h-10 w-10 hover:bg-gray-100 rounded-full transition-colors duration-200"
                    onClick={() => setSearchTerm("")}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                )}
              </div>

              {/* Filter Toggle Button */}
              <Button
                variant="outline"
                onClick={() => setIsFilterExpanded(!isFilterExpanded)}
                className="w-full justify-between hover:bg-gray-50 transition-colors duration-200 h-11"
              >
                <span className="flex items-center gap-2">
                  <Sparkles className="h-4 w-4" />
                  Advanced Filters
                  {hasActiveFilters && (
                    <Badge variant="default" className="ml-1 text-xs">Active</Badge>
                  )}
                </span>
                <ChevronDown className={`h-4 w-4 transition-transform duration-300 ${isFilterExpanded ? 'rotate-180' : ''}`} />
              </Button>

              {/* Expandable Filters */}
              <div
                style={{
                  maxHeight: isFilterExpanded ? '500px' : '0',
                  opacity: isFilterExpanded ? 1 : 0,
                  overflow: 'hidden',
                  transition: 'all 0.3s ease-in-out'
                }}
              >
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 pt-2">
                  <Select value={selectedCity} onValueChange={setSelectedCity}>
                    <SelectTrigger className="h-11 border-2">
                      <SelectValue placeholder="Select City" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all-cities">All Cities</SelectItem>
                      {availableCities.map((city) => (
                        <SelectItem key={city} value={city}>
                          {city}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>

                  <Input
                    placeholder="Enter pincode"
                    value={selectedPincode}
                    onChange={(e) => setSelectedPincode(e.target.value)}
                    className="h-11 border-2"
                  />

                  <Button 
                    variant="outline" 
                    onClick={handleClearFilters}
                    disabled={!hasActiveFilters}
                    className="h-11 hover:bg-red-50 hover:text-red-600 hover:border-red-300 transition-colors duration-200 disabled:opacity-50"
                  >
                    <X className="h-4 w-4 mr-2" />
                    Clear Filters
                  </Button>
                </div>
              </div>

              {/* Search Button */}
              <Button 
                onClick={handleSearch} 
                className="w-full h-12 text-base shadow-md hover:shadow-lg transition-all duration-200 active:scale-[0.98]"
              >
                <Search className="h-4 w-4 mr-2" />
                Search Outlets
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Recommendations Section */}
        {recommendations.length > 0 && (
          <div className="mb-10" style={{ animation: "fadeInUp 0.7s ease-out 0.2s both" }}>
            <div className="flex items-center gap-3 mb-6">
              <div className="p-2 bg-yellow-100 rounded-lg">
                <Star className="h-6 w-6 text-yellow-500" />
              </div>
              <div>
                <h2 className="text-2xl font-bold">Recommended for You</h2>
                <p className="text-sm text-gray-600">Handpicked based on your preferences</p>
              </div>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-6">
              {recommendations.slice(0, 6).map((outlet, index) => (
                <OutletCard key={outlet._id} outlet={outlet} index={index} />
              ))}
            </div>
            <Separator className="my-8" />
          </div>
        )}

        {/* All Outlets Section */}
        <div className="mb-6" style={{ animation: "fadeInUp 0.7s ease-out 0.3s both" }} id="outlets-list">
          <div className="flex items-center justify-between flex-wrap gap-3">
            <div>
              <h2 className="text-2xl font-bold">
                All Outlets
                {selectedCity && selectedCity !== "all-cities" && (
                  <span className="text-primary"> in {selectedCity}</span>
                )}
              </h2>
              <p className="text-sm text-gray-600 mt-1">
                {pagination.total} {pagination.total === 1 ? 'outlet' : 'outlets'} found
              </p>
            </div>
            
            {hasActiveFilters && (
              <Badge variant="outline" className="text-sm py-1.5 px-3">
                Filters Applied
              </Badge>
            )}
          </div>
        </div>

        {/* Outlets Grid */}
        {loading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[...Array(6)].map((_, i) => (
              <Card key={i} className="animate-pulse border-2">
                <CardHeader className="space-y-3">
                  <div className="h-5 bg-gray-200 rounded w-3/4"></div>
                  <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="h-4 bg-gray-200 rounded"></div>
                  <div className="h-4 bg-gray-200 rounded w-2/3"></div>
                  <div className="h-10 bg-gray-200 rounded mt-4"></div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : outlets.length > 0 ? (
          <>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
              {outlets.map((outlet, index) => (
                <OutletCard key={outlet._id} outlet={outlet} index={index} />
              ))}
            </div>

            {/* Pagination */}
            {pagination.pages > 1 && (
              <Card className="p-4 shadow-md">
                <div className="flex justify-center items-center gap-3 flex-wrap">
                  <Button
                    variant="outline"
                    disabled={!pagination.hasPrev}
                    onClick={() => handlePageChange(pagination.page - 1)}
                    className="hover:bg-primary/10 transition-colors duration-200"
                  >
                    Previous
                  </Button>

                  <div className="flex items-center gap-2">
                    {[...Array(pagination.pages)].map((_, i) => {
                      const pageNum = i + 1;
                      const isCurrentPage = pageNum === pagination.page;
                      const showPage =
                        pageNum === 1 ||
                        pageNum === pagination.pages ||
                        Math.abs(pageNum - pagination.page) <= 1;

                      if (!showPage) {
                        if (pageNum === 2 || pageNum === pagination.pages - 1) {
                          return <span key={i} className="text-gray-400 px-1">...</span>;
                        }
                        return null;
                      }

                      return (
                        <Button
                          key={i}
                          variant={isCurrentPage ? "default" : "outline"}
                          size="icon"
                          onClick={() => handlePageChange(pageNum)}
                          className={`transition-all duration-200 ${
                            isCurrentPage ? 'scale-110 shadow-md' : 'hover:scale-105'
                          }`}
                        >
                          {pageNum}
                        </Button>
                      );
                    })}
                  </div>

                  <Button
                    variant="outline"
                    disabled={!pagination.hasNext}
                    onClick={() => handlePageChange(pagination.page + 1)}
                    className="hover:bg-primary/10 transition-colors duration-200"
                  >
                    Next
                  </Button>
                </div>
              </Card>
            )}
          </>
        ) : (
          <Card className="shadow-lg border-2">
            <CardContent className="text-center py-16">
              <div className="p-4 bg-gray-100 rounded-full w-fit mx-auto mb-6">
                <MapPin className="h-16 w-16 text-gray-400" />
              </div>
              <h3 className="text-2xl font-bold mb-3">No outlets found</h3>
              <p className="text-gray-600 mb-6 max-w-md mx-auto">
                We couldn&apost find any outlets matching your criteria. Try adjusting your filters or search in a different area.
              </p>
              <Button 
                onClick={handleClearFilters}
                size="lg"
                className="shadow-md hover:shadow-lg transition-all duration-200 active:scale-[0.98]"
              >
                Clear All Filters
              </Button>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
};

export default OutletsPage;