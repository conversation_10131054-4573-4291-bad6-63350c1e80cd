/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";

import React, { useState, useEffect } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { toast } from "sonner";
import { ChefHat, Heart, Sparkles } from "lucide-react";

interface TasteProfile {
  dietaryPreferences: string[];
  cuisinePreferences: string[];
  spiceLevel: string;
  allergies: string[];
  eatingHabits: {
    preferredMealTimes: string[];
    portionSize: string;
    diningStyle: string;
  };
  locationPreferences: {
    preferredAreas: string[];
    maxDistance: number;
    preferCloudKitchen: boolean;
  };
  notes: string;
}

const TastePreferencesPage = () => {
  const [profile, setProfile] = useState<TasteProfile>({
    dietaryPreferences: [],
    cuisinePreferences: [],
    spiceLevel: "medium",
    allergies: [],
    eatingHabits: {
      preferredMealTimes: [],
      portionSize: "medium",
      diningStyle: "casual",
    },
    locationPreferences: {
      preferredAreas: [],
      maxDistance: 10,
      preferCloudKitchen: false,
    },
    notes: "",
  });
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);

  const dietaryOptions = [
    "Vegetarian",
    "Vegan",
    "Non-Vegetarian",
    "Jain",
    "Gluten-Free",
    "Keto",
    "Low-Carb",
    "High-Protein",
  ];

  const cuisineOptions = [
    "Indian",
    "Chinese",
    "Italian",
    "Mexican",
    "Thai",
    "Japanese",
    "Continental",
    "Mediterranean",
    "American",
    "Korean",
  ];

  // const spiceLevels = ["mild", "medium", "spicy", "extra-spicy"];
  // const portionSizes = ["small", "medium", "large"];
  // const diningStyles = ["casual", "fine-dining", "fast-food", "street-food"];
  // const mealTimes = ["breakfast", "lunch", "dinner", "snacks"];

  // const commonAllergies = [
  //   "Nuts",
  //   "Dairy",
  //   "Gluten",
  //   "Shellfish",
  //   "Eggs",
  //   "Soy",
  //   "Fish",
  //   "Sesame",
  // ];

  useEffect(() => {
    fetchTasteProfile();
  }, []);

  const fetchTasteProfile = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem("user-token");
      if (!token) {
        toast.error("Please log in to view your preferences");
        return;
      }

      const response = await fetch(
        `${process.env.NEXT_PUBLIC_BASE_URL}/api/v1/user/taste-profile`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (response.ok) {
        const data = await response.json();
        if (data.success && data.data.tasteProfile) {
          setProfile(data.data.tasteProfile);
        }
      }
    } catch (error) {
      console.error("Error fetching taste profile:", error);
      toast.error("Failed to load your preferences");
    } finally {
      setLoading(false);
    }
  };

  const saveTasteProfile = async () => {
    try {
      setSaving(true);
      const token = localStorage.getItem("user-token");
      if (!token) {
        toast.error("Please log in to save your preferences");
        return;
      }

      const response = await fetch(
        `${process.env.NEXT_PUBLIC_BASE_URL}/api/v1/user/taste-profile`,
        {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
          body: JSON.stringify(profile),
        }
      );

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          toast.success("Your taste preferences have been saved!");
        } else {
          toast.error(data.message || "Failed to save preferences");
        }
      } else {
        toast.error("Failed to save preferences");
      }
    } catch (error) {
      console.error("Error saving taste profile:", error);
      toast.error("Failed to save preferences");
    } finally {
      setSaving(false);
    }
  };

  const learnFromHistory = async () => {
    try {
      const token = localStorage.getItem("user-token");
      if (!token) {
        toast.error("Please log in to learn from your order history");
        return;
      }

      const response = await fetch(
        `${process.env.NEXT_PUBLIC_BASE_URL}/api/v1/user/learn-preferences`,
        {
          method: "POST",
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          toast.success("Successfully learned from your order history!");
          fetchTasteProfile(); // Refresh the profile
        } else {
          toast.error(data.message || "Failed to learn from order history");
        }
      } else {
        toast.error("Failed to learn from order history");
      }
    } catch (error) {
      console.error("Error learning from history:", error);
      toast.error("Failed to learn from order history");
    }
  };

  const handleArrayToggle = (
    field: keyof TasteProfile,
    value: string,
    subField?: string
  ) => {
    setProfile((prev) => {
      const newProfile = { ...prev };
      
      if (subField && typeof newProfile[field] === "object") {
        const currentArray = (newProfile[field] as any)[subField] || [];
        if (currentArray.includes(value)) {
          (newProfile[field] as any)[subField] = currentArray.filter(
            (item: string) => item !== value
          );
        } else {
          (newProfile[field] as any)[subField] = [...currentArray, value];
        }
      } else if (Array.isArray(newProfile[field])) {
        const currentArray = newProfile[field] as string[];
        if (currentArray.includes(value)) {
          (newProfile[field] as string[]) = currentArray.filter(
            (item) => item !== value
          );
        } else {
          (newProfile[field] as string[]) = [...currentArray, value];
        }
      }
      
      return newProfile;
    });
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-6 max-w-4xl">
        <div className="animate-pulse space-y-6">
          <div className="h-8 bg-gray-200 rounded w-1/3"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {[...Array(4)].map((_, i) => (
              <Card key={i}>
                <CardHeader>
                  <div className="h-6 bg-gray-200 rounded w-1/2"></div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="h-4 bg-gray-200 rounded"></div>
                    <div className="h-4 bg-gray-200 rounded w-2/3"></div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-6 max-w-4xl">
      <div className="mb-6">
        <h1 className="text-3xl font-bold mb-2 flex items-center gap-2">
          <ChefHat className="h-8 w-8" />
          Taste Preferences
        </h1>
        <p className="text-gray-600">
          Help us personalize your food recommendations by sharing your preferences
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        {/* Dietary Preferences */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Heart className="h-5 w-5" />
              Dietary Preferences
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-2">
              {dietaryOptions.map((option) => (
                <div key={option} className="flex items-center space-x-2">
                  <Checkbox
                    id={`dietary-${option}`}
                    checked={profile.dietaryPreferences.includes(option)}
                    onCheckedChange={() =>
                      handleArrayToggle("dietaryPreferences", option)
                    }
                  />
                  <Label htmlFor={`dietary-${option}`} className="text-sm">
                    {option}
                  </Label>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Cuisine Preferences */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Sparkles className="h-5 w-5" />
              Favorite Cuisines
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-2">
              {cuisineOptions.map((cuisine) => (
                <div key={cuisine} className="flex items-center space-x-2">
                  <Checkbox
                    id={`cuisine-${cuisine}`}
                    checked={profile.cuisinePreferences.includes(cuisine)}
                    onCheckedChange={() =>
                      handleArrayToggle("cuisinePreferences", cuisine)
                    }
                  />
                  <Label htmlFor={`cuisine-${cuisine}`} className="text-sm">
                    {cuisine}
                  </Label>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="flex gap-4 mb-6">
        <Button onClick={saveTasteProfile} disabled={saving}>
          {saving ? "Saving..." : "Save Preferences"}
        </Button>
        <Button variant="outline" onClick={learnFromHistory}>
          <Sparkles className="h-4 w-4 mr-2" />
          Learn from Order History
        </Button>
      </div>
    </div>
  );
};

export default TastePreferencesPage;
