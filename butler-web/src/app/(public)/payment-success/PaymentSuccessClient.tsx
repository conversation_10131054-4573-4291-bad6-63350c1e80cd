/* eslint-disable @typescript-eslint/no-explicit-any */

import { useEffect, useState } from "react";
import { useSearchParams, useRouter } from "next/navigation";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  CheckCircle,
  ChevronLeft,
  Clock,
  ShoppingBag,
  AlertCircle,
  MessageSquare,
} from "lucide-react";
import { Order, Payment } from "@/types/order";
import { Indian } from "@/lib/currency";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import CustomerFeedbackDialog from "@/components/feedback/CustomerFeedbackDialog";

export default function PaymentSuccessPage() {
  const searchParams = useSearchParams() || { get: () => null };
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [order, setOrder] = useState<Order | null>(null);
  const [payment, setPayment] = useState<Payment | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [showFeedbackDialog, setShowFeedbackDialog] = useState(false);

  // Get payment details from query parameters
  const razorpayPaymentId = searchParams.get("razorpay_payment_id");
  const razorpayOrderId = searchParams.get("razorpay_order_id");
  const razorpaySignature = searchParams.get("razorpay_signature");
  const paymentLinkId = searchParams.get("razorpay_payment_link_id");
  const paymentLinkStatus = searchParams.get("razorpay_payment_link_status");
  const paymentLinkReferenceId = searchParams.get(
    "razorpay_payment_link_reference_id"
  );

  useEffect(() => {
    const verifyPayment = async () => {
      try {
        if (!paymentLinkId) {
          setError("Payment information not found");
          setLoading(false);
          return;
        }

        // Fetch payment details from server
        const response = await fetch(
          `${process.env.NEXT_PUBLIC_BASE_URL}/api/v1/payments/verify`,
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${localStorage.getItem("user-token")}`,
            },
            body: JSON.stringify({
              razorpayPaymentId,
              razorpayOrderId,
              razorpaySignature,
              paymentLinkId,
              paymentLinkStatus,
              paymentLinkReferenceId,
            }),
          }
        );

        const data = await response.json();

        if (data.success) {
          setOrder(data.data.order);
          setPayment(data.data.payment);
        } else {
          setError(data.message || "Failed to verify payment");
        }
      } catch (error) {
        console.error("Error verifying payment:", error);
        setError("An error occurred while verifying your payment");
      } finally {
        setLoading(false);
      }
    };

    verifyPayment();
  }, [
    razorpayPaymentId,
    razorpayOrderId,
    razorpaySignature,
    paymentLinkId,
    paymentLinkStatus,
    paymentLinkReferenceId,
  ]);

  const handleViewOrder = () => {
    if (order) {
      router.push(`/order-tracking/${order._id}`);
    }
  };

  if (loading) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[70vh] p-4">
        <div className="bg-blue-50 p-8 rounded-full mb-6">
          <Clock className="animate-pulse text-blue-500 w-16 h-16" />
        </div>
        <h2 className="text-2xl font-bold mb-2">Verifying Payment</h2>
        <p className="text-gray-500 text-center max-w-md">
          Please wait while we verify your payment. This may take a few
          moments...
        </p>
      </div>
    );
  }

  if (error || !order) {
    return (
      <div className="container mx-auto max-w-md p-4">
        <Card className="border-red-100 shadow-md">
          <CardHeader className="text-center border-b pb-6">
            <div className="bg-red-50 p-6 rounded-full mx-auto w-fit mb-4">
              <AlertCircle className="h-12 w-12 text-red-500" />
            </div>
            <CardTitle className="text-xl text-red-600">
              Payment Verification Failed
            </CardTitle>
            <CardDescription>
              We couldn&apos;t verify your payment details
            </CardDescription>
          </CardHeader>
          <CardContent className="pt-6">
            <div className="bg-red-50 p-4 rounded-lg mb-6 text-red-700 text-sm">
              {error || "Unable to verify payment. Please contact support."}
            </div>
            <div className="flex flex-col gap-3">
              <Button onClick={() => router.push("/orders")} variant="default">
                <ChevronLeft className="mr-2 h-4 w-4" /> View My Orders
              </Button>
              <Button onClick={() => router.push("/")} variant="outline">
                Return to Home
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto max-w-md p-4">
      <Card className="border-green-100 shadow-md">
        <CardHeader className="text-center border-b pb-6">
          <div className="bg-green-50 p-6 rounded-full mx-auto w-fit mb-4">
            <CheckCircle className="h-12 w-12 text-green-500" />
          </div>
          <CardTitle className="text-2xl">Payment Successful!</CardTitle>
          <CardDescription>
            Your payment has been processed successfully
          </CardDescription>
          <Badge className="bg-green-100 text-green-800 mx-auto mt-2 px-3 py-1">
            PAID
          </Badge>
        </CardHeader>
        <CardContent className="pt-6">
          <div className="space-y-6">
            <div className="bg-gray-50 p-4 rounded-lg">
              <h3 className="font-medium text-gray-700 mb-3">
                Payment Details
              </h3>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-gray-600">Order Number:</span>
                  <span className="font-medium">#{order.orderNumber}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Amount Paid:</span>
                  <span className="font-medium text-green-700">
                    {Indian(payment?.amount || order.totalAmount)}
                  </span>
                </div>
                <Separator className="my-2" />
                <div className="flex justify-between">
                  <span className="text-gray-600">Payment ID:</span>
                  <span className="font-medium text-xs">
                    {razorpayPaymentId?.substring(0, 14)}...
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Date:</span>
                  <span className="font-medium">
                    {new Date().toLocaleDateString()}{" "}
                    {new Date().toLocaleTimeString()}
                  </span>
                </div>
              </div>
            </div>

            {order.items && order.items.length > 0 && (
              <div className="bg-gray-50 p-4 rounded-lg">
                <h3 className="font-medium text-gray-700 mb-3 flex items-center">
                  <ShoppingBag className="h-4 w-4 mr-2" /> Order Summary
                </h3>
                <div className="space-y-2 max-h-40 overflow-y-auto">
                  {order.items.map((item: any, index: number) => (
                    <div key={index} className="flex justify-between text-sm">
                      <span>
                        {item.dishId?.name || "Item"} × {item.quantity}
                      </span>
                      <span>{Indian(item.price * item.quantity)}</span>
                    </div>
                  ))}
                </div>
              </div>
            )}

            <div className="flex flex-col gap-3 pt-2">
              <Button onClick={handleViewOrder} className="py-6">
                Track Order Status
              </Button>
              <Button
                variant="outline"
                onClick={() => setShowFeedbackDialog(true)}
                className="border-blue-200 text-blue-600 hover:bg-blue-50"
              >
                <MessageSquare className="mr-2 h-4 w-4" />
                Share Your Experience
              </Button>
              <Button variant="outline" onClick={() => router.push("/orders")}>
                View All Orders
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Customer Feedback Dialog */}
      <CustomerFeedbackDialog
        isOpen={showFeedbackDialog}
        onClose={() => setShowFeedbackDialog(false)}
        orderId={order?._id}
        outletName={order?.outletId?.name}
        onFeedbackSubmitted={() => {
          setShowFeedbackDialog(false);
        }}
      />
    </div>
  );
}
