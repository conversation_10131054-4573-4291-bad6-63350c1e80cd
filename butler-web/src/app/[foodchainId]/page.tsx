"use client";

import React, { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import Image from "next/image";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import {
  MapPin,
  Phone,
  Mail,
  Globe,
  MessageCircle,
  Star,
  ChefHat,
  Store,
  Users,
  Search,
  X,
  Share2,
} from "lucide-react";
import { toast } from "sonner";
import { stringReducer } from "../helper/general";
import { Input } from "@/components/ui/input";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { firstLetterExtractor } from "../helper/chat";

interface Theme {
  primaryColor: string;
  secondaryColor: string;
  accentColor: string;
  logoUrl: string;
  favIcon: string;
}

interface Outlet {
  _id: string;
  name: string;
  address: string;
  contact: string;
}

interface Category {
  _id: string;
  name: string;
  description: string;
}

interface Dish {
  _id: string;
  name: string;
  description: string;
  price: number;
  image?: string;
  category: Category;
  isVeg: boolean;
  isAvailable: boolean;
  isFeatured: boolean;
  rating?: number;
}

interface FoodChain {
  _id: string;
  name: string;
  tagline?: string;
  contact: string;
  email?: string;
  website?: string;
  theme: Theme;
  outlets: Outlet[];
  categories: Category[];
  dishes: Dish[];
  stats: {
    totalOrders: number;
    totalRevenue: number;
    totalCustomers: number;
    avgOrderValue: number;
  };
  createdAt: string;
  updatedAt: string;
}

const FoodChainPublicPage = () => {
  const params = useParams();
  const router = useRouter();
  const [foodChain, setFoodChain] = useState<FoodChain | null>(null);
  const [loading, setLoading] = useState(true);
  const [search, setSearch] = useState("");
  const [selectedCategory, setSelectedCategory] = useState<string>("all");

  const foodchainId = params?.foodchainId as string;

  const fetchFoodChainData = async () => {
    try {
      setLoading(true);
      // We'll need to create a publ4[ic API endpoint for this
      const backendUrl =
        process.env.NEXT_PUBLIC_BASE_URL || "http://localhost:3001";
      const response = await fetch(
        `${backendUrl}/api/v1/public/foodchain/${foodchainId}`,
        {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
          },
        }
      );

      if (!response.ok) {
        throw new Error("Food chain not found");
      }

      const data = await response.json();
      setFoodChain(data.data);
    } catch (error) {
      console.error("Error fetching food chain:", error);
      toast.error("Food chain not found");
      // router.push("/");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (foodchainId) {
      fetchFoodChainData();
    }
  }, [foodchainId]);

  const handleChatWithOutlet = (outletId: string) => {
    // Navigate to chat with specific outlet
    router.push(`/chat?chainId=${foodchainId}&outletId=${outletId}`);
  };

  const filteredDishes =
    foodChain?.dishes
      ?.filter(
        (dish) =>
          selectedCategory === "all" || dish.category._id === selectedCategory
      )
      ?.filter((dish) =>
        search
          ? dish.name.includes(search) || dish.description.includes(search)
          : true
      ) || [];

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 p-4">
        <div className="max-w-6xl mx-auto space-y-6">
          <Skeleton className="h-64 w-full" />
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <Skeleton className="h-48" />
            <Skeleton className="h-48" />
            <Skeleton className="h-48" />
          </div>
        </div>
      </div>
    );
  }

  if (!foodChain) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-2">
            Food Chain Not Found
          </h1>
          <p className="text-gray-600 mb-4">
            The food chain you&apos;re looking for doesn&apos;t exist.
          </p>
          <Button onClick={() => router.push("/")}>Go Home</Button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 relative">
      <div
        className="absolute cursor-pointer bg-white z-40 rounded-full h-10 w-10 top-0 right-0 m-4 flex items-center justify-center"
        onClick={() => {
          if (navigator.share) {
            navigator.share({
              title: foodChain.name,
              text: `Check out ${foodChain.name} on Butler`,
              url: window.location.href,
            });
          } else {
            toast.error("Sharing not supported");
          }
        }}
      >
        <Share2 className="h-6 w-6" />
      </div>
      <div
        className="relative h-64 bg-gradient-to-r from-blue-600 to-purple-600 text-white"
        style={{
          background: `linear-gradient(135deg, ${foodChain.theme.primaryColor}, ${foodChain.theme.secondaryColor})`,
        }}
      >
        <div className="absolute inset-0 bg-black bg-opacity-30" />
        <div className="relative max-w-6xl mx-auto px-4 h-full flex items-center">
          <div className="flex items-center space-x-6">
            {foodChain.theme.logoUrl && (
              <>
                <Avatar
                  className="rounded-lg size-20"
                  style={{ color: foodChain.theme.primaryColor }}
                >
                  <AvatarImage
                    src={foodChain.theme.logoUrl}
                    alt={firstLetterExtractor(foodChain.name)}
                  />
                  <AvatarFallback>
                    {stringReducer(firstLetterExtractor(foodChain.name), 3)}
                  </AvatarFallback>
                </Avatar>
              </>
            )}
            <div>
              <h1 className="text-4xl font-bold mb-2">{foodChain.name}</h1>
              {foodChain.tagline && (
                <p className="text-xl opacity-90">{foodChain.tagline}</p>
              )}
              <div className="flex items-center space-x-4 mt-3">
                <div className="flex items-center space-x-1">
                  <Store className="w-4 h-4" />
                  <span>{foodChain.outlets.length} Outlets</span>
                </div>
                <div className="flex items-center space-x-1">
                  <ChefHat className="w-4 h-4" />
                  <span>{foodChain.dishes?.length || 0} Dishes</span>
                </div>
                <div className="flex items-center space-x-1">
                  <Users className="w-4 h-4" />
                  <span>{foodChain.stats.totalCustomers} Customers</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-6xl mx-auto px-4 py-8">
        {/* Contact Information */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Phone className="w-5 h-5" />
              <span>Contact Information</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="flex items-center space-x-2">
                <Phone className="w-4 h-4 text-gray-500" />
                <span>{foodChain.contact}</span>
              </div>
              {foodChain.email && (
                <div className="flex items-center space-x-2">
                  <Mail className="w-4 h-4 text-gray-500" />
                  <span>{foodChain.email}</span>
                </div>
              )}
              {foodChain.website && (
                <div className="flex items-center space-x-2">
                  <Globe className="w-4 h-4 text-gray-500" />
                  <a
                    href={
                      foodChain.website ||
                      window.location.origin + "/" + foodChain._id
                    }
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-blue-600 hover:underline"
                  >
                    {foodChain.website ||
                      window.location.origin + "/" + foodChain._id}
                  </a>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Outlets Section */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <MapPin className="w-5 h-5" />
              <span>Our Outlets</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {foodChain.outlets.map((outlet) => (
                <Card key={outlet._id} className="border border-gray-200">
                  <CardContent className="p-4">
                    <h3 className="font-semibold text-lg mb-2">
                      {outlet.name}
                    </h3>
                    <div className="space-y-2 text-sm text-gray-600">
                      <div className="flex items-start space-x-2">
                        <MapPin className="w-4 h-4 mt-0.5 flex-shrink-0" />
                        <span>{outlet.address}</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Phone className="w-4 h-4 flex-shrink-0" />
                        <span>{outlet.contact}</span>
                      </div>
                    </div>
                    <Button
                      onClick={() => handleChatWithOutlet(outlet._id)}
                      className="w-full mt-4"
                      style={{ backgroundColor: foodChain.theme.primaryColor }}
                    >
                      <MessageCircle className="w-4 h-4 mr-2" />
                      Chat with this outlet
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Menu Section */}
        {foodChain.dishes && foodChain.dishes.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <ChefHat className="w-5 h-5" />
                <span>Our Menu</span>
              </CardTitle>
              {/* Category Filter */}
              <div className="flex flex-wrap gap-2 mt-4">
                <Button
                  variant={selectedCategory === "all" ? "default" : "outline"}
                  size="sm"
                  onClick={() => setSelectedCategory("all")}
                  style={
                    selectedCategory === "all"
                      ? { backgroundColor: foodChain.theme.primaryColor }
                      : {}
                  }
                >
                  All Items
                </Button>
                {foodChain.categories.map((category) => (
                  <Button
                    key={category._id}
                    variant={
                      selectedCategory === category._id ? "default" : "outline"
                    }
                    size="sm"
                    onClick={() => setSelectedCategory(category._id)}
                    style={
                      selectedCategory === category._id
                        ? { backgroundColor: foodChain.theme.primaryColor }
                        : {}
                    }
                  >
                    {category.name}
                  </Button>
                ))}
              </div>
            </CardHeader>
            <CardContent>
              {foodChain.dishes.length > 0 && (
                <div className="relative">
                  <div className="absolute top-0 left-0 w-6 h-full ml-2 flex items-center bg-white opacity-50">
                    <Search className="w-6 h-6 text-gray-400" />
                  </div>
                  <Input
                    onChange={(e) => setSearch(e.target.value)}
                    className="my-4 pl-8"
                    placeholder="Search for dishes..."
                    value={search}
                  />
                  <div className="absolute top-0 right-0 w-8 h-full ml-2 flex items-center justify-center rounded-l-full bg-gray-500 opacity-50">
                    <X
                      onClick={() => setSearch("")}
                      className="w-6 h-6 text-white"
                    />
                  </div>
                </div>
              )}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {filteredDishes.map((dish) => (
                  <Card
                    key={dish._id}
                    className="border border-gray-200 overflow-hidden"
                  >
                    {dish.image && (
                      <div className="relative h-48 bg-gray-100">
                        <Image
                          src={dish.image}
                          alt={dish.name}
                          fill
                          className="object-cover"
                        />
                        {dish.isFeatured && (
                          <Badge className="absolute top-2 left-2 bg-yellow-500">
                            <Star className="w-3 h-3 mr-1" />
                            Featured
                          </Badge>
                        )}
                      </div>
                    )}
                    <CardContent className="p-4">
                      <div className="flex justify-between items-start mb-2">
                        <h3 className="font-semibold text-lg">{dish.name}</h3>
                        <span
                          className="font-bold text-lg"
                          style={{ color: foodChain.theme.primaryColor }}
                        >
                          ₹{dish.price}
                        </span>
                      </div>
                      {dish.description && (
                        <p className="text-gray-600 text-sm mb-2 line-clamp-2">
                          {dish.description}
                        </p>
                      )}
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          <Badge variant="outline" className="text-xs">
                            {stringReducer(dish.category.name, 15)}
                          </Badge>
                          <Badge>{dish.isVeg ? "🟢 Veg" : "🔴 Non-Veg"}</Badge>
                        </div>
                        {dish.rating && (
                          <div className="flex items-center space-x-1">
                            <Star className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                            <span className="text-sm font-medium">
                              {dish.rating}
                            </span>
                          </div>
                        )}
                      </div>
                      {!dish.isAvailable && (
                        <Badge variant="destructive" className="mt-2">
                          Currently Unavailable
                        </Badge>
                      )}
                    </CardContent>
                  </Card>
                ))}
              </div>
              {filteredDishes.length === 0 && (
                <div className="text-center py-8 text-gray-500">
                  <ChefHat className="w-12 h-12 mx-auto mb-4 opacity-50" />
                  <p>No dishes found in this category.</p>
                </div>
              )}
            </CardContent>
          </Card>
        )}

        {/* Call to Action */}
        <Card className="mt-8 text-center">
          <CardContent className="p-8">
            <h2 className="text-2xl font-bold mb-4">Ready to Order?</h2>
            <p className="text-gray-600 mb-6">
              Chat with any of our outlets to place your order and get
              personalized recommendations!
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              {foodChain.outlets.slice(0, 2).map((outlet) => (
                <Button
                  key={outlet._id}
                  onClick={() => handleChatWithOutlet(outlet._id)}
                  size="lg"
                  style={{ backgroundColor: foodChain.theme.primaryColor }}
                >
                  <MessageCircle className="w-5 h-5 mr-2" />
                  Chat with {outlet.name}, ({outlet.address})
                </Button>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default FoodChainPublicPage;
