import { AuthProvider } from "@/contexts/AuthContext";
import { SocketProvider } from "@/contexts/SocketContext";
import type { Metadata } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON>eist_Mono } from "next/font/google";
import "./globals.css";
import { ThemeProvider } from "@/contexts/ThemeContext";
import { ThemeProvider as NextThemesProvider } from "next-themes";
import { Toaster } from "@/components/ui/sonner";
import { Analytics } from "@vercel/analytics/next"
import UserVerifierDialog from "@/components/custom/auth/UserVerifyerDialog";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: {
    default:
      "Butler - AI-Powered Restaurant Management & Food Ordering Platform",
    template: "%s | Butler",
  },
  description:
    "Transform your restaurant business with Butler's AI-powered platform. Seamless food ordering for customers, comprehensive management tools for restaurants. Join thousands of satisfied users today.",
  keywords: [
    "restaurant management",
    "food ordering",
    "AI assistant",
    "restaurant POS",
    "online ordering",
    "food delivery",
    "restaurant analytics",
    "menu management",
    "order tracking",
    "restaurant technology",
  ],
  authors: [{ name: "Butler Team" }],
  creator: "Butler",
  publisher: "Butler",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  manifest: "/manifest.json",
  themeColor: "#3B82F6",
  appleWebApp: {
    capable: true,
    statusBarStyle: "default",
    title: "Butler",
  },
  viewport: {
    width: "device-width",
    initialScale: 1,
    maximumScale: 5,
    userScalable: true,
    viewportFit: "cover",
  },
  icons: {
    icon: "/icons/icon-192x192.png",
    shortcut: "/icons/icon-192x192.png",
    apple: "/icons/icon-192x192.png",
  },
  openGraph: {
    type: "website",
    locale: "en_US",
    url: "https://butler.com",
    title: "Butler - AI-Powered Restaurant Management & Food Ordering",
    description:
      "Transform your restaurant business with Butler's AI-powered platform. Seamless food ordering for customers, comprehensive management tools for restaurants.",
    siteName: "Butler",
    images: [
      {
        url: "/logos/butler-og.png",
        width: 1200,
        height: 630,
        alt: "Butler - Restaurant Management Platform",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: "Butler - AI-Powered Restaurant Management & Food Ordering",
    description:
      "Transform your restaurant business with Butler's AI-powered platform. Seamless food ordering for customers, comprehensive management tools for restaurants.",
    images: ["/logos/butler-og.png"],
    creator: "@butler_app",
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <head>
        <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=5, user-scalable=yes, viewport-fit=cover" />
        <link rel="apple-touch-icon" href="/icons/icon-192x192.png" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="default" />
        <meta name="theme-color" content="#ffffff" />
        <meta name="mobile-web-app-capable" content="yes" />
      </head>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased bg-white text-blue-500`}
      >
      <Analytics/>
        <AuthProvider>
          <SocketProvider>
            <NextThemesProvider attribute="class" defaultTheme="light">
              <ThemeProvider>
                {children}
                <UserVerifierDialog />
              </ThemeProvider>
            </NextThemesProvider>
          </SocketProvider>
        </AuthProvider>
        <Toaster />
      </body>
    </html>
  );
}
