export const getRoutes = (role: string) => {
  switch (role) {
    case "admin":
      return adminRoutes();
    case "super-admin":
      return superAdminRoutes();
    case "user":
      return userRoutes();
    default:
      return userRoutes();
  }
};

const adminRoutes = () => {
  // Check if campaigns are enabled
  const enableCampaigns = process.env.NEXT_PUBLIC_ENABLE_CAMPAIGNS === "true";

  const routes = [
    // {
    //   path: "/admin/dashboard",
    //   name: "Dashboard",
    //   icon: "uim:analytics",
    // },
    {
      path: "/admin/orders",
      name: "Orders",
      icon: "material-symbols:order-approve-outline-rounded",
    },
    {
      path: "/admin/analytics",
      name: "Analytics",
      icon: "material-symbols:analytics-outline",
    },
    {
      path: "/admin/outlets",
      name: "Outlets",
      icon: "hugeicons:restaurant-01",
    },
    {
      path: "/admin/dishes",
      name: "Dishes",
      icon: "hugeicons:dish-02",
    },
    {
      path: "/admin/inventory",
      name: "Inventory",
      icon: "material-symbols:inventory-2-outline",
    },
    {
      path: "/admin/employees",
      name: "Employees",
      icon: "hugeicons:waiter",
    },
    {
      path: "/admin/customers",
      name: "Customers",
      icon: "material-symbols:person-outline",
    },
    {
      path: "/admin/addons",
      name: "Add-ons",
      icon: "material-symbols:add-notes-outline",
    },
    {
      path: "/admin/admins",
      name: "Admin Users",
      icon: "material-symbols:admin-panel-settings-outline",
    },
    {
      path: "/admin/marketing",
      name: "Marketing",
      icon: "material-symbols:campaign-outline",
    },
    {
      path: "/admin/profile",
      name: "My Profile",
      icon: "material-symbols:account-circle-outline",
    },
    {
      path: "/admin/payments",
      name: "Payments",
      icon: "material-symbols:payments-rounded",
    },
    {
      path: "/admin/subscription",
      name: "Subscription",
      icon: "material-symbols:subscriptions-outline",
    },
    {
      path: "/admin/settings",
      name: "Settings",
      icon: "material-symbols:settings-rounded",
    },
  ];

  // Filter out campaigns route if disabled
  if (!enableCampaigns) {
    return routes.filter(
      (route) => route.path !== "/admin/marketing/campaigns"
    );
  }
  return routes;
};

const superAdminRoutes = () => {
  return [
    {
      path: "/super-admin/dashboard",
      name: "Dashboard",
      icon: "uim:analytics",
    },
    {
      path: "/super-admin/chain",
      name: "Chain",
      icon: "system-uicons:chain",
    },
    {
      path: "/super-admin/registration-requests",
      name: "Registration Requests",
      icon: "material-symbols:contact-mail-outline",
    },
    {
      path: "/super-admin/subscriptions",
      name: "Subscriptions",
      icon: "material-symbols:subscriptions-outline",
    },
    {
      path: "/super-admin/transfers",
      name: "Transfers",
      icon: "material-symbols:payments-rounded",
    },
    {
      path: "/super-admin/settings",
      name: "Settings",
      icon: "material-symbols:settings-rounded",
    },
  ];
};

const userRoutes = () => {
  return [
    {
      path: "/user-settings/profile",
      name: "Profile",
      icon: "material-symbols:account-circle-outline",
    },
    {
      path: "/user-settings/taste-preferences",
      name: "Taste Preferences",
      icon: "material-symbols:restaurant-menu-outline",
    },
    {
      path: "/user-settings/butler-instructions",
      name: "Butler Instructions",
      icon: "material-symbols:info-outline",
    },
    {
      path: "/conversations",
      name: "Back",
      icon: "material-symbols:arrow-back-ios-new-rounded",
    },
  ];
};
