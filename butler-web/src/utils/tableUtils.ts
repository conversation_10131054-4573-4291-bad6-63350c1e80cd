/* eslint-disable @typescript-eslint/no-explicit-any */
// Utility functions for table operations

export interface TableInfo {
  _id: string;
  name: string;
  capacity: number;
  location?: string;
  status: string;
}

// Cache for table information to avoid repeated API calls
const tableCache = new Map<string, TableInfo>();

/**
 * Fetch table information by table ID
 */
export const getTableInfo = async (
  outletId: string,
  tableId: string
): Promise<TableInfo | null> => {
  if (!outletId || !tableId) return null;

  // Check cache first
  const cacheKey = `${outletId}-${tableId}`;
  if (tableCache.has(cacheKey)) {
    return tableCache.get(cacheKey)!;
  }

  try {
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_BASE_URL}/api/v1/admin/outlets/${outletId}/tables/${tableId}`,
      {
        headers: {
          Authorization: `Bearer ${localStorage.getItem("auth-token")}`,
        },
      }
    );

    const data = await response.json();

    if (data.success && data.data) {
      const tableInfo: TableInfo = {
        _id: data.data._id,
        name: data.data.name,
        capacity: data.data.capacity,
        location: data.data.location,
        status: data.data.status,
      };

      // Cache the result
      tableCache.set(cacheKey, tableInfo);
      return tableInfo;
    }
  } catch (error) {
    console.error("Error fetching table info:", error);
  }

  return null;
};

/**
 * Fetch all tables for an outlet
 */
export const getOutletTables = async (
  outletId: string
): Promise<TableInfo[]> => {
  if (!outletId) return [];

  try {
    // Try admin endpoint first (for admin users)
    const adminToken = localStorage.getItem("auth-token");
    if (adminToken) {
      try {
        const response = await fetch(
          `${process.env.NEXT_PUBLIC_BASE_URL}/api/v1/admin/outlets/${outletId}/tables`,
          {
            headers: {
              Authorization: `Bearer ${adminToken}`,
            },
          }
        );

        const data = await response.json();

        if (data.success && data.data) {
          return data.data.map((table: any) => ({
            _id: table._id,
            name: table.name,
            capacity: table.capacity,
            location: table.location,
            status: table.status,
          }));
        }
      } catch (error: any) {
        console.log("Admin endpoint failed, trying public endpoint", error);
      }
    }

    // Fallback to public endpoint (for customers)
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_BASE_URL}/api/v1/public/outlets/${outletId}/tables`
    );

    const data = await response.json();

    if (data.success && data.data) {
      return data.data.map((table: any) => ({
        _id: table._id,
        name: table.name,
        capacity: table.capacity,
        location: table.location,
        status: table.status,
      }));
    }
  } catch (error) {
    console.error("Error fetching outlet tables:", error);
  }

  return [];
};

/**
 * Clear table cache (useful when tables are updated)
 */
export const clearTableCache = () => {
  tableCache.clear();
};

/**
 * Format table display name
 */
export const formatTableName = (table: TableInfo): string => {
  let displayName = table.name;

  if (table.location) {
    displayName += ` (${table.location})`;
  }

  displayName += ` - ${table.capacity} seats`;

  return displayName;
};
