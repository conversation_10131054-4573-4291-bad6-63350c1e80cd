#!/usr/bin/env node

/**
 * Load Testing Script for Butler Application
 * Tests the performance impact of new personalization features
 */

import http from 'http';
import https from 'https';
import { performance } from 'perf_hooks';

const BASE_URL = 'http://localhost:3001';
const CONCURRENT_USERS = [1, 5, 10, 20, 50]; // Different load levels
const TEST_DURATION = 30000; // 30 seconds per test

// Test endpoints
const ENDPOINTS = {
  outlets: '/api/v1/user/outlets?limit=10',
  intelligentOutlets: '/api/v1/user/intelligent-outlets?city=Mumbai&limit=10',
  dishes: '/api/v1/user/dishes?foodChainId=6899f5527403b47ea1df69a2&outletId=6899fa5358dbcc8f5ced8116',
  personalizedMenu: '/api/v1/user/personalized-menu?foodChainId=6899f5527403b47ea1df69a2',
  tasteProfile: '/api/v1/user/taste-profile',
  personalizedDishRecommendations: '/api/v1/user/personalized-dish-recommendations?foodChainId=6899f5527403b47ea1df69a2&userId=507f1f77bcf86cd799439011'
};

class LoadTester {
  constructor() {
    this.results = {};
    this.errors = {};
  }

  async makeRequest(endpoint, timeout = 5000) {
    return new Promise((resolve, reject) => {
      const startTime = performance.now();
      const url = new URL(endpoint, BASE_URL);
      
      const req = http.get(url, { timeout }, (res) => {
        let data = '';
        res.on('data', chunk => data += chunk);
        res.on('end', () => {
          const endTime = performance.now();
          const responseTime = endTime - startTime;
          
          resolve({
            statusCode: res.statusCode,
            responseTime,
            dataSize: data.length,
            success: res.statusCode >= 200 && res.statusCode < 300
          });
        });
      });

      req.on('error', (error) => {
        const endTime = performance.now();
        reject({
          error: error.message,
          responseTime: endTime - startTime,
          success: false
        });
      });

      req.on('timeout', () => {
        req.destroy();
        const endTime = performance.now();
        reject({
          error: 'Request timeout',
          responseTime: endTime - startTime,
          success: false
        });
      });
    });
  }

  async runConcurrentTest(endpoint, concurrentUsers, duration) {
    console.log(`\n🧪 Testing ${endpoint} with ${concurrentUsers} concurrent users for ${duration/1000}s`);
    
    const results = [];
    const errors = [];
    const startTime = Date.now();
    let requestCount = 0;

    const workers = Array(concurrentUsers).fill().map(async (_, userIndex) => {
      while (Date.now() - startTime < duration) {
        try {
          const result = await this.makeRequest(endpoint);
          results.push(result);
          requestCount++;
        } catch (error) {
          errors.push(error);
          requestCount++;
        }
        
        // Small delay between requests to simulate real user behavior
        await new Promise(resolve => setTimeout(resolve, Math.random() * 100 + 50));
      }
    });

    await Promise.all(workers);

    // Calculate statistics
    const successfulRequests = results.filter(r => r.success);
    const responseTimes = successfulRequests.map(r => r.responseTime);
    
    const stats = {
      endpoint,
      concurrentUsers,
      duration: duration / 1000,
      totalRequests: requestCount,
      successfulRequests: successfulRequests.length,
      failedRequests: errors.length,
      successRate: (successfulRequests.length / requestCount * 100).toFixed(2),
      avgResponseTime: responseTimes.length > 0 ? (responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length).toFixed(2) : 0,
      minResponseTime: responseTimes.length > 0 ? Math.min(...responseTimes).toFixed(2) : 0,
      maxResponseTime: responseTimes.length > 0 ? Math.max(...responseTimes).toFixed(2) : 0,
      requestsPerSecond: (requestCount / (duration / 1000)).toFixed(2),
      throughput: (successfulRequests.length / (duration / 1000)).toFixed(2),
      errors: errors.slice(0, 5) // Show first 5 errors
    };

    return stats;
  }

  async runPerformanceComparison() {
    console.log('🚀 Starting Butler Application Performance Testing');
    console.log('=' .repeat(60));

    const allResults = {};

    for (const [name, endpoint] of Object.entries(ENDPOINTS)) {
      console.log(`\n📊 Testing ${name.toUpperCase()} endpoint`);
      allResults[name] = {};

      for (const users of CONCURRENT_USERS) {
        try {
          const result = await this.runConcurrentTest(endpoint, users, TEST_DURATION);
          allResults[name][users] = result;
          
          console.log(`  ${users} users: ${result.successRate}% success, ${result.avgResponseTime}ms avg, ${result.throughput} req/s`);
        } catch (error) {
          console.error(`  ❌ Error testing ${users} users:`, error.message);
          allResults[name][users] = { error: error.message };
        }
      }
    }

    return allResults;
  }

  generateReport(results) {
    console.log('\n' + '='.repeat(80));
    console.log('📈 PERFORMANCE TEST REPORT');
    console.log('='.repeat(80));

    for (const [endpoint, endpointResults] of Object.entries(results)) {
      console.log(`\n🔍 ${endpoint.toUpperCase()} ENDPOINT ANALYSIS:`);
      console.log('-'.repeat(50));
      
      console.log('Users\tSuccess%\tAvg(ms)\tMax(ms)\tReq/s\tThroughput');
      console.log('-'.repeat(50));
      
      for (const [users, stats] of Object.entries(endpointResults)) {
        if (stats.error) {
          console.log(`${users}\tERROR\t${stats.error}`);
        } else {
          console.log(`${users}\t${stats.successRate}%\t${stats.avgResponseTime}\t${stats.maxResponseTime}\t${stats.requestsPerSecond}\t${stats.throughput}`);
        }
      }

      // Performance insights
      const validResults = Object.values(endpointResults).filter(r => !r.error);
      if (validResults.length > 0) {
        const avgResponseTimes = validResults.map(r => parseFloat(r.avgResponseTime));
        const maxResponseTime = Math.max(...avgResponseTimes);
        const minResponseTime = Math.min(...avgResponseTimes);
        
        console.log(`\n📊 Performance Insights:`);
        console.log(`   • Response time range: ${minResponseTime.toFixed(2)}ms - ${maxResponseTime.toFixed(2)}ms`);
        console.log(`   • Performance degradation: ${((maxResponseTime - minResponseTime) / minResponseTime * 100).toFixed(1)}%`);
        
        if (maxResponseTime > 1000) {
          console.log(`   ⚠️  High response times detected (>${maxResponseTime.toFixed(0)}ms)`);
        }
        if (validResults.some(r => parseFloat(r.successRate) < 95)) {
          console.log(`   ⚠️  Low success rate detected`);
        }
      }
    }

    // Overall recommendations
    console.log('\n🎯 RECOMMENDATIONS:');
    console.log('-'.repeat(30));
    console.log('• Monitor response times under high load');
    console.log('• Consider implementing caching for frequently accessed data');
    console.log('• Add database connection pooling if not already implemented');
    console.log('• Consider rate limiting to prevent abuse');
    console.log('• Monitor memory usage during peak loads');
  }
}

// Run the tests
async function main() {
  const tester = new LoadTester();
  
  try {
    const results = await tester.runPerformanceComparison();
    tester.generateReport(results);
  } catch (error) {
    console.error('❌ Test execution failed:', error);
  }
}

if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export default LoadTester;
