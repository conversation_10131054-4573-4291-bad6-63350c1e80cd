import mongoose from "mongoose";
import bcrypt from "bcrypt";
import dotenv from "dotenv";
import { fileURLToPath } from "url";
import { dirname, join } from "path";
import fs from "fs";
import readline from "readline";
// Get the directory name
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Load environment variables
dotenv.config();

// Define User schema
const userSchema = new mongoose.Schema({
  email: { type: String, required: true, unique: true },
  password: { type: String, required: true },
  name: { type: String, required: true },
  phone: { type: String },
  address: { type: String },
  foodChain: { type: mongoose.Schema.Types.ObjectId, ref: "FoodChain" },
  role: {
    type: String,
    enum: ["user", "admin", "super_admin"],
    default: "user",
  },
  status: {
    type: String,
    enum: ["active", "blocked"],
    default: "active",
  },
  isFirstLogin: { type: Boolean, default: false },
  createdBy: {
    type: String,
    enum: ["self", "admin", "system"],
    default: "self",
  },
  twoFactorEnabled: { type: Boolean, default: false },
  twoFactorSecret: { type: String },
  twoFactorBackupCodes: [{ type: String }],
  resetPasswordToken: { type: String },
  resetPasswordExpires: { type: Date },
  lastLogin: { type: Date },
  lastPasswordChange: { type: Date },
  failedLoginAttempts: { type: Number, default: 0 },
  lockedUntil: { type: Date },
  permissions: [{ type: String }],
  preferences: {
    darkMode: { type: Boolean, default: false },
    language: { type: String, default: "en" },
    emailNotifications: { type: Boolean, default: true },
    pushNotifications: { type: Boolean, default: true },
  },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now },
  aiMessage: { type: String },
});

const User = mongoose.model("User", userSchema);

// Read MongoDB URI from .env file
const envPath = join(__dirname, ".env");
const mongoUri = process.env.MONGODB_URI;

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout,
});

const askQuestion = (question) => {
  return new Promise((resolve) => {
    rl.question(question, (answer) => {
      resolve(answer);
    });
  });
};

// Connect to MongoDB
mongoose
  .connect(mongoUri)
  .then(async () => {
    console.log("Connected to MongoDB");
    const name = await askQuestion("What is your name? ");
    const email = await askQuestion("What is your email? ");
    const password = await askQuestion("What is your password? ");

    try {
      // Check if super admin already exists
      const existingSuperAdmin = await User.findOne({
        role: "super_admin",
        email: email,
      });
      if (existingSuperAdmin) {
        console.log(
          "\x1b[33m%s\x1b[0m",
          "Super admin already exists:\t" + existingSuperAdmin.email
        );
        process.exit(0);
      }
      // Create super admin
      const salt = await bcrypt.genSalt(10);
      const hashedPassword = await bcrypt.hash(password, salt);

      const superAdmin = new User({
        email: email,
        password: hashedPassword,
        name: "Super Admin",
        role: "super_admin",
      });

      await superAdmin.save();
      console.log(
        "\x1b[36m%s\x1b[0m",
        "Super admin created successfully:\t" + superAdmin.email
      );
    } catch (error) {
      console.error("Error creating super admin:", error);
    } finally {
      mongoose.connection.close();
    }
    rl.close();
    process.exit(0);
  })
  .catch((error) => {
    console.error("Error connecting to MongoDB:", error);
  });
