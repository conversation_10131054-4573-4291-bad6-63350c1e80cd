import Coupon from "../models/Coupon.js";
import Order from "../models/Order.js";
import Outlet from "../models/Outlet.js";

// Validate a coupon for a user
export const validateUserCoupon = async (req, res) => {
  try {
    const { code, outletId, amount } = req.body;
    const userId = req.user._id;

    if (!code || !outletId || !amount) {
      return res.status(400).json({
        success: false,
        message: "Coupon code, outlet ID, and order amount are required",
      });
    }

    // Find the outlet to get the foodChainId
    const outlet = await Outlet.findById(outletId).select("foodChain");
    if (!outlet) {
      return res.status(404).json({
        success: false,
        message: "Outlet not found",
      });
    }

    const foodChainId = outlet.foodChain;

    // Find the coupon
    const coupon = await Coupon.findOne({
      code: code.toUpperCase(),
      foodChainId,
    });

    if (!coupon) {
      return res.status(404).json({
        success: false,
        message: "Coupon not found",
      });
    }

    // Check if coupon is active
    if (!coupon.isActive) {
      return res.status(400).json({
        success: false,
        message: "Coupon is inactive",
      });
    }

    // Check if coupon is expired
    const now = new Date();
    if (now < coupon.startDate || now > coupon.endDate) {
      return res.status(400).json({
        success: false,
        message: "Coupon is not valid at this time",
      });
    }

    // Check usage limit
    if (coupon.usageLimit > 0 && coupon.usedCount >= coupon.usageLimit) {
      return res.status(400).json({
        success: false,
        message: "Coupon usage limit reached",
      });
    }

    // Check minimum order value
    if (amount < coupon.minOrderValue) {
      return res.status(400).json({
        success: false,
        message: `Minimum order value of ${coupon.minOrderValue} required`,
      });
    }

    // Check if coupon is applicable to the outlet
    if (
      coupon.applicableOutlets &&
      coupon.applicableOutlets.length > 0 &&
      !coupon.applicableOutlets.includes(outletId)
    ) {
      return res.status(400).json({
        success: false,
        message: "Coupon not applicable to this outlet",
      });
    }

    // Check customer restrictions
    if (coupon.customerRestrictions) {
      // Check if first-time only
      if (coupon.customerRestrictions.firstTimeOnly) {
        const previousOrders = await Order.countDocuments({
          userId,
          foodChainId,
        });

        if (previousOrders > 0) {
          return res.status(400).json({
            success: false,
            message: "Coupon is for first-time customers only",
          });
        }
      }

      // Check if specific customers only
      if (
        coupon.customerRestrictions.specificCustomers &&
        coupon.customerRestrictions.specificCustomers.length > 0 &&
        !coupon.customerRestrictions.specificCustomers.includes(userId)
      ) {
        return res.status(400).json({
          success: false,
          message: "Coupon not applicable to this customer",
        });
      }
    }

    // Calculate discount
    let discount = 0;
    if (coupon.discountType === "percentage") {
      discount = (amount * coupon.discountValue) / 100;
      // Apply max discount if specified
      if (coupon.maxDiscount && discount > coupon.maxDiscount) {
        discount = coupon.maxDiscount;
      }
    } else {
      // Fixed discount
      discount = coupon.discountValue;
      // Ensure discount doesn't exceed order amount
      if (discount > amount) {
        discount = amount;
      }
    }

    res.status(200).json({
      success: true,
      message: "Coupon is valid",
      data: {
        coupon: {
          code: coupon.code,
          discountType: coupon.discountType,
          discountValue: coupon.discountValue,
        },
        discount,
        finalAmount: amount - discount,
      },
    });
  } catch (error) {
    console.error("Error validating user coupon:", error);
    res.status(500).json({
      success: false,
      message: "Error validating coupon",
      error: error.message,
    });
  }
};

// Apply a coupon to an order during checkout
export const applyUserCoupon = async (req, res) => {
  try {
    const { code, orderId } = req.body;
    const userId = req.user._id;

    if (!code || !orderId) {
      return res.status(400).json({
        success: false,
        message: "Coupon code and order ID are required",
      });
    }

    // Find the order
    const order = await Order.findOne({
      _id: orderId,
      userId,
    });

    if (!order) {
      return res.status(404).json({
        success: false,
        message: "Order not found",
      });
    }

    // Find the coupon
    const coupon = await Coupon.findOne({
      code: code.toUpperCase(),
      foodChainId: order.foodChainId,
    });

    if (!coupon) {
      return res.status(404).json({
        success: false,
        message: "Coupon not found",
      });
    }

    // Validate coupon
    // Check if coupon is active
    if (!coupon.isActive) {
      return res.status(400).json({
        success: false,
        message: "Coupon is inactive",
      });
    }

    // Check if coupon is expired
    const now = new Date();
    if (now < coupon.startDate || now > coupon.endDate) {
      return res.status(400).json({
        success: false,
        message: "Coupon is not valid at this time",
      });
    }

    // Check usage limit
    if (coupon.usageLimit > 0 && coupon.usedCount >= coupon.usageLimit) {
      return res.status(400).json({
        success: false,
        message: "Coupon usage limit reached",
      });
    }

    // Check minimum order value
    if (order.totalAmount < coupon.minOrderValue) {
      return res.status(400).json({
        success: false,
        message: `Minimum order value of ${coupon.minOrderValue} required`,
      });
    }

    // Check if coupon is applicable to the outlet
    if (
      coupon.applicableOutlets &&
      coupon.applicableOutlets.length > 0 &&
      !coupon.applicableOutlets.includes(order.outletId.toString())
    ) {
      return res.status(400).json({
        success: false,
        message: "Coupon not applicable to this outlet",
      });
    }

    // Check customer restrictions
    if (coupon.customerRestrictions) {
      // Check if first-time only
      if (coupon.customerRestrictions.firstTimeOnly) {
        const previousOrders = await Order.countDocuments({
          userId,
          foodChainId: order.foodChainId,
          _id: { $ne: orderId },
        });

        if (previousOrders > 0) {
          return res.status(400).json({
            success: false,
            message: "Coupon is for first-time customers only",
          });
        }
      }

      // Check if specific customers only
      if (
        coupon.customerRestrictions.specificCustomers &&
        coupon.customerRestrictions.specificCustomers.length > 0 &&
        !coupon.customerRestrictions.specificCustomers.includes(userId)
      ) {
        return res.status(400).json({
          success: false,
          message: "Coupon not applicable to this customer",
        });
      }
    }

    // Calculate amount after offers first
    const amountAfterOffers = Math.max(0, order.totalAmount - (order.offerDiscount || 0));

    // Calculate discount based on amount after offers
    let discount = 0;
    if (coupon.discountType === "percentage") {
      discount = (amountAfterOffers * coupon.discountValue) / 100;
      // Apply max discount if specified
      if (coupon.maxDiscount && discount > coupon.maxDiscount) {
        discount = coupon.maxDiscount;
      }
    } else {
      // Fixed discount
      discount = coupon.discountValue;
      // Ensure discount doesn't exceed amount after offers
      if (discount > amountAfterOffers) {
        discount = amountAfterOffers;
      }
    }

    // Apply discount to order
    order.couponCode = coupon.code;
    order.couponDiscount = discount;
    order.finalAmount = Math.max(0, amountAfterOffers - discount);
    await order.save();

    // Increment coupon usage count
    coupon.usedCount += 1;
    await coupon.save();

    res.status(200).json({
      success: true,
      message: "Coupon applied successfully",
      data: {
        order: {
          _id: order._id,
          orderNumber: order.orderNumber,
          totalAmount: order.totalAmount,
          couponDiscount: order.couponDiscount,
          finalAmount: order.finalAmount,
        },
        discount,
      },
    });
  } catch (error) {
    console.error("Error applying user coupon:", error);
    res.status(500).json({
      success: false,
      message: "Error applying coupon",
      error: error.message,
    });
  }
};

// Get active coupons for a user
export const getActiveCoupons = async (req, res) => {
  try {
    const { outletId } = req.query;

    if (!outletId) {
      return res.status(400).json({
        success: false,
        message: "Outlet ID is required",
      });
    }

    // Find the outlet to get the foodChainId
    const outlet = await Outlet.findById(outletId).select("foodChain");
    if (!outlet) {
      return res.status(404).json({
        success: false,
        message: "Outlet not found",
      });
    }

    const foodChainId = outlet.foodChain;
    const now = new Date();

    // Query for active coupons
    const coupons = await Coupon.find({
      foodChainId,
      isActive: true,
      startDate: { $lte: now },
      endDate: { $gte: now },
      $or: [
        { applicableOutlets: { $in: [outletId] } },
        { applicableOutlets: { $size: 0 } }, // Empty array means applicable to all outlets
      ],
    }).select(
      "code description discountType discountValue minOrderValue maxDiscount"
    );

    res.status(200).json({
      success: true,
      count: coupons.length,
      data: coupons,
    });
  } catch (error) {
    console.error("Error fetching active coupons:", error);
    res.status(500).json({
      success: false,
      message: "Error fetching active coupons",
      error: error.message,
    });
  }
};
