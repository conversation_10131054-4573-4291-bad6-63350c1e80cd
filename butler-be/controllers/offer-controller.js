import Offer from "../models/Offer.js";
import Dish from "../models/Dish.js";
import Outlet from "../models/Outlet.js";

// Validation function for offer details based on type
const validateOfferDetails = (offerType, discountDetails) => {
  switch (offerType) {
    case "discount":
      if (!discountDetails.discountType || !discountDetails.discountValue) {
        return {
          isValid: false,
          message: "Discount type and value are required for discount offers",
        };
      }
      break;

    case "BOGO":
      if (!discountDetails.buyQuantity || !discountDetails.getQuantity) {
        return {
          isValid: false,
          message: "Buy and get quantities are required for BOGO offers",
        };
      }
      break;

    case "freeItem":
      if (!discountDetails.freeItemId) {
        return {
          isValid: false,
          message: "Free item is required for freeItem offers",
        };
      }
      break;

    case "combo":
      if (!discountDetails.comboItems || !discountDetails.comboPrice) {
        return {
          isValid: false,
          message: "Combo items and price are required for combo offers",
        };
      }
      break;

    case "quantityDiscount":
      if (!discountDetails.buyQuantity || !discountDetails.discountValue) {
        return {
          isValid: false,
          message:
            "Buy quantity and discount value are required for quantity discount offers",
        };
      }
      break;

    case "multiDishType":
      if (
        !discountDetails.requiredCategories ||
        !discountDetails.minimumCategoriesCount
      ) {
        return {
          isValid: false,
          message:
            "Required categories and minimum count are required for multi-dish type offers",
        };
      }
      break;

    case "minimumAmount":
      if (
        !discountDetails.minimumOrderValue ||
        !discountDetails.discountValue
      ) {
        return {
          isValid: false,
          message:
            "Minimum order value and discount value are required for minimum amount offers",
        };
      }
      break;

    case "dayOfWeek":
      if (
        !discountDetails.timeRestrictions?.daysOfWeek ||
        !discountDetails.discountValue
      ) {
        return {
          isValid: false,
          message:
            "Days of week and discount value are required for day-of-week offers",
        };
      }
      break;

    case "dateRange":
      // Date range is handled by startDate and endDate, just need discount
      if (!discountDetails.discountValue) {
        return {
          isValid: false,
          message: "Discount value is required for date range offers",
        };
      }
      break;

    case "customerTier":
      if (!discountDetails.customerTiers || !discountDetails.discountValue) {
        return {
          isValid: false,
          message:
            "Customer tiers and discount value are required for customer tier offers",
        };
      }
      break;

    case "firstTime":
      if (!discountDetails.discountValue) {
        return {
          isValid: false,
          message: "Discount value is required for first-time customer offers",
        };
      }
      break;

    case "timeBasedSpecial":
      if (!discountDetails.timeRestrictions || !discountDetails.discountValue) {
        return {
          isValid: false,
          message:
            "Time restrictions and discount value are required for time-based special offers",
        };
      }
      break;

    default:
      return { isValid: false, message: "Invalid offer type" };
  }

  return { isValid: true };
};

// Create a new offer
export const createOffer = async (req, res) => {
  try {
    console.log("Creating offer with data:", req.body);

    const {
      name,
      description,
      offerType,
      discountDetails,
      startDate,
      endDate,
      applicableOutlets,
      applicableDishes,
      termsAndConditions,
      bannerImage,
      displayOnApp,
      stackingRules,
      usageRules,
      autoApply,
    } = req.body;

    // Validate offer details based on type (simplified for now)
    if (!discountDetails) {
      return res.status(400).json({
        success: false,
        message: "Discount details are required",
      });
    }

    // Basic validation - just check if we have some discount value for most types
    if (
      [
        "discount",
        "minimumAmount",
        "quantityDiscount",
        "dateRange",
        "customerTier",
        "firstTime",
        "timeBasedSpecial",
      ].includes(offerType)
    ) {
      if (
        discountDetails.discountValue === undefined ||
        discountDetails.discountValue === null
      ) {
        return res.status(400).json({
          success: false,
          message: "Discount value is required for this offer type",
        });
      }
    }

    // Create new offer
    const newOffer = new Offer({
      name,
      description,
      offerType,
      discountDetails,
      startDate,
      endDate,
      createdBy: req.user._id,
      foodChainId: req.user.foodChain,
      applicableOutlets,
      applicableDishes,
      termsAndConditions,
      bannerImage,
      displayOnApp: displayOnApp !== undefined ? displayOnApp : true,
      stackingRules: stackingRules || {
        canStackWithOthers: false,
        priority: 0,
      },
      usageRules: usageRules || {
        usageLimit: 0,
        usedCount: 0,
        perCustomerLimit: 0,
        perOrderLimit: 1,
      },
      autoApply: autoApply || false,
      analytics: {
        totalUsage: 0,
        totalSavings: 0,
        uniqueCustomers: [],
      },
    });

    await newOffer.save();

    res.status(201).json({
      success: true,
      message: "Offer created successfully",
      data: newOffer,
    });
  } catch (error) {
    console.error("Error creating offer:", error);
    res.status(500).json({
      success: false,
      message: "Error creating offer",
      error: error.message,
    });
  }
};

// Get all offers for a food chain
export const getAllOffers = async (req, res) => {
  try {
    const { status, type, search } = req.query;
    const foodChainId = req.user.foodChain;

    let query = { foodChainId };

    // Filter by status if provided
    if (status === "active") {
      const now = new Date();
      query.isActive = true;
      query.startDate = { $lte: now };
      query.endDate = { $gte: now };
    } else if (status === "inactive") {
      query.isActive = false;
    } else if (status === "expired") {
      const now = new Date();
      query.endDate = { $lt: now };
    } else if (status === "upcoming") {
      const now = new Date();
      query.startDate = { $gt: now };
    }

    // Filter by offer type if provided
    if (type) {
      query.offerType = type;
    }

    // Search by name or description
    if (search) {
      query.$or = [
        { name: { $regex: search, $options: "i" } },
        { description: { $regex: search, $options: "i" } },
      ];
    }

    const offers = await Offer.find(query)
      .sort({ createdAt: -1 })
      .populate("createdBy", "name email")
      .populate("applicableOutlets", "name")
      .populate("applicableDishes", "name")
      .populate("discountDetails.freeItemId", "name price")
      .populate("discountDetails.comboItems.dishId", "name price");

    res.status(200).json({
      success: true,
      count: offers.length,
      data: offers,
    });
  } catch (error) {
    console.error("Error fetching offers:", error);
    res.status(500).json({
      success: false,
      message: "Error fetching offers",
      error: error.message,
    });
  }
};

// Get a single offer by ID
export const getOfferById = async (req, res) => {
  try {
    const { id } = req.params;
    const foodChainId = req.user.foodChain;

    const offer = await Offer.findOne({ _id: id, foodChainId })
      .populate("createdBy", "name email")
      .populate("applicableOutlets", "name")
      .populate("applicableDishes", "name")
      .populate("discountDetails.freeItemId", "name price")
      .populate("discountDetails.comboItems.dishId", "name price");

    if (!offer) {
      return res.status(404).json({
        success: false,
        message: "Offer not found",
      });
    }

    res.status(200).json({
      success: true,
      data: offer,
    });
  } catch (error) {
    console.error("Error fetching offer:", error);
    res.status(500).json({
      success: false,
      message: "Error fetching offer",
      error: error.message,
    });
  }
};

// Update an offer
export const updateOffer = async (req, res) => {
  try {
    const { id } = req.params;
    const foodChainId = req.user.foodChain;
    const updateData = req.body;

    // Validate offer details based on type if offerType is being updated
    if (updateData.offerType || updateData.discountDetails) {
      const offerType =
        updateData.offerType || (await Offer.findById(id)).offerType;
      const discountDetails =
        updateData.discountDetails ||
        (await Offer.findById(id)).discountDetails;

      if (offerType === "discount") {
        if (!discountDetails.discountType || !discountDetails.discountValue) {
          return res.status(400).json({
            success: false,
            message: "Discount type and value are required for discount offers",
          });
        }
      } else if (offerType === "BOGO") {
        if (!discountDetails.buyQuantity || !discountDetails.getQuantity) {
          return res.status(400).json({
            success: false,
            message: "Buy and get quantities are required for BOGO offers",
          });
        }
      } else if (offerType === "freeItem") {
        if (!discountDetails.freeItemId) {
          return res.status(400).json({
            success: false,
            message: "Free item is required for freeItem offers",
          });
        }
      } else if (offerType === "combo") {
        if (!discountDetails.comboItems || !discountDetails.comboPrice) {
          return res.status(400).json({
            success: false,
            message: "Combo items and price are required for combo offers",
          });
        }
      }
    }

    // Update the offer
    updateData.updatedAt = new Date();
    const updatedOffer = await Offer.findOneAndUpdate(
      { _id: id, foodChainId },
      updateData,
      { new: true }
    );

    if (!updatedOffer) {
      return res.status(404).json({
        success: false,
        message: "Offer not found",
      });
    }

    res.status(200).json({
      success: true,
      message: "Offer updated successfully",
      data: updatedOffer,
    });
  } catch (error) {
    console.error("Error updating offer:", error);
    res.status(500).json({
      success: false,
      message: "Error updating offer",
      error: error.message,
    });
  }
};

// Delete an offer
export const deleteOffer = async (req, res) => {
  try {
    const { id } = req.params;
    const foodChainId = req.user.foodChain;

    const deletedOffer = await Offer.findOneAndDelete({
      _id: id,
      foodChainId,
    });

    if (!deletedOffer) {
      return res.status(404).json({
        success: false,
        message: "Offer not found",
      });
    }

    res.status(200).json({
      success: true,
      message: "Offer deleted successfully",
    });
  } catch (error) {
    console.error("Error deleting offer:", error);
    res.status(500).json({
      success: false,
      message: "Error deleting offer",
      error: error.message,
    });
  }
};

// Get active offers for customer app
export const getActiveOffers = async (req, res) => {
  try {
    const { foodChainId, outletId } = req.query;

    if (!foodChainId) {
      return res.status(400).json({
        success: false,
        message: "Food chain ID is required",
      });
    }

    const now = new Date();

    // Query for active offers
    let query = {
      foodChainId,
      isActive: true,
      startDate: { $lte: now },
      endDate: { $gte: now },
      displayOnApp: true,
    };

    // Filter by outlet if provided
    if (outletId) {
      query.$or = [
        { applicableOutlets: { $in: [outletId] } },
        { applicableOutlets: { $size: 0 } }, // Empty array means applicable to all outlets
      ];
    }

    const offers = await Offer.find(query)
      .sort({ createdAt: -1 })
      .populate("applicableOutlets", "name")
      .populate("applicableDishes", "name")
      .populate("discountDetails.freeItemId", "name price")
      .populate("discountDetails.comboItems.dishId", "name price");

    res.status(200).json({
      success: true,
      count: offers.length,
      data: offers,
    });
  } catch (error) {
    console.error("Error fetching active offers:", error);
    res.status(500).json({
      success: false,
      message: "Error fetching active offers",
      error: error.message,
    });
  }
};

// Get applicable offers for an order
export const getApplicableOffers = async (req, res) => {
  try {
    const { outletId, orderAmount, customerId, items } = req.body;
    const foodChainId = req.user?.foodChain || req.body.foodChainId;

    if (!outletId || !orderAmount || !items) {
      return res.status(400).json({
        success: false,
        message: "Outlet ID, order amount, and items are required",
      });
    }

    // Get all active offers for the food chain and outlet
    const now = new Date();
    const offers = await Offer.find({
      foodChainId,
      isActive: true,
      startDate: { $lte: now },
      endDate: { $gte: now },
      $or: [
        { applicableOutlets: { $in: [outletId] } },
        { applicableOutlets: { $size: 0 } }, // Offers applicable to all outlets
      ],
    })
      .populate("discountDetails.freeItemId", "name price")
      .populate("discountDetails.comboItems.dishId", "name price")
      .populate("discountDetails.requiredCategories", "name")
      .sort({ "stackingRules.priority": -1 });

    // Filter offers based on applicability
    const applicableOffers = [];
    for (const offer of offers) {
      const applicabilityResult = await checkOfferApplicability(offer, {
        orderAmount,
        customerId,
        items,
        outletId,
      });

      if (applicabilityResult.isApplicable) {
        applicableOffers.push({
          ...offer.toObject(),
          estimatedSavings: applicabilityResult.estimatedSavings,
          canAutoApply: offer.autoApply && applicabilityResult.canAutoApply,
        });
      }
    }

    res.status(200).json({
      success: true,
      data: applicableOffers,
      count: applicableOffers.length,
    });
  } catch (error) {
    console.error("Error getting applicable offers:", error);
    res.status(500).json({
      success: false,
      message: "Error getting applicable offers",
      error: error.message,
    });
  }
};

// Helper function to check offer applicability
const checkOfferApplicability = async (offer, orderData) => {
  const { orderAmount, customerId, items, outletId } = orderData;

  // Check usage limits
  if (offer.isUsageLimitReached) {
    return { isApplicable: false, reason: "Usage limit reached" };
  }

  // Check time restrictions
  if (offer.isTimeRestricted) {
    return { isApplicable: false, reason: "Time restricted" };
  }

  // Check minimum order value
  if (
    offer.discountDetails.minimumOrderValue &&
    orderAmount < offer.discountDetails.minimumOrderValue
  ) {
    return { isApplicable: false, reason: "Minimum order value not met" };
  }

  // Check customer tier restrictions
  if (
    offer.discountDetails.customerTiers &&
    offer.discountDetails.customerTiers.length > 0
  ) {
    // This would require customer tier information - placeholder for now
    // const customerTier = await getCustomerTier(customerId);
    // if (!offer.discountDetails.customerTiers.includes(customerTier)) {
    //   return { isApplicable: false, reason: "Customer tier not eligible" };
    // }
  }

  // Check multi-dish type requirements
  if (offer.offerType === "multiDishType") {
    const itemCategories = new Set();
    items.forEach((item) => {
      if (item.category) itemCategories.add(item.category.toString());
    });

    if (itemCategories.size < offer.discountDetails.minimumCategoriesCount) {
      return { isApplicable: false, reason: "Not enough dish categories" };
    }
  }

  // Check applicable dishes
  if (offer.applicableDishes && offer.applicableDishes.length > 0) {
    const hasApplicableDish = items.some((item) =>
      offer.applicableDishes.includes(item.dishId?.toString() || item.dishId)
    );
    if (!hasApplicableDish) {
      return { isApplicable: false, reason: "No applicable dishes in order" };
    }
  }

  // Calculate estimated savings
  let estimatedSavings = 0;

  switch (offer.offerType) {
    case "discount":
    case "minimumAmount":
    case "dayOfWeek":
    case "dateRange":
    case "customerTier":
    case "firstTime":
    case "timeBasedSpecial":
      if (offer.discountDetails.discountType === "percentage") {
        estimatedSavings =
          (orderAmount * offer.discountDetails.discountValue) / 100;
        if (offer.discountDetails.maxDiscount) {
          estimatedSavings = Math.min(
            estimatedSavings,
            offer.discountDetails.maxDiscount
          );
        }
      } else {
        estimatedSavings = offer.discountDetails.discountValue;
      }
      break;

    case "BOGO":
      // Calculate BOGO savings based on applicable items
      // This is a simplified calculation
      const applicableItems = items.filter(
        (item) =>
          !offer.applicableDishes.length ||
          offer.applicableDishes.includes(item.dishId?.toString())
      );
      if (applicableItems.length >= offer.discountDetails.buyQuantity) {
        const cheapestItem = applicableItems.reduce((min, item) =>
          item.price < min.price ? item : min
        );
        estimatedSavings =
          cheapestItem.price * offer.discountDetails.getQuantity;
      }
      break;

    case "freeItem":
      // Add free item value as savings
      if (offer.discountDetails.freeItemId?.price) {
        estimatedSavings =
          offer.discountDetails.freeItemId.price *
          (offer.discountDetails.freeItemQuantity || 1);
      }
      break;
  }

  return {
    isApplicable: true,
    estimatedSavings: Math.round(estimatedSavings * 100) / 100,
    canAutoApply: true,
  };
};

// Get offer analytics and performance data
export const getOfferAnalytics = async (req, res) => {
  try {
    const { id } = req.params;
    const foodChainId = req.user.foodChain;

    // Get the offer
    const offer = await Offer.findOne({
      _id: id,
      foodChainId,
    }).populate("analytics.uniqueCustomers", "name email phone");

    if (!offer) {
      return res.status(404).json({
        success: false,
        message: "Offer not found",
      });
    }

    // Calculate additional analytics
    const now = new Date();
    const daysActive = Math.max(
      1,
      Math.ceil((now - offer.startDate) / (1000 * 60 * 60 * 24))
    );
    const daysRemaining = Math.max(
      0,
      Math.ceil((offer.endDate - now) / (1000 * 60 * 60 * 24))
    );

    const analytics = {
      ...offer.analytics,
      daysActive,
      daysRemaining,
      averageUsagePerDay: (offer.analytics?.totalUsage || 0) / daysActive,
      usageRate:
        offer.usageRules?.usageLimit > 0
          ? ((offer.usageRules?.usedCount || 0) / offer.usageRules.usageLimit) *
            100
          : 0,
      averageSavingsPerUse:
        (offer.analytics?.totalUsage || 0) > 0
          ? (offer.analytics?.totalSavings || 0) /
            (offer.analytics?.totalUsage || 1)
          : 0,
      isExpired: now > offer.endDate,
      isActive:
        offer.isActive && now >= offer.startDate && now <= offer.endDate,
    };

    res.status(200).json({
      success: true,
      data: {
        offer: offer.toObject(),
        analytics,
      },
    });
  } catch (error) {
    console.error("Error getting offer analytics:", error);
    res.status(500).json({
      success: false,
      message: "Error getting offer analytics",
      error: error.message,
    });
  }
};
