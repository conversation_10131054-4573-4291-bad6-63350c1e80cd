import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import mongoose from 'mongoose';
import { logInfo, logError } from '../utils/logger.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Get system metrics
export const getSystemMetrics = async (req, res) => {
  try {
    const metrics = {
      timestamp: new Date().toISOString(),
      server: {
        uptime: process.uptime(),
        environment: process.env.NODE_ENV || 'development',
        nodeVersion: process.version,
        platform: process.platform,
        arch: process.arch,
        pid: process.pid
      },
      memory: {
        used: Math.round(process.memoryUsage().heapUsed / 1024 / 1024 * 100) / 100,
        total: Math.round(process.memoryUsage().heapTotal / 1024 / 1024 * 100) / 100,
        external: Math.round(process.memoryUsage().external / 1024 / 1024 * 100) / 100,
        rss: Math.round(process.memoryUsage().rss / 1024 / 1024 * 100) / 100
      },
      cpu: process.cpuUsage(),
      database: {
        status: mongoose.connection.readyState === 1 ? 'connected' : 'disconnected',
        host: mongoose.connection.host,
        name: mongoose.connection.name,
        collections: mongoose.connection.db ? Object.keys(mongoose.connection.db.collections).length : 0
      }
    };

    res.status(200).json({
      success: true,
      data: metrics
    });
  } catch (error) {
    logError(error, { context: 'system_metrics' });
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve system metrics'
    });
  }
};

// Get application statistics
export const getApplicationStats = async (req, res) => {
  try {
    // Get database statistics
    const db = mongoose.connection.db;
    const collections = await db.listCollections().toArray();
    
    const stats = {
      timestamp: new Date().toISOString(),
      database: {
        collections: collections.length,
        collectionNames: collections.map(c => c.name)
      },
      api: {
        totalEndpoints: getTotalEndpoints(),
        securityFeatures: [
          'Rate Limiting',
          'Input Validation',
          'XSS Protection',
          'NoSQL Injection Protection',
          'CORS',
          'Security Headers',
          'Request Logging',
          'Error Handling'
        ]
      }
    };

    // Get collection statistics
    for (const collection of collections) {
      try {
        const count = await db.collection(collection.name).countDocuments();
        stats.database[collection.name] = { count };
      } catch (error) {
        stats.database[collection.name] = { count: 'error', error: error.message };
      }
    }

    res.status(200).json({
      success: true,
      data: stats
    });
  } catch (error) {
    logError(error, { context: 'application_stats' });
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve application statistics'
    });
  }
};

// Get log summary
export const getLogSummary = async (req, res) => {
  try {
    const logsDir = path.join(__dirname, '../logs');
    const today = new Date().toISOString().split('T')[0];
    
    const logFiles = {
      main: `butler-${today}.log`,
      error: `butler-error-${today}.log`,
      http: `butler-http-${today}.log`,
      security: `butler-security-${today}.log`,
      audit: `butler-audit-${today}.log`
    };

    const summary = {
      timestamp: new Date().toISOString(),
      logs: {}
    };

    for (const [type, filename] of Object.entries(logFiles)) {
      const filePath = path.join(logsDir, filename);
      try {
        if (fs.existsSync(filePath)) {
          const stats = fs.statSync(filePath);
          const content = fs.readFileSync(filePath, 'utf8');
          const lines = content.split('\n').filter(line => line.trim());
          
          summary.logs[type] = {
            exists: true,
            size: Math.round(stats.size / 1024 * 100) / 100, // KB
            lines: lines.length,
            lastModified: stats.mtime,
            recentEntries: lines.slice(-5) // Last 5 entries
          };
        } else {
          summary.logs[type] = {
            exists: false,
            message: 'Log file not found'
          };
        }
      } catch (error) {
        summary.logs[type] = {
          exists: false,
          error: error.message
        };
      }
    }

    res.status(200).json({
      success: true,
      data: summary
    });
  } catch (error) {
    logError(error, { context: 'log_summary' });
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve log summary'
    });
  }
};

// Get security events
export const getSecurityEvents = async (req, res) => {
  try {
    const logsDir = path.join(__dirname, '../logs');
    const today = new Date().toISOString().split('T')[0];
    const securityLogFile = path.join(logsDir, `butler-security-${today}.log`);
    
    const events = {
      timestamp: new Date().toISOString(),
      events: [],
      summary: {
        total: 0,
        types: {}
      }
    };

    if (fs.existsSync(securityLogFile)) {
      const content = fs.readFileSync(securityLogFile, 'utf8');
      const lines = content.split('\n').filter(line => line.trim());
      
      for (const line of lines) {
        try {
          const logEntry = JSON.parse(line);
          if (logEntry.message && logEntry.message.event) {
            events.events.push({
              event: logEntry.message.event,
              timestamp: logEntry.message.timestamp,
              details: logEntry.message
            });
            
            const eventType = logEntry.message.event;
            events.summary.types[eventType] = (events.summary.types[eventType] || 0) + 1;
            events.summary.total++;
          }
        } catch (parseError) {
          // Skip invalid JSON lines
        }
      }
    }

    res.status(200).json({
      success: true,
      data: events
    });
  } catch (error) {
    logError(error, { context: 'security_events' });
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve security events'
    });
  }
};

// Get performance metrics
export const getPerformanceMetrics = async (req, res) => {
  try {
    const metrics = {
      timestamp: new Date().toISOString(),
      server: {
        uptime: process.uptime(),
        memory: {
          heapUsed: process.memoryUsage().heapUsed,
          heapTotal: process.memoryUsage().heapTotal,
          external: process.memoryUsage().external,
          rss: process.memoryUsage().rss
        },
        cpu: process.cpuUsage()
      },
      database: {
        status: mongoose.connection.readyState,
        readyStates: {
          0: 'disconnected',
          1: 'connected',
          2: 'connecting',
          3: 'disconnecting'
        }[mongoose.connection.readyState]
      }
    };

    // Add database performance metrics if connected
    if (mongoose.connection.readyState === 1) {
      try {
        const adminDb = mongoose.connection.db.admin();
        const serverStatus = await adminDb.serverStatus();
        
        metrics.database.performance = {
          connections: serverStatus.connections,
          opcounters: serverStatus.opcounters,
          network: serverStatus.network
        };
      } catch (dbError) {
        metrics.database.performance = {
          error: 'Unable to retrieve database performance metrics'
        };
      }
    }

    res.status(200).json({
      success: true,
      data: metrics
    });
  } catch (error) {
    logError(error, { context: 'performance_metrics' });
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve performance metrics'
    });
  }
};

// Helper function to count total endpoints
const getTotalEndpoints = () => {
  // This is a simplified count - in a real application, you might want to
  // dynamically discover routes from Express router
  return {
    user: 15,
    admin: 8,
    public: 5,
    cart: 6,
    total: 34
  };
};

// Get comprehensive monitoring dashboard
export const getMonitoringDashboard = async (req, res) => {
  try {
    const [systemMetrics, appStats, logSummary, securityEvents, performanceMetrics] = await Promise.all([
      getSystemMetricsData(),
      getApplicationStatsData(),
      getLogSummaryData(),
      getSecurityEventsData(),
      getPerformanceMetricsData()
    ]);

    const dashboard = {
      timestamp: new Date().toISOString(),
      status: 'healthy',
      system: systemMetrics,
      application: appStats,
      logs: logSummary,
      security: securityEvents,
      performance: performanceMetrics
    };

    res.status(200).json({
      success: true,
      data: dashboard
    });
  } catch (error) {
    logError(error, { context: 'monitoring_dashboard' });
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve monitoring dashboard'
    });
  }
};

// Helper functions to get data without HTTP response
const getSystemMetricsData = async () => {
  return {
    uptime: process.uptime(),
    memory: process.memoryUsage(),
    cpu: process.cpuUsage(),
    database: {
      status: mongoose.connection.readyState === 1 ? 'connected' : 'disconnected'
    }
  };
};

const getApplicationStatsData = async () => {
  const db = mongoose.connection.db;
  const collections = await db.listCollections().toArray();
  return {
    collections: collections.length,
    endpoints: getTotalEndpoints()
  };
};

const getLogSummaryData = async () => {
  const logsDir = path.join(__dirname, '../logs');
  const today = new Date().toISOString().split('T')[0];
  
  const logFiles = ['butler', 'butler-error', 'butler-security'].map(name => `${name}-${today}.log`);
  const summary = {};
  
  for (const file of logFiles) {
    const filePath = path.join(logsDir, file);
    if (fs.existsSync(filePath)) {
      const stats = fs.statSync(filePath);
      summary[file] = {
        size: stats.size,
        lastModified: stats.mtime
      };
    }
  }
  
  return summary;
};

const getSecurityEventsData = async () => {
  const logsDir = path.join(__dirname, '../logs');
  const today = new Date().toISOString().split('T')[0];
  const securityLogFile = path.join(logsDir, `butler-security-${today}.log`);
  
  if (fs.existsSync(securityLogFile)) {
    const content = fs.readFileSync(securityLogFile, 'utf8');
    const lines = content.split('\n').filter(line => line.trim());
    return { eventCount: lines.length };
  }
  
  return { eventCount: 0 };
};

const getPerformanceMetricsData = async () => {
  return {
    memory: process.memoryUsage(),
    uptime: process.uptime()
  };
};

export default {
  getSystemMetrics,
  getApplicationStats,
  getLogSummary,
  getSecurityEvents,
  getPerformanceMetrics,
  getMonitoringDashboard
};
