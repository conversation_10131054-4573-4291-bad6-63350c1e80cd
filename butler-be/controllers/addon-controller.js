import AddOn from "../models/AddOn.js";
import Dish from "../models/Dish.js";
import { InventoryItem } from "../models/Inventory.js";

// Create add-on
export const createAddOn = async (req, res) => {
  try {
    const foodChainId = req.user.foodChain;
    const {
      name,
      description,
      price,
      type,
      isActive = true,
      inventoryItem,
      inventoryQuantityPerUnit = 1,
      trackInventory = false
    } = req.body;

    if (!name || typeof price !== "number") {
      return res.status(400).json({ success: false, message: "Name and price are required" });
    }

    // If linking to inventory, validate the inventory item exists and belongs to this food chain
    if (inventoryItem && trackInventory) {
      const inventoryExists = await InventoryItem.findOne({
        _id: inventoryItem,
        foodChainId
      });

      if (!inventoryExists) {
        return res.status(400).json({
          success: false,
          message: "Invalid inventory item or inventory item doesn't belong to this food chain"
        });
      }
    }

    const addOn = await AddOn.create({
      name,
      description,
      price,
      type,
      isActive,
      foodChain: foodChainId,
      inventoryItem: trackInventory ? inventoryItem : null,
      inventoryQuantityPerUnit,
      trackInventory
    });

    res.status(201).json({ success: true, data: addOn });
  } catch (error) {
    res.status(500).json({ success: false, message: "Failed to create add-on", error: error.message });
  }
};

// List add-ons for this food chain
export const listAddOns = async (req, res) => {
  try {
    const foodChainId = req.user.foodChain;
    const { active } = req.query;
    const query = { foodChain: foodChainId };
    if (typeof active !== "undefined") query.isActive = active === "true";
    const items = await AddOn.find(query)
      .populate('inventoryItem', 'name quantity unit')
      .sort({ updatedAt: -1 });
    res.json({ success: true, data: items });
  } catch (error) {
    res.status(500).json({ success: false, message: "Failed to fetch add-ons", error: error.message });
  }
};

// Update add-on
export const updateAddOn = async (req, res) => {
  try {
    const foodChainId = req.user.foodChain;
    const { id } = req.params;
    const updates = req.body;
    const item = await AddOn.findOneAndUpdate({ _id: id, foodChain: foodChainId }, updates, { new: true });
    if (!item) return res.status(404).json({ success: false, message: "Add-on not found" });
    res.json({ success: true, data: item });
  } catch (error) {
    res.status(500).json({ success: false, message: "Failed to update add-on", error: error.message });
  }
};

// Delete add-on
export const deleteAddOn = async (req, res) => {
  try {
    const foodChainId = req.user.foodChain;
    const { id } = req.params;
    const deleted = await AddOn.findOneAndDelete({ _id: id, foodChain: foodChainId });
    if (!deleted) return res.status(404).json({ success: false, message: "Add-on not found" });
    res.json({ success: true, message: "Add-on deleted" });
  } catch (error) {
    res.status(500).json({ success: false, message: "Failed to delete add-on", error: error.message });
  }
};

// Assign enabled add-ons to a dish
export const setDishEnabledAddOns = async (req, res) => {
  try {
    const foodChainId = req.user.foodChain;
    const { dishId } = req.params;
    const { addOnIds } = req.body; // array of AddOn IDs

    if (!Array.isArray(addOnIds)) {
      return res.status(400).json({ success: false, message: "addOnIds must be an array" });
    }

    // Ensure all add-ons belong to this food chain
    const count = await AddOn.countDocuments({ _id: { $in: addOnIds }, foodChain: foodChainId });
    if (count !== addOnIds.length) {
      return res.status(400).json({ success: false, message: "Some add-ons are invalid for this food chain" });
    }

    const dish = await Dish.findOneAndUpdate(
      { _id: dishId, foodChain: foodChainId },
      { $set: { enabledAddOns: addOnIds } },
      { new: true }
    ).populate("enabledAddOns");

    if (!dish) return res.status(404).json({ success: false, message: "Dish not found" });

    res.json({ success: true, data: dish });
  } catch (error) {
    res.status(500).json({ success: false, message: "Failed to set dish add-ons", error: error.message });
  }
};

// Import inventory item as add-on
export const importFromInventory = async (req, res) => {
  try {
    const foodChainId = req.user.foodChain;
    const { inventoryItemId, price, inventoryQuantityPerUnit = 1, type } = req.body;

    if (!inventoryItemId || typeof price !== "number") {
      return res.status(400).json({
        success: false,
        message: "Inventory item ID and price are required"
      });
    }

    // Validate inventory item exists and belongs to this food chain
    const inventoryItem = await InventoryItem.findOne({
      _id: inventoryItemId,
      foodChainId
    });

    if (!inventoryItem) {
      return res.status(404).json({
        success: false,
        message: "Inventory item not found or doesn't belong to this food chain"
      });
    }

    // Check if add-on already exists for this inventory item
    const existingAddOn = await AddOn.findOne({
      inventoryItem: inventoryItemId,
      foodChain: foodChainId
    });

    if (existingAddOn) {
      return res.status(400).json({
        success: false,
        message: "Add-on already exists for this inventory item"
      });
    }

    // Create add-on from inventory item
    const addOn = await AddOn.create({
      name: inventoryItem.name,
      description: inventoryItem.description || `Imported from inventory: ${inventoryItem.name}`,
      price,
      type: type || "imported",
      foodChain: foodChainId,
      inventoryItem: inventoryItemId,
      inventoryQuantityPerUnit,
      trackInventory: true,
      isActive: true
    });

    const populatedAddOn = await AddOn.findById(addOn._id)
      .populate('inventoryItem', 'name quantity unit');

    res.status(201).json({
      success: true,
      data: populatedAddOn,
      message: "Successfully imported inventory item as add-on"
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Failed to import from inventory",
      error: error.message
    });
  }
};

// Get available inventory items for import
export const getAvailableInventoryItems = async (req, res) => {
  try {
    const foodChainId = req.user.foodChain;
    const { outletId } = req.query;

    // Get inventory items that are not already linked to add-ons
    const existingAddOnInventoryIds = await AddOn.distinct('inventoryItem', {
      foodChain: foodChainId,
      inventoryItem: { $ne: null }
    });

    const query = {
      foodChainId,
      _id: { $nin: existingAddOnInventoryIds }
    };

    if (outletId) {
      query.outletId = outletId;
    }

    const availableItems = await InventoryItem.find(query)
      .select('name description category unit quantity')
      .sort({ name: 1 });

    res.json({
      success: true,
      data: availableItems,
      message: `Found ${availableItems.length} available inventory items`
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Failed to fetch available inventory items",
      error: error.message
    });
  }
};

// Link existing add-on to inventory item
export const linkToInventory = async (req, res) => {
  try {
    const foodChainId = req.user.foodChain;
    const { addOnId } = req.params;
    const { inventoryItemId, inventoryQuantityPerUnit = 1, trackInventory = true } = req.body;

    if (!inventoryItemId) {
      return res.status(400).json({
        success: false,
        message: "Inventory item ID is required"
      });
    }

    // Validate add-on exists and belongs to this food chain
    const addOn = await AddOn.findOne({
      _id: addOnId,
      foodChain: foodChainId
    });

    if (!addOn) {
      return res.status(404).json({
        success: false,
        message: "Add-on not found"
      });
    }

    // Validate inventory item exists and belongs to this food chain
    const inventoryItem = await InventoryItem.findOne({
      _id: inventoryItemId,
      foodChainId
    });

    if (!inventoryItem) {
      return res.status(404).json({
        success: false,
        message: "Inventory item not found or doesn't belong to this food chain"
      });
    }

    // Update add-on with inventory link
    addOn.inventoryItem = inventoryItemId;
    addOn.inventoryQuantityPerUnit = inventoryQuantityPerUnit;
    addOn.trackInventory = trackInventory;
    await addOn.save();

    const updatedAddOn = await AddOn.findById(addOn._id)
      .populate('inventoryItem', 'name quantity unit');

    res.json({
      success: true,
      data: updatedAddOn,
      message: "Successfully linked add-on to inventory item"
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Failed to link add-on to inventory",
      error: error.message
    });
  }
};

// Unlink add-on from inventory
export const unlinkFromInventory = async (req, res) => {
  try {
    const foodChainId = req.user.foodChain;
    const { addOnId } = req.params;

    const addOn = await AddOn.findOne({
      _id: addOnId,
      foodChain: foodChainId
    });

    if (!addOn) {
      return res.status(404).json({
        success: false,
        message: "Add-on not found"
      });
    }

    // Remove inventory link
    addOn.inventoryItem = null;
    addOn.inventoryQuantityPerUnit = 1;
    addOn.trackInventory = false;
    await addOn.save();

    res.json({
      success: true,
      data: addOn,
      message: "Successfully unlinked add-on from inventory"
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Failed to unlink add-on from inventory",
      error: error.message
    });
  }
};

