import CustomerFeedback from "../models/CustomerFeedback.js";
import Order from "../models/Order.js";
import Outlet from "../models/Outlet.js";

// Submit customer feedback
export const submitCustomerFeedback = async (req, res) => {
  try {
    const {
      orderId,
      rating,
      comment,
      categories,
      feedbackType = "order_experience",
      context,
    } = req.body;
    
    const userId = req.user._id;

    // Validate required fields
    if (!rating || rating < 1 || rating > 5) {
      return res.status(400).json({
        success: false,
        message: "Valid rating (1-5) is required",
      });
    }

    let outletId, foodChainId, orderValue, deliveryMethod;

    // If orderId is provided, get order details
    if (orderId) {
      const order = await Order.findById(orderId).populate("outletId");
      if (!order) {
        return res.status(404).json({
          success: false,
          message: "Order not found",
        });
      }

      // Verify the order belongs to the user
      if (order.userId.toString() !== userId.toString()) {
        return res.status(403).json({
          success: false,
          message: "Unauthorized to provide feedback for this order",
        });
      }

      outletId = order.outletId._id;
      foodChainId = order.outletId.foodChain;
      orderValue = order.finalAmount || order.totalAmount;
      deliveryMethod = order.deliveryMethod || "pickup";
    }

    // Create feedback record
    const feedback = new CustomerFeedback({
      userId,
      orderId,
      outletId,
      foodChainId,
      rating,
      comment: comment?.trim(),
      categories: categories || [],
      feedbackType,
      context: {
        ...context,
        orderValue,
        deliveryMethod,
      },
    });

    await feedback.save();

    res.status(201).json({
      success: true,
      message: "Feedback submitted successfully",
      data: {
        feedbackId: feedback._id,
        rating: feedback.rating,
      },
    });
  } catch (error) {
    console.error("Error submitting customer feedback:", error);
    res.status(500).json({
      success: false,
      message: "Error submitting feedback",
      error: error.message,
    });
  }
};

// Get customer's feedback history
export const getCustomerFeedbackHistory = async (req, res) => {
  try {
    const userId = req.user._id;
    const { page = 1, limit = 10 } = req.query;
    const skip = (page - 1) * limit;

    const feedbacks = await CustomerFeedback.find({ userId })
      .populate("orderId", "orderNumber createdAt")
      .populate("outletId", "name address")
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(parseInt(limit));

    const total = await CustomerFeedback.countDocuments({ userId });

    res.status(200).json({
      success: true,
      data: {
        feedbacks,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / limit),
        },
      },
    });
  } catch (error) {
    console.error("Error fetching feedback history:", error);
    res.status(500).json({
      success: false,
      message: "Error fetching feedback history",
      error: error.message,
    });
  }
};

// Get outlet feedback summary (for admin)
export const getOutletFeedbackSummary = async (req, res) => {
  try {
    const { outletId } = req.params;
    const { startDate, endDate, page = 1, limit = 20 } = req.query;
    const foodChainId = req.user.foodChain;

    // Verify outlet belongs to the food chain
    const outlet = await Outlet.findOne({ _id: outletId, foodChain: foodChainId });
    if (!outlet) {
      return res.status(404).json({
        success: false,
        message: "Outlet not found",
      });
    }

    // Build date filter
    const dateFilter = {};
    if (startDate && endDate) {
      dateFilter.createdAt = {
        $gte: new Date(startDate),
        $lte: new Date(endDate),
      };
    }

    // Get average rating and total feedbacks
    const averageData = await CustomerFeedback.getAverageRating(outletId);

    // Get category summary
    const categorySummary = await CustomerFeedback.getCategorySummary(
      outletId,
      startDate ? new Date(startDate) : null,
      endDate ? new Date(endDate) : null
    );

    // Get recent feedbacks with pagination
    const skip = (page - 1) * limit;
    const recentFeedbacks = await CustomerFeedback.find({
      outletId,
      ...dateFilter,
    })
      .populate("userId", "name email")
      .populate("orderId", "orderNumber")
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(parseInt(limit));

    const totalFeedbacks = await CustomerFeedback.countDocuments({
      outletId,
      ...dateFilter,
    });

    // Calculate rating distribution
    const ratingDistribution = {};
    for (let i = 1; i <= 5; i++) {
      ratingDistribution[i] = await CustomerFeedback.countDocuments({
        outletId,
        rating: i,
        ...dateFilter,
      });
    }

    res.status(200).json({
      success: true,
      data: {
        summary: {
          averageRating: averageData.averageRating || 0,
          totalFeedbacks: averageData.totalFeedbacks || 0,
          ratingDistribution,
        },
        categorySummary,
        recentFeedbacks,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: totalFeedbacks,
          pages: Math.ceil(totalFeedbacks / limit),
        },
      },
    });
  } catch (error) {
    console.error("Error fetching outlet feedback summary:", error);
    res.status(500).json({
      success: false,
      message: "Error fetching feedback summary",
      error: error.message,
    });
  }
};

// Respond to customer feedback (admin)
export const respondToFeedback = async (req, res) => {
  try {
    const { feedbackId } = req.params;
    const { message } = req.body;
    const adminId = req.user._id;
    const foodChainId = req.user.foodChain;

    if (!message?.trim()) {
      return res.status(400).json({
        success: false,
        message: "Response message is required",
      });
    }

    const feedback = await CustomerFeedback.findById(feedbackId).populate("outletId");
    if (!feedback) {
      return res.status(404).json({
        success: false,
        message: "Feedback not found",
      });
    }

    // Verify feedback belongs to the admin's food chain
    if (feedback.foodChainId.toString() !== foodChainId.toString()) {
      return res.status(403).json({
        success: false,
        message: "Unauthorized to respond to this feedback",
      });
    }

    feedback.adminResponse = {
      message: message.trim(),
      respondedBy: adminId,
      respondedAt: new Date(),
    };
    feedback.status = "responded";

    await feedback.save();

    res.status(200).json({
      success: true,
      message: "Response submitted successfully",
      data: feedback,
    });
  } catch (error) {
    console.error("Error responding to feedback:", error);
    res.status(500).json({
      success: false,
      message: "Error submitting response",
      error: error.message,
    });
  }
};

// Get food chain feedback analytics (admin)
export const getFoodChainFeedbackAnalytics = async (req, res) => {
  try {
    const foodChainId = req.user.foodChain;
    const { startDate, endDate } = req.query;

    const dateFilter = {};
    if (startDate && endDate) {
      dateFilter.createdAt = {
        $gte: new Date(startDate),
        $lte: new Date(endDate),
      };
    }

    // Get overall analytics
    const analytics = await CustomerFeedback.aggregate([
      {
        $match: {
          foodChainId: new mongoose.Types.ObjectId(foodChainId),
          ...dateFilter,
        },
      },
      {
        $group: {
          _id: null,
          totalFeedbacks: { $sum: 1 },
          averageRating: { $avg: "$rating" },
          ratingDistribution: { $push: "$rating" },
        },
      },
    ]);

    // Get outlet-wise breakdown
    const outletBreakdown = await CustomerFeedback.aggregate([
      {
        $match: {
          foodChainId: new mongoose.Types.ObjectId(foodChainId),
          ...dateFilter,
        },
      },
      {
        $group: {
          _id: "$outletId",
          totalFeedbacks: { $sum: 1 },
          averageRating: { $avg: "$rating" },
        },
      },
      {
        $lookup: {
          from: "outlets",
          localField: "_id",
          foreignField: "_id",
          as: "outlet",
        },
      },
      {
        $unwind: "$outlet",
      },
      {
        $project: {
          outletName: "$outlet.name",
          totalFeedbacks: 1,
          averageRating: 1,
        },
      },
      { $sort: { averageRating: -1 } },
    ]);

    res.status(200).json({
      success: true,
      data: {
        overall: analytics[0] || { totalFeedbacks: 0, averageRating: 0 },
        outletBreakdown,
      },
    });
  } catch (error) {
    console.error("Error fetching food chain feedback analytics:", error);
    res.status(500).json({
      success: false,
      message: "Error fetching analytics",
      error: error.message,
    });
  }
};
