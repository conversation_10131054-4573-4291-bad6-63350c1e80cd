/**
 * Table Socket Handler
 * Handles real-time table updates and notifications
 */

export const setupTableSocket = (io) => {
  const tableNamespace = io.of("/table-updates");

  // Track table connections
  const tableConnections = new Map();

  tableNamespace.on("connection", (socket) => {
    console.log(`Table socket connected: ${socket.id}`);

    // Join table room for specific outlet
    socket.on("join-table-room", (data) => {
      const { outletId, foodChainId } = data;
      
      if (!outletId || !foodChainId) {
        socket.emit("error", { message: "Missing outletId or foodChainId" });
        return;
      }

      const tableRoom = `tables-${outletId}`;
      const foodChainRoom = `foodchain-tables-${foodChainId}`;
      
      socket.join(tableRoom);
      socket.join(foodChainRoom);
      
      console.log(`Socket ${socket.id} joined table rooms: ${tableRoom}, ${foodChainRoom}`);
      
      // Track connection
      tableConnections.set(socket.id, {
        outletId,
        foodChainId,
        joinedAt: new Date(),
      });

      socket.emit("joined-table-room", {
        outletId,
        foodChainId,
        message: "Successfully joined table updates room",
      });
    });

    // Leave table room
    socket.on("leave-table-room", (data) => {
      const { outletId, foodChainId } = data;
      
      if (outletId && foodChainId) {
        const tableRoom = `tables-${outletId}`;
        const foodChainRoom = `foodchain-tables-${foodChainId}`;
        
        socket.leave(tableRoom);
        socket.leave(foodChainRoom);
        
        console.log(`Socket ${socket.id} left table rooms: ${tableRoom}, ${foodChainRoom}`);
      }
      
      tableConnections.delete(socket.id);
    });

    // Handle table status updates
    socket.on("update-table-status", (data) => {
      const { outletId, tableId, status, foodChainId } = data;
      
      if (!outletId || !tableId || !status || !foodChainId) {
        socket.emit("error", { message: "Missing required fields for table status update" });
        return;
      }

      // Broadcast to all clients in the outlet room
      const tableRoom = `tables-${outletId}`;
      tableNamespace.to(tableRoom).emit("table-status-updated", {
        type: "table-status-update",
        data: {
          outletId,
          tableId,
          status,
          updatedBy: socket.id,
          timestamp: new Date(),
        },
      });

      console.log(`Table status updated: ${tableId} -> ${status}`);
    });

    // Handle disconnect
    socket.on("disconnect", (reason) => {
      console.log(`Table socket disconnected: ${socket.id}, reason: ${reason}`);
      tableConnections.delete(socket.id);
    });

    // Handle errors
    socket.on("error", (error) => {
      console.error(`Table socket error for ${socket.id}:`, error);
    });
  });

  // Cleanup function
  const cleanup = () => {
    tableConnections.clear();
  };

  return { cleanup };
};

/**
 * Emit table availability update to all connected clients
 */
export const emitTableAvailabilityUpdate = async (
  outletId,
  tableId,
  tableData,
  foodChainId
) => {
  try {
    const io = global.io;
    if (!io) {
      console.error("Global io object not available for table updates");
      return;
    }

    const tableNamespace = io.of("/table-updates");
    const tableRoom = `tables-${outletId}`;
    const foodChainRoom = `foodchain-tables-${foodChainId}`;

    console.log(`🪑 Emitting table availability update to rooms: ${tableRoom}, ${foodChainRoom}`);

    const updateData = {
      type: "table-availability-update",
      data: {
        outletId,
        tableId,
        table: tableData,
        timestamp: new Date(),
      },
    };

    // Emit to outlet-specific room
    tableNamespace.to(tableRoom).emit("table-availability-update", updateData);
    
    // Emit to food chain room
    tableNamespace.to(foodChainRoom).emit("table-availability-update", updateData);

    console.log(`✅ Table availability update emitted for table: ${tableId}`);

    // Get connected clients count for debugging
    const tableRoomClients = tableNamespace.adapter.rooms.get(tableRoom);
    const foodChainRoomClients = tableNamespace.adapter.rooms.get(foodChainRoom);
    
    console.log(`📊 Connected clients - Table room: ${tableRoomClients ? tableRoomClients.size : 0}, Food chain room: ${foodChainRoomClients ? foodChainRoomClients.size : 0}`);
  } catch (error) {
    console.error("Error emitting table availability update:", error);
  }
};

/**
 * Emit table status update to all connected clients
 */
export const emitTableStatusUpdate = async (
  outletId,
  tableId,
  status,
  foodChainId,
  additionalData = {}
) => {
  try {
    const io = global.io;
    if (!io) {
      console.error("Global io object not available for table status updates");
      return;
    }

    const tableNamespace = io.of("/table-updates");
    const tableRoom = `tables-${outletId}`;
    const foodChainRoom = `foodchain-tables-${foodChainId}`;

    console.log(`🪑 Emitting table status update to rooms: ${tableRoom}, ${foodChainRoom}`);

    const updateData = {
      type: "table-status-update",
      data: {
        outletId,
        tableId,
        status,
        ...additionalData,
        timestamp: new Date(),
      },
    };

    // Emit to outlet-specific room
    tableNamespace.to(tableRoom).emit("table-status-update", updateData);
    
    // Emit to food chain room
    tableNamespace.to(foodChainRoom).emit("table-status-update", updateData);

    console.log(`✅ Table status update emitted for table: ${tableId} -> ${status}`);
  } catch (error) {
    console.error("Error emitting table status update:", error);
  }
};
