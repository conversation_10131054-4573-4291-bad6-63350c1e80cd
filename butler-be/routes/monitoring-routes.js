import express from 'express';
import {
  getSystemMetrics,
  getApplicationStats,
  getLogSummary,
  getSecurityEvents,
  getPerformanceMetrics,
  getMonitoringDashboard
} from '../controllers/monitoring-controller.js';
import { authenticateSuperAdminToken } from '../middlewares/auth.js';

const router = express.Router();

// All monitoring routes require super admin authentication
router.use(authenticateSuperAdminToken);

// System metrics endpoint
router.get('/system', getSystemMetrics);

// Application statistics endpoint
router.get('/stats', getApplicationStats);

// Log summary endpoint
router.get('/logs', getLogSummary);

// Security events endpoint
router.get('/security', getSecurityEvents);

// Performance metrics endpoint
router.get('/performance', getPerformanceMetrics);

// Comprehensive monitoring dashboard
router.get('/dashboard', getMonitoringDashboard);

export default router;
