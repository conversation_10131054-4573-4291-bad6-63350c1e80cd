import express from "express";
import {
  getCart,
  addToCart,
  removeFromCart,
  updateCartItemQuantity,
  clearCart,
  syncCart,
} from "../controllers/cart-controller.js";
import { authenticateToken } from "../middlewares/auth.js";
import { validateCartOperation, validateObjectIdParam } from "../middlewares/validation.js";

const router = express.Router();

// Get user's cart
router.get("/user/cart", authenticateToken, getCart);

// Add item to cart
router.post("/user/cart/add", authenticateToken, validateCartOperation, addToCart);

// Remove item from cart
router.delete("/user/cart/remove/:dishId", authenticateToken, validateObjectIdParam('dishId'), removeFromCart);

// Update item quantity in cart
router.put(
  "/user/cart/update/:dishId",
  authenticateToken,
  validateObjectIdParam('dishId'),
  validateCartOperation,
  updateCartItemQuantity
);

// Clear cart
router.delete("/user/cart/clear", authenticateToken, clearCart);

// Sync cart from localStorage to backend
router.post("/user/cart/sync", authenticateToken, syncCart);

export default router;
