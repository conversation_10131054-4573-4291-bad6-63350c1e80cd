import express from "express";
import {
  register,
  registerWithPhone,
  login,
  loginWithPhone,
  getDishes,
  getRecommendations,
  createOrder,
  getOrderHistory,
  getOrderStatus,
  getConversation,
  getAllConversations,
  deleteConversation,
  getUserDetails,
  updateUserDetails,
  updatePassword,
  cancelOrder,
  updateOrderItems,
  getOutlets,
  getOutletRecommendations,
  googleAuth,
  googleCallback,
  verifyGoogleToken,
  getAgenticRecommendations,
} from "../controllers/user-controller.js";
import {
  validateUserCoupon,
  applyUserCoupon,
  getActiveCoupons,
} from "../controllers/user-coupon-controller.js";
import { getSimilarDishes } from "../controllers/vector-controller.js";
import {
  submitCustomerFeedback,
  getCustomerFeedbackHistory,
} from "../controllers/customer-feedback-controller.js";
import { authenticateToken } from "../middlewares/auth.js";
import {
  validateUserRegistration,
  validateUserLogin,
  validatePhoneAuth,
  validateOrder<PERSON>reation,
  validateCartOperation,
  validateOutletQuery,
  validateDishQuery,
  validatePasswordUpdate,
  validateProfileUpdate,
  validateCouponApplication,
  validateFeedback,
  validateObjectIdParam
} from "../middlewares/validation.js";

const router = express.Router();

// Auth routes
router.post("/user/register", validateUserRegistration, register);
router.post("/user/register-phone", validatePhoneAuth, registerWithPhone);
router.post("/user/login", validateUserLogin, login);
router.post("/user/login-phone", validatePhoneAuth, loginWithPhone);

// Dish routes
router.get(
  "/user/dishes",
  validateDishQuery,
  //  authenticateToken,
  getDishes
);
router.get(
  "/user/conversation",
  //  authenticateToken,
  getRecommendations
);

// Agentic conversation route
router.get(
  "/user/agentic-conversation",
  //  authenticateToken,
  getAgenticRecommendations
);

router.get(
  "/user/get_conversation",
  //  authenticateToken,
  getConversation
);

router.get(
  "/user/get_all_conversations",
  //  authenticateToken,
  getAllConversations
);

router.delete(
  "/user/delete_conversation",
  //  authenticateToken,
  deleteConversation
);

router.get("/user/user_profile/:id", getUserDetails);

router.put("/user/update_profile/:id", authenticateToken, validateObjectIdParam('id'), validateProfileUpdate, updateUserDetails);

router.put("/user/update_password/:id", authenticateToken, validateObjectIdParam('id'), validatePasswordUpdate, updatePassword);

// Order routes
router.post("/user/create-order", authenticateToken, validateOrderCreation, createOrder);
router.get("/user/orders", authenticateToken, getOrderHistory);
router.get("/user/orders/:orderId", authenticateToken, validateObjectIdParam('orderId'), getOrderStatus);
router.put("/user/orders/:orderId/cancel", authenticateToken, validateObjectIdParam('orderId'), cancelOrder);
router.put(
  "/user/orders/:orderId/update-items",
  authenticateToken,
  validateObjectIdParam('orderId'),
  updateOrderItems
);

// Coupon routes
router.post("/user/coupons/validate", authenticateToken, validateCouponApplication, validateUserCoupon);
router.post("/user/coupons/apply", authenticateToken, validateCouponApplication, applyUserCoupon);
router.get("/user/coupons/active", authenticateToken, getActiveCoupons);

// Vector search route
router.get("/user/vector/similar-dishes", getSimilarDishes);

// Outlet routes (public access for browsing)
router.get("/user/outlets", validateOutletQuery, getOutlets);
router.get("/user/outlets/recommendations", validateOutletQuery, getOutletRecommendations);

// Google OAuth routes
router.get("/user/auth/google", googleAuth);
router.get("/user/auth/google/callback", googleCallback);
router.post("/user/auth/google/verify", verifyGoogleToken);

// Customer Feedback routes
router.post("/user/feedback/submit", authenticateToken, validateFeedback, submitCustomerFeedback);
router.get("/user/feedback/history", authenticateToken, getCustomerFeedbackHistory);

export default router;
