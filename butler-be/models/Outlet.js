import mongoose from "mongoose";

const operatingHoursSchema = new mongoose.Schema({
  day: {
    type: String,
    required: true,
    enum: [
      "monday",
      "tuesday",
      "wednesday",
      "thursday",
      "friday",
      "saturday",
      "sunday",
    ],
  },
  isOpen: { type: Boolean, default: true },
  openTime: { type: String, default: "09:00" },
  closeTime: { type: String, default: "22:00" },
});

const paymentSettingsSchema = new mongoose.Schema({
  acceptCash: { type: Boolean, default: true },
  acceptOnline: { type: Boolean, default: true },
  defaultPaymentMethod: {
    type: String,
    enum: ["cash", "online"],
    default: "cash",
  },
});

const staffAssignmentSchema = new mongoose.Schema({
  userId: { type: mongoose.Schema.Types.ObjectId, ref: "User", required: true },
  role: {
    type: String,
    enum: ["manager", "chef", "delivery", "cashier", "waiter"],
    required: true,
  },
  isActive: { type: Boolean, default: true },
});

const outletSchema = new mongoose.Schema({
  name: { type: String, required: true },
  address: { type: String, required: true },
  city: { type: String, required: true },
  pincode: { type: String, required: true },
  contact: { type: String },
  foodChain: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "FoodChain",
    required: true,
  },
  status: { type: String, enum: ["active", "inactive"], default: "active" },
  operatingHours: [operatingHoursSchema],
  paymentSettings: { type: paymentSettingsSchema, default: () => ({}) },
  deliveryRadius: { type: Number, default: 5 }, // in kilometers
  deliveryZones: [{ type: String }], // list of area codes or zone names
  staff: [staffAssignmentSchema],
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now },
  isCloudKitchen: { type: Boolean, default: false },
  dishes: [{ type: mongoose.Schema.Types.ObjectId, ref: "Dish" }],
  trialEndDate: {
    type: Date,
    default: function () {
      // Set trial end date to 15 days from now by default
      const date = new Date();
      date.setDate(date.getDate() + 15);
      return date;
    },
  },
  isTrialEnded: {
    type: Boolean,
    default: false,
  },
  tables: [
    {
      _id: { type: mongoose.Schema.Types.ObjectId, auto: true },
      name: { type: String, required: true },
      capacity: { type: Number, required: true },
      isAvailable: { type: Boolean, default: true },
      status: {
        type: String,
        enum: ["available", "occupied", "reserved", "cleaning", "maintenance"],
        default: "available"
      },
      currentOrder: {
        type: mongoose.Schema.Types.ObjectId,
        ref: "Order",
        default: null
      },
      qrCode: { type: String }, // Generated QR code URL
      location: { type: String }, // Table location description (e.g., "Near window", "Corner table")
      shape: {
        type: String,
        enum: ["round", "square", "rectangular", "oval"],
        default: "square"
      },
      features: [{ type: String }], // e.g., ["window_view", "power_outlet", "wheelchair_accessible"]
      lastOccupied: { type: Date },
      lastCleaned: { type: Date },
      reservedBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: "User",
        default: null
      },
      reservedUntil: { type: Date },
      notes: { type: String }, // Staff notes about the table
      createdAt: { type: Date, default: Date.now },
      updatedAt: { type: Date, default: Date.now },
    },
  ],
});

export default mongoose.model("Outlet", outletSchema);
