import mongoose from "mongoose";

const customerFeedbackSchema = new mongoose.Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "User",
    required: true,
  },
  orderId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Order",
  },
  outletId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Outlet",
  },
  foodChainId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "FoodChain",
  },
  rating: {
    type: Number,
    required: true,
    min: 1,
    max: 5,
  },
  comment: {
    type: String,
    maxlength: 1000,
  },
  categories: [{
    type: String,
    enum: [
      "food_quality",
      "service", 
      "delivery_time",
      "packaging",
      "value_for_money",
      "overall_experience"
    ],
  }],
  feedbackType: {
    type: String,
    enum: ["order_experience", "general", "complaint", "suggestion"],
    default: "order_experience",
  },
  context: {
    page: String,
    outletName: String,
    orderValue: Number,
    deliveryMethod: String,
  },
  status: {
    type: String,
    enum: ["pending", "reviewed", "responded"],
    default: "pending",
  },
  adminResponse: {
    message: String,
    respondedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
    },
    respondedAt: Date,
  },
  isPublic: {
    type: Boolean,
    default: false,
  },
  helpfulVotes: {
    type: Number,
    default: 0,
  },
  createdAt: {
    type: Date,
    default: Date.now,
  },
  updatedAt: {
    type: Date,
    default: Date.now,
  },
});

// Index for efficient queries
customerFeedbackSchema.index({ userId: 1, createdAt: -1 });
customerFeedbackSchema.index({ outletId: 1, rating: -1 });
customerFeedbackSchema.index({ foodChainId: 1, createdAt: -1 });
customerFeedbackSchema.index({ orderId: 1 });

// Update the updatedAt field before saving
customerFeedbackSchema.pre("save", function (next) {
  this.updatedAt = new Date();
  next();
});

// Virtual for average rating calculation
customerFeedbackSchema.statics.getAverageRating = async function(outletId) {
  const result = await this.aggregate([
    { $match: { outletId: new mongoose.Types.ObjectId(outletId) } },
    {
      $group: {
        _id: null,
        averageRating: { $avg: "$rating" },
        totalFeedbacks: { $sum: 1 },
        ratingDistribution: {
          $push: "$rating"
        }
      }
    }
  ]);
  
  return result[0] || { averageRating: 0, totalFeedbacks: 0, ratingDistribution: [] };
};

// Method to get feedback summary by categories
customerFeedbackSchema.statics.getCategorySummary = async function(outletId, startDate, endDate) {
  const matchStage = { outletId: new mongoose.Types.ObjectId(outletId) };
  
  if (startDate && endDate) {
    matchStage.createdAt = { $gte: startDate, $lte: endDate };
  }
  
  const result = await this.aggregate([
    { $match: matchStage },
    { $unwind: "$categories" },
    {
      $group: {
        _id: "$categories",
        count: { $sum: 1 },
        averageRating: { $avg: "$rating" }
      }
    },
    { $sort: { count: -1 } }
  ]);
  
  return result;
};

const CustomerFeedback = mongoose.model("CustomerFeedback", customerFeedbackSchema);

export default CustomerFeedback;
