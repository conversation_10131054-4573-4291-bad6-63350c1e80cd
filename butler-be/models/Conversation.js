import mongoose from "mongoose";

const messageSchema = new mongoose.Schema({
  message: { type: String, required: true },
  sender: { type: String, enum: ["user", "butler"], required: true },
  time: { type: String, required: true },
  suggestedQuestions: [{ type: String }],
  // Store recommended dish IDs for butler messages
  recommendedDishIds: [
    {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Dish",
    },
  ],
  
  // Store AI response metadata for context
  aiMetadata: {
    keywords: [{ type: String }],
    detectedLanguage: { type: String },
    responseType: { type: String }, // 'recommendation', 'information', 'cart_operation', etc.
    tokenUsage: { type: Number },
    processingTime: { type: Number },
  },
});

const conversationSchema = new mongoose.Schema({
  userId: { type: mongoose.Schema.Types.ObjectId, ref: "User", required: true },
  outletId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Outlet",
    required: true,
  },
  conversationId: {
    type: String,
    unique: true,
    default: function () {
      return `${this.userId}_${this.outletId}`;
    },
  },
  foodChainId: { type: mongoose.Schema.Types.ObjectId, ref: "FoodChain" },
  messages: [messageSchema],
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now },
});

// Update the updatedAt timestamp before saving
conversationSchema.pre("save", function (next) {
  this.updatedAt = new Date();
  next();
});

// Create a compound index for userId and outletId
conversationSchema.index({ userId: 1, outletId: 1 });

export default mongoose.model("Conversation", conversationSchema);
