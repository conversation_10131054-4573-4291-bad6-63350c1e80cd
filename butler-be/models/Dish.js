import mongoose from "mongoose";

const outletPricingSchema = new mongoose.Schema({
  outletId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Outlet",
    required: true,
  },
  price: { type: Number, required: true },
  discountedPrice: { type: Number },
  isAvailable: { type: Boolean, default: true },
});

const timeAvailabilitySchema = new mongoose.Schema({
  breakfast: { type: Boolean, default: true },
  lunch: { type: Boolean, default: true },
  dinner: { type: Boolean, default: true },
  customHours: { type: Boolean, default: false },
  startTime: { type: String },
  endTime: { type: String },
  // Days of week availability (0 = Sunday, 6 = Saturday)
  daysOfWeek: {
    type: [Number],
    default: [0, 1, 2, 3, 4, 5, 6],
  },
});

const ingredientSchema = new mongoose.Schema({
  inventoryItemId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "InventoryItem",
  },
  name: { type: String, required: true },
  quantity: { type: Number, required: true },
  unit: { type: String, required: true },
  isOptional: { type: Boolean, default: false },
  canBeExcluded: { type: Boolean, default: false },
});

const dishVariantSchema = new mongoose.Schema({
  name: { type: String, required: true },
  description: { type: String },
  priceAdjustment: { type: Number, default: 0 },
  isDefault: { type: Boolean, default: false },
});

const dishSchema = new mongoose.Schema({
  name: { type: String, required: true },
  description: { type: String },
  price: { type: Number, required: true },
  image: { type: String },
  category: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Category",
    required: true,
  },
  foodChain: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "FoodChain",
    required: true,
  },
  outlets: [
    {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Outlet",
    },
  ],
  outletPricing: [outletPricingSchema],
  isAvailable: { type: Boolean, default: true },
  isVeg: { type: Boolean, default: true },
  isSeasonal: { type: Boolean, default: false },
  isSpecial: { type: Boolean, default: false },
  isFeatured: { type: Boolean, default: false },
  isCombo: { type: Boolean, default: false },
  comboItems: [
    {
      dishId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: "Dish",
      },
      quantity: { type: Number, default: 1 },
      isRequired: { type: Boolean, default: true },
    },
  ],
  variants: [dishVariantSchema],
  ingredients: [ingredientSchema],
  // Enable specific add-ons for this dish (references AddOn model)
  enabledAddOns: [
    {
      type: mongoose.Schema.Types.ObjectId,
      ref: "AddOn",
    },
  ],
  nutritionalInfo: {
    calories: { type: Number },
    protein: { type: Number },
    carbs: { type: Number },
    fat: { type: Number },
    fiber: { type: Number },
  },
  allergens: [{ type: String }],
  preparationTime: { type: Number }, // in minutes
  spiceLevel: {
    type: String,
    enum: ["mild", "medium", "spicy", "extra_spicy"],
    default: "medium",
  },
  seasonalAvailability: {
    startDate: { type: Date },
    endDate: { type: Date },
  },
  timeAvailability: { type: timeAvailabilitySchema, default: () => ({}) },
  // Menu scheduling
  scheduleType: {
    type: String,
    enum: [
      "regular",
      "daily_special",
      "weekly_special",
      "monthly_special",
      "limited_time",
    ],
    default: "regular",
  },
  scheduleDates: [{ type: Date }], // Specific dates when this dish is available
  // Version control
  version: { type: Number, default: 1 },
  previousVersions: [
    {
      version: { type: Number },
      name: { type: String },
      description: { type: String },
      price: { type: Number },
      ingredients: [ingredientSchema],
      updatedAt: { type: Date },
      updatedBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: "User",
      },
    },
  ],
  cuisine: { type: String },
  tags: [{ type: String }],
  ratings: {
    average: { type: Number, default: 0 },
    count: { type: Number, default: 0 },
  },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "User",
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "User",
  },
});

export default mongoose.model("Dish", dishSchema);
