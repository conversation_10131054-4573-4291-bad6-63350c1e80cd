import mongoose from "mongoose";

const addOnSchema = new mongoose.Schema({
  name: { type: String, required: true },
  description: { type: String },
  price: { type: Number, required: true, min: 0 },
  type: { type: String }, // e.g., "extra_cheese", "sauce", etc.
  foodChain: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "FoodChain",
    required: true,
    index: true,
  },
  // Link to inventory item for automatic quantity management
  inventoryItem: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "InventoryItem",
    default: null,
  },
  // Quantity of inventory item consumed per add-on unit
  inventoryQuantityPerUnit: {
    type: Number,
    default: 1,
    min: 0,
  },
  // Whether this add-on should automatically reduce inventory
  trackInventory: {
    type: Boolean,
    default: false,
  },
  isActive: { type: Boolean, default: true },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now },
});

addOnSchema.pre("save", function (next) {
  this.updatedAt = new Date();
  next();
});

// Method to check if inventory is available for this add-on
addOnSchema.methods.checkInventoryAvailability = async function(quantity = 1) {
  if (!this.trackInventory || !this.inventoryItem) {
    return { available: true, message: "No inventory tracking" };
  }

  const InventoryItem = mongoose.model("InventoryItem");
  const inventoryItem = await InventoryItem.findById(this.inventoryItem);

  if (!inventoryItem) {
    return { available: false, message: "Linked inventory item not found" };
  }

  const requiredQuantity = quantity * this.inventoryQuantityPerUnit;
  if (inventoryItem.quantity < requiredQuantity) {
    return {
      available: false,
      message: `Insufficient inventory. Required: ${requiredQuantity}, Available: ${inventoryItem.quantity}`
    };
  }

  return { available: true, message: "Inventory available" };
};

// Method to reduce inventory when add-on is used
addOnSchema.methods.reduceInventory = async function(quantity = 1, orderId = null, userId = null) {
  if (!this.trackInventory || !this.inventoryItem) {
    return { success: true, message: "No inventory tracking" };
  }

  const InventoryItem = mongoose.model("InventoryItem");
  const InventoryTransaction = mongoose.model("InventoryTransaction");

  const inventoryItem = await InventoryItem.findById(this.inventoryItem);

  if (!inventoryItem) {
    return { success: false, message: "Linked inventory item not found" };
  }

  const requiredQuantity = quantity * this.inventoryQuantityPerUnit;

  if (inventoryItem.quantity < requiredQuantity) {
    return {
      success: false,
      message: `Insufficient inventory. Required: ${requiredQuantity}, Available: ${inventoryItem.quantity}`
    };
  }

  // Update inventory quantity
  const previousQuantity = inventoryItem.quantity;
  inventoryItem.quantity -= requiredQuantity;
  inventoryItem.updatedAt = new Date();
  await inventoryItem.save();

  // Create transaction record
  if (userId) {
    const transaction = new InventoryTransaction({
      itemId: inventoryItem._id,
      type: "deduction",
      quantity: requiredQuantity,
      previousQuantity,
      newQuantity: inventoryItem.quantity,
      reason: `Used in add-on: ${this.name}`,
      orderId,
      userId,
      outletId: inventoryItem.outletId,
      foodChainId: inventoryItem.foodChainId,
    });
    await transaction.save();
  }

  return {
    success: true,
    message: `Reduced inventory by ${requiredQuantity} units`,
    newQuantity: inventoryItem.quantity
  };
};

export default mongoose.model("AddOn", addOnSchema);

