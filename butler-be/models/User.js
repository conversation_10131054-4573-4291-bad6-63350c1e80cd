import mongoose from "mongoose";

const userSchema = new mongoose.Schema({
  email: { type: String, unique: true, sparse: true },
  password: { type: String },
  name: { type: String, required: true },
  phone: { type: String, unique: true, sparse: true },
  address: { type: String },
  city: { type: String }, // User's preferred city for outlet recommendations
  foodChain: { type: mongoose.Schema.Types.ObjectId, ref: "FoodChain" },
  role: {
    type: String,
    enum: ["user", "admin", "super_admin"],
    default: "user",
  },
  status: {
    type: String,
    enum: ["active", "blocked"],
    default: "active",
  },
  // First-time login flag
  isFirstLogin: { type: Boolean, default: false },
  // User creation source
  createdBy: {
    type: String,
    enum: ["self", "admin", "system", "google"],
    default: "self",
  },
  // Google OAuth fields
  googleId: { type: String, unique: true, sparse: true },
  // Two-factor authentication fields
  twoFactorEnabled: { type: <PERSON>olean, default: false },
  twoFactorSecret: { type: String },
  twoFactorBackupCodes: [{ type: String }],
  // Password reset fields
  resetPasswordToken: { type: String },
  resetPasswordExpires: { type: Date },
  // Last login and security tracking
  lastLogin: { type: Date },
  lastPasswordChange: { type: Date },
  failedLoginAttempts: { type: Number, default: 0 },
  lockedUntil: { type: Date },
  // Permissions and preferences
  permissions: [{ type: String }],
  preferences: {
    darkMode: { type: Boolean, default: false },
    language: { type: String, default: "en" },
    emailNotifications: { type: Boolean, default: true },
    pushNotifications: { type: Boolean, default: true },
  },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now },
  aiMessage: { type: String },
  tasteBuds: { type: String },
});

export default mongoose.model("User", userSchema);
