import CartOperation from "../models/CartOperation.js";
import Dish from "../models/Dish.js";
import { emitCartUpdate, emitCartOperation } from "../sockets/cartSocket.js";
import Cart from "../models/Cart.js";
import { validateAndApplyOffersToCart } from "./offer-validation-service.js";

/**
 * Detect cart operation intent from user message
 * @param {string} message - User message
 * @param {string} userId - User ID (optional, for context resolution)
 * @param {string} conversationId - Conversation ID (optional, for context resolution)
 * @returns {Object|Array|null} - Detected operation(s) or null if no operation detected
 */
export const detectCartOperation = async (
  message,
  userId = null,
  conversationId = null
) => {
  // Convert message to lowercase for case-insensitive matching
  const lowerMessage = message.toLowerCase();

  // Check for multiple operations in a single message
  const multipleOperations = await detectMultipleOperations(
    lowerMessage,
    userId,
    conversationId
  );
  if (multipleOperations.length > 0) {
    return multipleOperations.length === 1
      ? multipleOperations[0]
      : multipleOperations;
  }

  // No cart operation detected (handled by detectMultipleOperations)
  return null;
};

/**
 * Detect multiple operations in a single message
 * @param {string} lowerMessage - Lowercase user message
 * @param {string} userId - User ID (optional, for context resolution)
 * @param {string} conversationId - Conversation ID (optional, for context resolution)
 * @returns {Array} - Array of detected operations
 */
const detectMultipleOperations = async (
  lowerMessage,
  userId = null,
  conversationId = null
) => {
  const operations = [];

  // Resolve context references if we have user context
  let resolvedMessage = lowerMessage;
  if (userId && conversationId) {
    try {
      const originalMessage = lowerMessage;
      resolvedMessage = await resolveContextReferences(
        originalMessage,
        userId,
        conversationId
      );
      if (resolvedMessage !== originalMessage) {
        console.log(
          `🔄 Context resolved: "${originalMessage}" -> "${resolvedMessage}"`
        );
        lowerMessage = resolvedMessage.toLowerCase();
      }
    } catch (error) {
      console.error("Error resolving context:", error);
    }
  }

  // Enhanced patterns for compound operations with quantity support
  const compoundPatterns = [
    // "add X and remove Y" patterns
    /add\s+(.*?)\s+and\s+remove\s+(.*?)$/i,
    /add\s+(.*?)\s+but\s+remove\s+(.*?)$/i,
    /add\s+(.*?)\s+then\s+remove\s+(.*?)$/i,

    // "remove X and add Y" patterns
    /remove\s+(.*?)\s+and\s+add\s+(.*?)$/i,
    /remove\s+(.*?)\s+but\s+add\s+(.*?)$/i,
    /remove\s+(.*?)\s+then\s+add\s+(.*?)$/i,

    // Multiple add patterns with enhanced quantity parsing
    /add\s+(.*?)\s+and\s+(.*?)$/i,
    /add\s+(.*?)\s+also\s+(.*?)$/i,
    /add\s+(.*?)\s+plus\s+(.*?)$/i,
  ];

  // Helper function to parse quantity and item from a string
  const parseQuantityAndItem = (itemString) => {
    const trimmed = itemString.trim();

    // Check for quantity at the beginning: "3 chicken wraps", "2 veg burgers"
    const quantityMatch = trimmed.match(/^(\d+)\s+(.+)$/);
    if (quantityMatch) {
      return {
        quantity: parseInt(quantityMatch[1], 10),
        item: quantityMatch[2].trim(),
      };
    }

    // Check for quantity words: "three chicken wraps", "two veg burgers"
    const wordQuantityMap = {
      one: 1,
      two: 2,
      three: 3,
      four: 4,
      five: 5,
      six: 6,
      seven: 7,
      eight: 8,
      nine: 9,
      ten: 10,
    };

    for (const [word, num] of Object.entries(wordQuantityMap)) {
      if (trimmed.toLowerCase().startsWith(word + " ")) {
        return {
          quantity: num,
          item: trimmed.substring(word.length + 1).trim(),
        };
      }
    }

    // Default to quantity 1 if no quantity found
    return {
      quantity: 1,
      item: trimmed,
    };
  };

  // Check for compound patterns
  for (const pattern of compoundPatterns) {
    const match = lowerMessage.match(pattern);
    if (match) {
      const patternStr = pattern.source;

      if (patternStr.includes("add") && patternStr.includes("remove")) {
        if (patternStr.indexOf("add") < patternStr.indexOf("remove")) {
          // "add X and remove Y" pattern
          const addParsed = parseQuantityAndItem(match[1]);
          const removeParsed = parseQuantityAndItem(match[2]);

          operations.push({
            operation: "add",
            item: addParsed.item,
            quantity: addParsed.quantity,
          });
          operations.push({
            operation: "remove",
            item: removeParsed.item,
            quantity: removeParsed.quantity,
          });
        } else {
          // "remove X and add Y" pattern
          const removeParsed = parseQuantityAndItem(match[1]);
          const addParsed = parseQuantityAndItem(match[2]);

          operations.push({
            operation: "remove",
            item: removeParsed.item,
            quantity: removeParsed.quantity,
          });
          operations.push({
            operation: "add",
            item: addParsed.item,
            quantity: addParsed.quantity,
          });
        }
      } else if (
        patternStr.includes("add") &&
        (patternStr.includes("and") ||
          patternStr.includes("also") ||
          patternStr.includes("plus"))
      ) {
        // Multiple add patterns with quantity parsing
        const firstParsed = parseQuantityAndItem(match[1]);
        const secondParsed = parseQuantityAndItem(match[2]);

        operations.push({
          operation: "add",
          item: firstParsed.item,
          quantity: firstParsed.quantity,
        });
        operations.push({
          operation: "add",
          item: secondParsed.item,
          quantity: secondParsed.quantity,
        });
      }
      break;
    }
  }

  // If no compound operations found, try to detect single operation
  if (operations.length === 0) {
    const singleOp = detectSingleOperation(lowerMessage);
    if (singleOp) {
      operations.push(singleOp);
    }
  }

  return operations;
};

/**
 * Resolve pronouns and context references to actual dish names
 */
const resolveContextReferences = async (message, userId, conversationId) => {
  const lowerMessage = message.toLowerCase();

  // Check for pronouns that need context resolution
  const pronounPatterns = [
    /add\s+(it|this|that)\s+to\s+(?:my\s+)?cart/i,
    /remove\s+(it|this|that)\s+from\s+(?:my\s+)?cart/i,
    /order\s+(it|this|that)/i,
    /i\s+want\s+(it|this|that)/i,
    /give\s+me\s+(it|this|that)/i,
    /(it|this|that)\s+please/i,
    /yes,?\s+(add\s+)?(it|this|that)/i,
    /sure,?\s+(add\s+)?(it|this|that)/i,
    /okay,?\s+(add\s+)?(it|this|that)/i,
    /^(yes|sure|okay|ok)\s*$/i, // Simple affirmations
  ];

  let hasPronoun = false;
  for (const pattern of pronounPatterns) {
    if (pattern.test(lowerMessage)) {
      hasPronoun = true;
      break;
    }
  }

  if (!hasPronoun) {
    return message; // No pronouns to resolve
  }

  try {
    // Strategy 1: Check recent conversation messages for AI recommendations
    const Conversation = (await import("../models/Conversation.js")).default;
    const conversation = await Conversation.findOne({
      userId,
      outletId: conversationId, // Note: conversationId might actually be outletId in some contexts
    })
      .populate({
        path: "messages.recommendedDishIds",
        select: "name",
      })
      .sort({ updatedAt: -1 })
      .limit(1);

    if (conversation && conversation.messages && conversation.messages.length > 0) {
      // Get the last few butler messages with recommendations
      const recentButlerMessages = conversation.messages
        .filter((msg) => msg.sender === "butler" && msg.recommendedDishIds && msg.recommendedDishIds.length > 0)
        .slice(-3); // Last 3 butler messages with recommendations

      if (recentButlerMessages.length > 0) {
        // Get the most recent recommendation
        const lastRecommendation = recentButlerMessages[recentButlerMessages.length - 1];
        const recommendedDishes = lastRecommendation.recommendedDishIds;

        if (recommendedDishes && recommendedDishes.length > 0) {
          // Use the first recommended dish (most relevant)
          const dishName = recommendedDishes[0].name;
          console.log(`🔄 Resolving pronoun from AI recommendation to: ${dishName}`);

          // Replace pronouns with the actual dish name
          return message.replace(/(it|this|that)/gi, dishName);
        }
      }
    }

    // Strategy 2: Check recent cart operations as fallback
    const recentOperations = await CartOperation.find({
      userId,
      conversationId,
      status: "completed",
      operation: "add",
      createdAt: { $gte: new Date(Date.now() - 300000) }, // Last 5 minutes
    })
      .sort({ createdAt: -1 })
      .limit(1)
      .populate("dishId", "name");

    if (recentOperations.length > 0) {
      const lastDish = recentOperations[0].dishId;
      if (lastDish && lastDish.name) {
        console.log(`🔄 Resolving pronoun from cart operation to: ${lastDish.name}`);
        // Replace pronouns with the actual dish name
        return message.replace(/(it|this|that)/gi, lastDish.name);
      }
    }

    // If no context found, log warning
    console.log("⚠️ Could not resolve pronoun - no recent context found");
    return message;
  } catch (error) {
    console.error("Error resolving context references:", error);
    return message;
  }
};

/**
 * Detect single operation (extracted from main function for reuse)
 */
const detectSingleOperation = (lowerMessage) => {
  // Enhanced patterns to handle "more" context
  const addPatterns = [
    // Handle "more" patterns first - these need special processing
    /add\s+(\d+)\s+more\s+(.*?)(?:\s+to\s+(?:my\s+)?cart)?$/i,
    /(\d+)\s+more\s+(.*?)(?:\s+please)?$/i,
    /add\s+more\s+(.*?)(?:\s+to\s+(?:my\s+)?cart)?$/i,
    /more\s+(.*?)(?:\s+please)?$/i,

    // Standard patterns
    /add\s+(\d+)\s+(.*?)\s+to\s+(?:my\s+)?cart/i,
    /add\s+(.*?)\s+to\s+(?:my\s+)?cart/i,
    /put\s+(.*?)\s+in\s+(?:my\s+)?cart/i,
    /i\s+want\s+to\s+order\s+(.*)/i,
    /i\s+would\s+like\s+to\s+order\s+(.*)/i,
    /i\s+want\s+(.*)/i,
    /i\s+would\s+like\s+(.*)/i,
    /can\s+i\s+get\s+(.*)/i,
    /can\s+i\s+have\s+(.*)/i,
    /please\s+add\s+(.*?)\s+to\s+(?:my\s+)?cart/i,
    /give\s+me\s+(.*)/i,
    /i'll\s+take\s+(.*)/i,
    /i\s+will\s+take\s+(.*)/i,
    /i\s+need\s+(.*)/i,
    /add\s+a\s+(.*)/i,
    /add\s+an\s+(.*)/i,
    /add\s+one\s+(.*)/i,
    /add\s+(\d+)\s+(.*)/i,
    /add\s+(.*)/i, // Simple "add [item]" pattern
  ];

  const removePatterns = [
    /remove\s+(.*?)\s+from\s+(?:my\s+)?cart/i,
    /delete\s+(.*?)\s+from\s+(?:my\s+)?cart/i,
    /take\s+(.*?)\s+out\s+of\s+(?:my\s+)?cart/i,
    /i\s+don't\s+want\s+(.*?)\s+(?:anymore|any more)/i,
    /cancel\s+(.*?)\s+from\s+(?:my\s+)?order/i,
    /please\s+remove\s+(.*?)\s+from\s+(?:my\s+)?cart/i,
    /remove\s+the\s+(.*)/i,
    /delete\s+the\s+(.*)/i,
    /cancel\s+the\s+(.*)/i,
    /take\s+out\s+the\s+(.*)/i,
    /i\s+changed\s+my\s+mind\s+about\s+the\s+(.*)/i,
    /no\s+more\s+(.*)/i,
  ];

  const clearPatterns = [
    /clear\s+(?:my\s+)?cart/i,
    /empty\s+(?:my\s+)?cart/i,
    /remove\s+everything\s+from\s+(?:my\s+)?cart/i,
    /start\s+over\s+(?:with\s+)?(?:my\s+)?cart/i,
    /reset\s+(?:my\s+)?cart/i,
    /cancel\s+(?:my\s+)?order/i,
    /cancel\s+(?:my\s+)?cart/i,
    /delete\s+(?:my\s+)?cart/i,
    /delete\s+everything/i,
    /remove\s+all\s+items/i,
    /i\s+want\s+to\s+start\s+over/i,
    /i\s+changed\s+my\s+mind\s+about\s+everything/i,
  ];

  // Order placement patterns
  const orderPatterns = [
    /place\s+(?:my\s+)?order/i,
    /place\s+(?:the\s+)?order/i,
    /order\s+now/i,
    /order\s+it/i, // Added for "order it"
    /order\s+this/i, // Added for "order this"
    /order\s+that/i, // Added for "order that"
    /checkout/i,
    /proceed\s+to\s+checkout/i,
    /confirm\s+(?:my\s+)?order/i,
    /finalize\s+(?:my\s+)?order/i,
    /complete\s+(?:my\s+)?order/i,
    /submit\s+(?:my\s+)?order/i,
    /i\s+want\s+to\s+order/i,
    /ready\s+to\s+order/i,
    /let\s+me\s+order/i,
    /i\s+would\s+like\s+to\s+order/i,
    /can\s+i\s+place\s+(?:my\s+)?order/i,
    /can\s+you\s+place\s+(?:my\s+)?order/i,
    /please\s+place\s+(?:my\s+)?order/i,
    /go\s+ahead\s+and\s+order/i,
    /that\s*'?s\s+all\s*,?\s*place\s+(?:my\s+)?order/i,
    /that\s*'?s\s+it\s*,?\s*order\s+now/i,
    /done\s*,?\s*place\s+order/i,
    /finished\s*,?\s*checkout/i,
    // Additional natural patterns
    /place\s+order/i,
    /order\s+it/i,
    /order\s+this/i,
    /order\s+these/i,
    /order\s+everything/i,
    /order\s+all/i,
    /make\s+(?:my\s+)?order/i,
    /send\s+(?:my\s+)?order/i,
    /book\s+(?:my\s+)?order/i,
    /confirm\s+order/i,
    /yes\s*,?\s*order/i,
    /yes\s*,?\s*place\s+order/i,
    /order\s+kar\s+do/i, // Hindi: "place the order"
    /order\s+karo/i, // Hindi: "place order"
    /order\s+de\s+do/i, // Hindi: "give the order"
  ];

  // Check for order placement intent first (higher priority)
  for (const pattern of orderPatterns) {
    if (pattern.test(lowerMessage)) {
      console.log(
        "🛒 Order pattern matched:",
        pattern.source,
        "for message:",
        lowerMessage
      );
      return {
        operation: "order",
        item: null,
        quantity: 0,
      };
    }
  }

  // Check for clear cart intent
  for (const pattern of clearPatterns) {
    if (pattern.test(lowerMessage)) {
      return {
        operation: "clear",
        item: null,
        quantity: 0,
      };
    }
  }

  // Check for add intent with special handling for "more" patterns
  for (let i = 0; i < addPatterns.length; i++) {
    const pattern = addPatterns[i];
    const match = lowerMessage.match(pattern);
    if (match) {
      // Handle "more" patterns (first 4 patterns)
      if (i < 4) {
        if (i === 0 || i === 1) {
          // Patterns with quantity: "add 2 more wraps" or "2 more wraps"
          return {
            operation: "add",
            item: match[2].trim(),
            quantity: parseInt(match[1], 10),
            isMore: true, // Flag to indicate this is adding more of existing item
          };
        } else {
          // Patterns without explicit quantity: "add more wraps" or "more wraps"
          return {
            operation: "add",
            item: match[1].trim(),
            quantity: 1,
            isMore: true,
          };
        }
      }

      // Handle standard patterns
      if (match.length > 1) {
        const firstGroup = match[1].trim();
        if (/^\d+$/.test(firstGroup)) {
          return {
            operation: "add",
            item: match[2].trim(),
            quantity: parseInt(firstGroup, 10),
          };
        } else {
          return {
            operation: "add",
            item: firstGroup,
            quantity: 1,
          };
        }
      }
    }
  }

  // Check for remove intent
  for (const pattern of removePatterns) {
    const match = lowerMessage.match(pattern);
    if (match && match.length > 1) {
      return {
        operation: "remove",
        item: match[1].trim(),
        quantity: 1,
      };
    }
  }

  return null;
};

/**
 * Find dish by name or description
 * @param {string} itemName - Item name to search for
 * @param {string} foodChainId - Food chain ID
 * @param {string} outletId - Outlet ID
 * @returns {Promise<Object|null>} - Found dish or null
 */
export const findDishByName = async (itemName, foodChainId, outletId) => {
  try {
    // Clean up the item name - remove common words that might interfere with search
    const cleanedItemName = itemName
      .replace(
        /a |an |the |some |few |one |two |three |four |five |six |seven |eight |nine |ten /gi,
        ""
      )
      .replace(
        /please|thank you|thanks|kindly|would like|want|need|get|have/gi,
        ""
      )
      .trim();

    if (!cleanedItemName) {
      return null;
    }

    // Check for vegetarian preference in the item name
    const isVegRequest = /\b(veg|vegetarian|veggie)\b/i.test(itemName);
    const isNonVegRequest =
      /\b(non-veg|non vegetarian|chicken|mutton|fish|meat|egg)\b/i.test(
        itemName
      );

    // Create a regex for case-insensitive partial matching
    const searchRegex = new RegExp(cleanedItemName, "i");

    // Search for dishes that match the name or description
    let dishes = await Dish.find({
      foodChain: foodChainId,
      outlets: outletId,
      isAvailable: true,
      $or: [
        { name: searchRegex },
        { description: searchRegex },
        { tags: searchRegex },
        { cuisine: searchRegex },
      ],
    }).populate("category", "name");

    // Apply dietary preference filtering
    if (isVegRequest && !isNonVegRequest) {
      dishes = dishes.filter((dish) => dish.isVeg === true);
    } else if (isNonVegRequest && !isVegRequest) {
      dishes = dishes.filter((dish) => dish.isVeg === false);
    }

    if (dishes.length === 0) {
      // Try a more flexible search by splitting the item name into words
      // and searching for dishes that match any of the words
      const words = cleanedItemName
        .split(/\s+/)
        .filter((word) => word.length > 2);

      if (words.length > 0) {
        const wordQueries = words.map((word) => ({
          $or: [
            { name: new RegExp(word, "i") },
            { description: new RegExp(word, "i") },
            { tags: new RegExp(word, "i") },
            { cuisine: new RegExp(word, "i") },
          ],
        }));

        let moreFlexibleDishes = await Dish.find({
          foodChain: foodChainId,
          outlets: outletId,
          isAvailable: true,
          $or: wordQueries,
        }).populate("category", "name");

        // Apply dietary preference filtering to flexible search too
        if (isVegRequest && !isNonVegRequest) {
          moreFlexibleDishes = moreFlexibleDishes.filter(
            (dish) => dish.isVeg === true
          );
        } else if (isNonVegRequest && !isVegRequest) {
          moreFlexibleDishes = moreFlexibleDishes.filter(
            (dish) => dish.isVeg === false
          );
        }

        if (moreFlexibleDishes.length > 0) {
          // Sort by relevance - dishes that match more words are more relevant
          const scoredDishes = moreFlexibleDishes.map((dish) => {
            const dishText = `${dish.name} ${dish.description || ""} ${
              dish.category?.name || ""
            } ${dish.tags?.join(" ") || ""} ${
              dish.cuisine || ""
            }`.toLowerCase();
            let score = 0;

            words.forEach((word) => {
              if (dishText.includes(word.toLowerCase())) {
                score++;
              }
            });

            // Boost score for dietary preference match
            if (isVegRequest && dish.isVeg) {
              score += 2;
            } else if (isNonVegRequest && !dish.isVeg) {
              score += 2;
            }

            return { dish, score };
          });

          // Sort by score (highest first)
          scoredDishes.sort((a, b) => b.score - a.score);

          return scoredDishes[0].dish;
        }
      }

      return null;
    }

    // If multiple dishes found, prioritize based on dietary preference and exact name matches
    let bestMatch = null;
    let bestScore = 0;

    dishes.forEach((dish) => {
      let score = 0;

      // Exact name match gets highest priority
      if (dish.name.toLowerCase().includes(cleanedItemName.toLowerCase())) {
        score += 10;
      }

      // Dietary preference match
      if (isVegRequest && dish.isVeg) {
        score += 5;
      } else if (isNonVegRequest && !dish.isVeg) {
        score += 5;
      }

      // Partial name match
      if (dish.name.toLowerCase().includes(cleanedItemName.toLowerCase())) {
        score += 3;
      }

      // Description match
      if (
        dish.description &&
        dish.description.toLowerCase().includes(cleanedItemName.toLowerCase())
      ) {
        score += 2;
      }

      // Tags match
      if (
        dish.tags &&
        dish.tags.some((tag) =>
          tag.toLowerCase().includes(cleanedItemName.toLowerCase())
        )
      ) {
        score += 1;
      }

      if (score > bestScore) {
        bestScore = score;
        bestMatch = dish;
      }
    });

    return bestMatch || dishes[0];
  } catch (error) {
    console.error("Error finding dish by name:", error);
    throw error;
  }
};

/**
 * Record a cart operation
 * @param {Object} operationData - Operation data
 * @returns {Promise<Object>} - Created operation
 */
export const recordCartOperation = async (operationData) => {
  try {
    const cartOperation = new CartOperation(operationData);
    await cartOperation.save();
    return cartOperation;
  } catch (error) {
    console.error("Error recording cart operation:", error);
    throw error;
  }
};

/**
 * Process a cart operation
 * @param {Object} operation - Operation details
 * @param {string} userId - User ID
 * @param {string} conversationId - Conversation ID
 * @param {string} foodChainId - Food chain ID
 * @param {string} outletId - Outlet ID
 * @returns {Promise<Object>} - Result of the operation
 */
export const processCartOperation = async (
  operation,
  userId,
  conversationId,
  foodChainId,
  outletId
) => {
  try {
    if (!operation) {
      return null;
    }

    let result = {
      success: false,
      message: "",
      operation: operation.operation,
      dish: null,
    };

    // Handle clear cart operation
    if (operation.operation === "clear") {
      // Get user's cart and clear it
      let cart = await Cart.findActiveCart(userId, foodChainId, outletId);
      if (cart) {
        await cart.clearCart();
      }

      await recordCartOperation({
        userId,
        conversationId,
        operation: "clear",
        status: "completed",
      });

      result.success = true;
      result.message =
        "I've cleared your cart. Would you like to start a new order?";

      // Emit real-time cart update for clear operation
      try {
        // Small delay to ensure database operations are completed
        setTimeout(async () => {
          await emitCartUpdate(userId, foodChainId, outletId, "clear");
          await emitCartOperation(userId, foodChainId, outletId, result);
        }, 100);
      } catch (emitError) {
        console.error("Error emitting cart clear updates:", emitError);
      }

      return result;
    }

    // Find the dish by name for add/remove operations
    const dish = await findDishByName(operation.item, foodChainId, outletId);

    if (!dish) {
      await recordCartOperation({
        userId,
        conversationId,
        operation: operation.operation,
        status: "failed",
        metadata: { itemName: operation.item, reason: "Dish not found" },
      });

      // Provide a more helpful message
      if (operation.operation === "add") {
        result.message = `I couldn't find "${operation.item}" in our menu. Could you please try a different dish or check our menu for available options?`;
      } else {
        result.message = `I couldn't find "${operation.item}" in your cart or our menu. Please check the name and try again.`;
      }
      return result;
    }

    // Get or create user's cart
    let cart = await Cart.findActiveCart(userId, foodChainId, outletId);
    if (!cart) {
      cart = await Cart.createOrUpdateCart(userId, foodChainId, outletId);
    }

    // Actually perform the cart operation
    if (operation.operation === "add") {
      // Add item to cart
      await cart.addItem(
        {
          ...dish.toObject(),
          categoryName: dish.category?.name,
        },
        operation.quantity || 1
      );

      // Apply offers
      await validateAndApplyOffersToCart(cart);

      result.message = `I've added "${dish.name}" to your cart. Would you like anything else?`;
    } else if (operation.operation === "remove") {
      // Check if item is in cart
      const itemExists = cart.items.some(
        (item) => item.dishId.toString() === dish._id.toString()
      );

      if (itemExists) {
        // Remove item from cart
        await cart.removeItem(dish._id);

        // Re-apply offers
        await validateAndApplyOffersToCart(cart);

        result.message = `I've removed "${dish.name}" from your cart. Is there anything else you'd like?`;
      } else {
        result.message = `I couldn't find "${dish.name}" in your cart. Is there something else you'd like to order?`;
      }
    }

    // Record the operation
    await recordCartOperation({
      userId,
      conversationId,
      operation: operation.operation,
      dishId: dish._id,
      quantity: operation.quantity,
      status: "completed",
      metadata: { dishName: dish.name },
    });

    result.success = true;
    result.dish = dish;
    result.operation = operation.operation;
    result.quantity = operation.quantity;

    // Emit real-time cart update after successful operation
    try {
      // Small delay to ensure database operations are completed
      setTimeout(async () => {
        await emitCartUpdate(
          userId,
          foodChainId,
          outletId,
          operation.operation
        );
        await emitCartOperation(userId, foodChainId, outletId, result);
      }, 100);
    } catch (emitError) {
      console.error("Error emitting cart updates:", emitError);
      // Don't fail the operation if socket emission fails
    }

    return result;
  } catch (error) {
    console.error("Error processing cart operation:", error);
    throw error;
  }
};

/**
 * Smart cart operation processor with duplicate prevention and context awareness
 * @param {Object} operation - Detected cart operation
 * @param {string} userId - User ID
 * @param {string} conversationId - Conversation ID
 * @param {string} foodChainId - Food chain ID
 * @param {string} outletId - Outlet ID
 * @param {Object} context - Additional context for smart processing
 * @returns {Object} - Operation result with enhanced feedback
 */
export const processSmartCartOperation = async (
  operation,
  userId,
  conversationId,
  foodChainId,
  outletId,
  context = {}
) => {
  try {
    console.log("🧠 Processing smart cart operation:", operation);

    // Get current cart state for context-aware processing
    let cart = await Cart.findActiveCart(userId, foodChainId, outletId);
    if (!cart) {
      cart = await Cart.createOrUpdateCart(userId, foodChainId, outletId);
    }

    let result = {
      success: false,
      message: "",
      operation: operation.operation,
      dish: null,
      quantity: operation.quantity || 1,
    };

    // Handle order placement operation
    if (operation.operation === "order") {
      return await processOrderPlacement(
        cart,
        userId,
        conversationId,
        foodChainId,
        outletId
      );
    }

    // Handle clear cart operation
    if (operation.operation === "clear") {
      return await processClearOperation(
        cart,
        userId,
        conversationId,
        foodChainId,
        outletId
      );
    }

    // Handle "more" operations with context awareness
    if (operation.isMore) {
      return await processMoreOperation(
        operation,
        cart,
        userId,
        conversationId,
        foodChainId,
        outletId,
        context
      );
    }

    // Find the dish by name for add/remove operations
    const dish = await findDishByName(operation.item, foodChainId, outletId);

    if (!dish) {
      await recordCartOperation({
        userId,
        conversationId,
        operation: operation.operation,
        status: "failed",
        metadata: {
          itemName: operation.item,
          reason: "Dish not found",
          context,
        },
      });

      return {
        success: false,
        message: `I couldn't find "${operation.item}" in our menu. Could you try selecting from the recommendations below?`,
        operation: operation.operation,
        dish: null,
        quantity: operation.quantity || 1,
        fallbackAction: "show_recommendations",
        context: { searchTerm: operation.item },
      };
    }

    // Check for duplicate operations (prevent adding same item multiple times in quick succession)
    const recentOperations = await CartOperation.find({
      userId,
      conversationId,
      dishId: dish._id,
      operation: operation.operation,
      status: "completed",
      createdAt: { $gte: new Date(Date.now() - 5000) }, // Reduced to 5 seconds
    })
      .sort({ createdAt: -1 })
      .limit(1);

    // If we have a recent identical operation, return the existing state without processing again
    if (recentOperations.length > 0) {
      const existingItem = cart.items.find(
        (item) => item.dishId.toString() === dish._id.toString()
      );

      console.log("🔄 Duplicate operation detected, returning existing state");

      if (operation.operation === "add" && existingItem) {
        // For add operations, still emit socket update to ensure UI is synced
        setTimeout(async () => {
          await emitCartUpdate(userId, foodChainId, outletId, "duplicate-add");
        }, 50);

        return {
          success: true,
          message: `"${dish.name}" is already in your cart with quantity ${existingItem.quantity}.`,
          operation: operation.operation,
          dish: dish,
          quantity: existingItem.quantity,
          isDuplicate: true,
          skipProcessing: true,
        };
      }

      // For remove operations, also check if already processed
      if (operation.operation === "remove" && !existingItem) {
        return {
          success: true,
          message: `"${dish.name}" has already been removed from your cart.`,
          operation: operation.operation,
          dish: dish,
          quantity: 0,
          isDuplicate: true,
          skipProcessing: true,
        };
      }
    }

    // Process the operation based on type
    if (operation.operation === "add") {
      result = await processAddOperation(
        cart,
        dish,
        operation,
        result,
        userId,
        conversationId
      );
    } else if (operation.operation === "remove") {
      result = await processRemoveOperation(
        cart,
        dish,
        operation,
        result,
        userId,
        conversationId
      );
    } else if (operation.operation === "view") {
      result = await processViewOperation(cart, result);
    }

    // Add context information to result
    result.currentCartState = {
      itemCount: cart.items.length,
      totalItems: cart.items.reduce((sum, item) => sum + item.quantity, 0),
      subtotal: cart.subtotal,
    };

    // Emit updates if successful
    if (result.success) {
      try {
        setTimeout(async () => {
          await emitCartUpdate(
            userId,
            foodChainId,
            outletId,
            operation.operation
          );
          await emitCartOperation(userId, foodChainId, outletId, result);
        }, 100);
      } catch (emitError) {
        console.error("Error emitting cart updates:", emitError);
      }
    }

    return result;
  } catch (error) {
    console.error("❌ Error processing smart cart operation:", error);
    return {
      success: false,
      message:
        "I encountered an issue while processing your request. Please try again or add the item manually.",
      operation: operation.operation,
      dish: null,
      quantity: operation.quantity || 1,
      error: error.message,
      fallbackAction: "manual_add",
      context: { originalOperation: operation },
    };
  }
};

/**
 * Process "more" operation with context from recent cart operations
 */
const processMoreOperation = async (
  operation,
  cart,
  userId,
  conversationId,
  foodChainId,
  outletId,
  context
) => {
  try {
    console.log("🔄 Processing 'more' operation:", operation);

    // Get recent cart operations to understand what "more" refers to
    const recentOperations = await CartOperation.find({
      userId,
      status: "completed",
      operation: "add",
      createdAt: { $gte: new Date(Date.now() - 300000) }, // Last 5 minutes
    })
      .sort({ createdAt: -1 })
      .limit(5)
      .populate("dishId", "name");

    // Try to find what item "more" refers to
    let targetDish = null;

    // First, try to match by the item name in the operation
    if (operation.item) {
      targetDish = await findDishByName(operation.item, foodChainId, outletId);
    }

    // If no direct match, look at recent operations for context
    if (!targetDish && recentOperations.length > 0) {
      // Check if any recent operation matches the item name
      for (const recentOp of recentOperations) {
        if (recentOp.dishId && recentOp.dishId.name) {
          const dishName = recentOp.dishId.name.toLowerCase();
          const searchTerm = operation.item.toLowerCase();

          // Check if the search term is contained in the dish name or vice versa
          if (dishName.includes(searchTerm) || searchTerm.includes(dishName)) {
            targetDish = await Dish.findById(recentOp.dishId._id);
            break;
          }
        }
      }

      // If still no match, use the most recent operation
      if (!targetDish && recentOperations[0]?.dishId) {
        targetDish = await Dish.findById(recentOperations[0].dishId._id);
        console.log(
          `🎯 Using most recent item for "more": ${targetDish?.name}`
        );
      }
    }

    if (!targetDish) {
      return {
        success: false,
        message: `I couldn't find "${operation.item}" in our menu. Could you try selecting from the recommendations below?`,
        operation: operation.operation,
        dish: null,
        quantity: operation.quantity || 1,
        fallbackAction: "show_recommendations",
        context: { searchTerm: operation.item },
      };
    }

    // Process as a regular add operation
    const addOperation = {
      operation: "add",
      item: targetDish.name,
      quantity: operation.quantity || 1,
    };

    return await processAddOperation(
      cart,
      targetDish,
      addOperation,
      {
        success: false,
        message: "",
        operation: "add",
        dish: null,
        quantity: operation.quantity || 1,
      },
      userId,
      conversationId
    );
  } catch (error) {
    console.error("Error in processMoreOperation:", error);
    return {
      success: false,
      message:
        "I couldn't process your request. Could you please be more specific about what you'd like to add?",
      operation: operation.operation,
      dish: null,
      quantity: operation.quantity || 1,
      fallbackAction: "show_recommendations",
    };
  }
};

/**
 * Process add operation with smart quantity handling
 */
const processAddOperation = async (
  cart,
  dish,
  operation,
  result,
  userId,
  conversationId
) => {
  try {
    const existingItem = cart.items.find(
      (item) => item.dishId.toString() === dish._id.toString()
    );

    if (existingItem) {
      // Item already exists, increase quantity
      await cart.updateItemQuantity(
        dish._id,
        existingItem.quantity + (operation.quantity || 1)
      );
      result.message = `Great! I've increased "${dish.name}" quantity to ${
        existingItem.quantity + (operation.quantity || 1)
      } in your cart.`;
      result.isQuantityUpdate = true;
    } else {
      // Add new item
      await cart.addItem(
        {
          ...dish.toObject(),
          categoryName: dish.category?.name,
        },
        operation.quantity || 1
      );
      result.message = `Perfect! I've added "${dish.name}" to your cart. Anything else you'd like to try?`;
    }

    // Apply offers
    await validateAndApplyOffersToCart(cart);

    // Record the operation
    await recordCartOperation({
      userId,
      conversationId,
      operation: operation.operation,
      dishId: dish._id,
      quantity: operation.quantity,
      status: "completed",
      metadata: {
        dishName: dish.name,
        isQuantityUpdate: result.isQuantityUpdate,
      },
    });

    result.success = true;
    result.dish = dish;
    result.operation = operation.operation;
    result.quantity = operation.quantity;
    return result;
  } catch (error) {
    console.error("Error in processAddOperation:", error);
    result.message = `I couldn't add "${dish.name}" to your cart right now. Would you like to try adding it manually?`;
    result.fallbackAction = "manual_add";
    return result;
  }
};

/**
 * Process remove operation with context awareness
 */
const processRemoveOperation = async (
  cart,
  dish,
  operation,
  result,
  userId,
  conversationId
) => {
  try {
    const existingItem = cart.items.find(
      (item) => item.dishId.toString() === dish._id.toString()
    );

    if (!existingItem) {
      result.message = `"${dish.name}" isn't in your cart. Would you like to see what you currently have?`;
      result.suggestedAction = "view_cart";
      return result;
    }

    await cart.removeItem(dish._id);
    await validateAndApplyOffersToCart(cart);

    // Record the operation
    await recordCartOperation({
      userId,
      conversationId,
      operation: operation.operation,
      dishId: dish._id,
      quantity: operation.quantity,
      status: "completed",
      metadata: { dishName: dish.name },
    });

    result.success = true;
    result.dish = dish;
    result.operation = operation.operation;
    result.quantity = operation.quantity;
    result.message = `I've removed "${dish.name}" from your cart.`;
    return result;
  } catch (error) {
    console.error("Error in processRemoveOperation:", error);
    result.message = `I couldn't remove "${dish.name}" from your cart. Please try again.`;
    return result;
  }
};

/**
 * Process clear operation
 */
const processClearOperation = async (
  cart,
  userId,
  conversationId,
  foodChainId,
  outletId
) => {
  try {
    const itemCount = cart.items.length;
    await cart.clearCart();

    await recordCartOperation({
      userId,
      conversationId,
      operation: "clear",
      status: "completed",
    });

    const result = {
      success: true,
      message:
        itemCount > 0
          ? `I've cleared all ${itemCount} items from your cart.`
          : "Your cart is already empty.",
      operation: "clear",
      dish: null,
      quantity: 0,
      currentCartState: {
        itemCount: 0,
        totalItems: 0,
        subtotal: 0,
      },
    };

    // Emit real-time cart update for clear operation
    try {
      setTimeout(async () => {
        await emitCartUpdate(userId, foodChainId, outletId, "clear");
        await emitCartOperation(userId, foodChainId, outletId, result);
      }, 100);
    } catch (emitError) {
      console.error("Error emitting cart clear updates:", emitError);
    }

    return result;
  } catch (error) {
    console.error("Error in processClearOperation:", error);
    return {
      success: false,
      message: "I couldn't clear your cart right now. Please try again.",
      operation: "clear",
      dish: null,
      quantity: 0,
    };
  }
};

/**
 * Process order placement operation
 * @param {Object} cart - Cart instance
 * @param {string} userId - User ID
 * @param {string} conversationId - Conversation ID
 * @param {string} foodChainId - Food chain ID
 * @param {string} outletId - Outlet ID
 * @returns {Promise<Object>} - Result of the operation
 */
const processOrderPlacement = async (
  cart,
  userId,
  conversationId,
  foodChainId,
  outletId
) => {
  let order = null;
  let orderCreated = false;

  try {
    console.log("🛒 Processing order placement:", { userId, outletId });

    // Check if cart has items
    if (!cart || cart.items.length === 0) {
      return {
        success: false,
        message:
          "Your cart is empty! Please add some items before placing an order.",
        operation: "order",
        dish: null,
        quantity: 0,
        suggestedAction: "show_menu",
      };
    }

    // Import Order model and other required modules
    const { default: Order } = await import("../models/Order.js");
    const { default: Outlet } = await import("../models/Outlet.js");
    const { emitNewOrder } = await import("../sockets/orderSocket.js");

    // Get outlet details
    const outlet = await Outlet.findById(outletId);
    if (!outlet) {
      return {
        success: false,
        message:
          "Sorry, I couldn't find the outlet information. Please try again.",
        operation: "order",
        dish: null,
        quantity: 0,
      };
    }

    // Apply offers to cart before creating order
    try {
      await validateAndApplyOffersToCart(cart);
      console.log("✅ Offers applied to cart before order placement");
    } catch (offerError) {
      console.error("Error applying offers to cart:", offerError);
      // Continue with order creation even if offer application fails
    }

    // Prepare order data from cart
    const orderData = {
      userId,
      outletId,
      foodChainId: outlet.foodChain,
      items: cart.items.map((item) => ({
        dishId: item.dishId,
        dishName: item.dishName,
        quantity: item.quantity,
        price: item.price,
      })),
      totalAmount: cart.subtotal || 0,
      appliedOffers: cart.appliedOffers || [],
      offerDiscount: cart.totalDiscount || 0,
      finalAmount: cart.finalTotal || cart.subtotal || 0,
      status: "pending",
      paymentMethod: "cash", // Default to cash for AI orders
    };

    // Create the order - this is the critical part
    order = new Order(orderData);
    await order.save();
    orderCreated = true;
    console.log("✅ Order created successfully:", order.orderNumber);

    // Store cart item count before clearing
    const itemCount = cart.items.length;

    // Prepare success response early
    const successResponse = {
      success: true,
      message: `Great! Your order #${order.orderNumber} has been placed successfully. You can track your order now.`,
      operation: "order",
      dish: null,
      quantity: itemCount,
      orderId: order._id,
      orderNumber: order.orderNumber,
      redirectTo: `/order-tracking/${order._id}`,
      suggestedAction: "track_order",
    };

    // Try to perform post-order operations, but don't fail if they error
    try {
      // Clear the cart after successful order placement
      cart.items = [];
      cart.subtotal = 0;
      cart.totalDiscount = 0;
      cart.finalTotal = 0;
      cart.appliedOffers = [];
      cart.status = "converted";
      await cart.save();
      console.log("✅ Cart cleared successfully");
    } catch (cartError) {
      console.error("Error clearing cart (non-critical):", cartError);
    }

    try {
      // Record the operation
      await recordCartOperation({
        userId,
        conversationId,
        operation: "order",
        dishId: null,
        quantity: itemCount,
        status: "completed",
        metadata: {
          orderId: order._id,
          orderNumber: order.orderNumber,
          totalAmount: orderData.totalAmount,
        },
      });
      console.log("✅ Cart operation recorded successfully");
    } catch (recordError) {
      console.error(
        "Error recording cart operation (non-critical):",
        recordError
      );
    }

    try {
      // Emit socket event for real-time notification
      await emitNewOrder(order._id);
      console.log("✅ Socket event emitted successfully");
    } catch (emitError) {
      console.error("Error emitting new order (non-critical):", emitError);
    }

    return successResponse;
  } catch (error) {
    console.error("Error in processOrderPlacement:", error);
    console.error("Error details:", {
      message: error.message,
      stack: error.stack,
      userId,
      outletId,
      cartItems: cart?.items?.length || 0,
      orderCreated,
      orderId: order?._id,
    });

    // If order was created successfully but there was an error after, still return success
    if (orderCreated && order) {
      console.log(
        "⚠️ Order was created but post-creation operations failed. Returning success anyway."
      );
      return {
        success: true,
        message: `Your order #${order.orderNumber} has been placed successfully! You can track your order now.`,
        operation: "order",
        dish: null,
        quantity: cart?.items?.length || 0,
        orderId: order._id,
        orderNumber: order.orderNumber,
        redirectTo: `/order-tracking/${order._id}`,
        suggestedAction: "track_order",
        warning:
          "Some post-order operations failed, but your order is confirmed.",
      };
    }

    // Provide more specific error messages based on the error type
    let errorMessage =
      "I couldn't place your order right now. Please try using the checkout page instead.";

    if (error.message.includes("validation")) {
      errorMessage =
        "There was an issue with your order details. Please check your cart and try again.";
    } else if (
      error.message.includes("network") ||
      error.message.includes("connection")
    ) {
      errorMessage =
        "There seems to be a connection issue. Please try placing your order again in a moment.";
    } else if (
      error.message.includes("outlet") ||
      error.message.includes("foodChain")
    ) {
      errorMessage =
        "There was an issue with the restaurant information. Please try again or contact support.";
    }

    return {
      success: false,
      message: errorMessage,
      operation: "order",
      dish: null,
      quantity: 0,
      fallbackAction: "manual_checkout",
      error: error.message, // Include error for debugging
    };
  }
};

/**
 * Process view operation
 */
const processViewOperation = async (cart, result) => {
  const itemCount = cart.items.length;
  const totalQuantity = cart.items.reduce(
    (sum, item) => sum + item.quantity,
    0
  );

  result.success = true;
  result.operation = "view";
  if (itemCount === 0) {
    result.message =
      "Your cart is empty. Would you like to see some recommendations?";
    result.suggestedAction = "show_recommendations";
  } else {
    result.message = `You have ${totalQuantity} items (${itemCount} different dishes) in your cart totaling ₹${
      cart.subtotal || 0
    }.`;
    result.suggestedAction = "view_cart_details";
  }
  return result;
};
