import Outlet from "../models/Outlet.js";
import Order from "../models/Order.js";
import Dish from "../models/Dish.js";
import { getUserTasteProfile } from "./enhanced-user-preference-service.js";

/**
 * Intelligent Outlet Recommendation Service
 * Provides personalized outlet recommendations based on user preferences, eating habits, and food preferences
 */

/**
 * Get intelligent outlet recommendations
 * @param {string} userId - User ID (optional for personalization)
 * @param {Object} filters - Search filters (city, pincode, etc.)
 * @param {Object} userLocation - User's current location {lat, lng}
 * @param {number} limit - Number of recommendations
 * @returns {Promise<Array>} - Intelligent outlet recommendations
 */
export const getIntelligentOutletRecommendations = async (
  userId = null,
  filters = {},
  userLocation = null,
  limit = 20
) => {
  try {
    let { city, pincode, searchTerm } = filters;

    // Get user's preferred city if not provided in filters
    let userPreferredCity = null;
    if (userId && !city) {
      try {
        const User = (await import("../models/User.js")).default;
        const user = await User.findById(userId).select("city");
        if (user && user.city) {
          userPreferredCity = user.city;
          city = user.city; // Use user's preferred city
          console.log(`🏙️ Using user's preferred city: ${city}`);
        }
      } catch (error) {
        console.error("Error getting user's city:", error);
      }
    }

    // Build base query for active outlets
    const query = { status: "active" };

    if (city) {
      query.city = { $regex: city, $options: "i" };
    }

    if (pincode) {
      query.pincode = pincode;
    }

    if (searchTerm) {
      query.$or = [
        { name: { $regex: searchTerm, $options: "i" } },
        { address: { $regex: searchTerm, $options: "i" } }
      ];
    }

    // Get outlets with enhanced data
    let outlets = await Outlet.find(query)
      .populate("foodChain", "name theme")
      .populate("dishes", "name category cuisine isVeg price ratings tags")
      .select(
        "name address city pincode contact isCloudKitchen deliveryRadius dishes operatingHours location"
      )
      .limit(100); // Get more outlets for better filtering

    // If no outlets found and user has a preferred city, try without city filter
    if (outlets.length === 0 && userPreferredCity) {
      console.log("⚠️ No outlets found in user's city, expanding search...");
      delete query.city;
      outlets = await Outlet.find(query)
        .populate("foodChain", "name theme")
        .populate("dishes", "name category cuisine isVeg price ratings tags")
        .select(
          "name address city pincode contact isCloudKitchen deliveryRadius dishes operatingHours location"
        )
        .limit(100);
    }

    // Get user preferences if logged in
    let userProfile = null;
    let userOrderHistory = [];

    if (userId) {
      try {
        userProfile = await getUserTasteProfile(userId);
        userOrderHistory = await Order.find({ userId })
          .populate("outletId", "name")
          .sort({ createdAt: -1 })
          .limit(50);
      } catch (error) {
        console.error("Error getting user profile:", error);
      }
    }

    // Calculate intelligent scores for each outlet
    const scoredOutlets = await Promise.all(
      outlets.map(async (outlet) => {
        const intelligentScore = await calculateIntelligentOutletScore(
          outlet,
          userProfile,
          userOrderHistory,
          userLocation,
          userPreferredCity
        );

        return {
          ...outlet.toObject(),
          intelligentScore: intelligentScore.total,
          recommendationReasons: intelligentScore.reasons,
          matchingPreferences: intelligentScore.matchingPreferences,
          cuisineMatch: intelligentScore.cuisineMatch,
          priceMatch: intelligentScore.priceMatch,
          distanceKm: intelligentScore.distanceKm,
          personalizedCategories: intelligentScore.personalizedCategories,
          isPersonalized: userId ? true : false,
          isInPreferredCity: userPreferredCity && outlet.city &&
            outlet.city.toLowerCase().includes(userPreferredCity.toLowerCase())
        };
      })
    );

    // Sort by intelligent score and apply final filtering
    const recommendations = scoredOutlets
      .filter((outlet) => outlet.intelligentScore > 0)
      .sort((a, b) => b.intelligentScore - a.intelligentScore)
      .slice(0, limit);

    return {
      recommendations,
      isPersonalized: userId ? true : false,
      totalAnalyzed: outlets.length,
      userProfile: userProfile ? {
        profileCompleteness: userProfile.profileCompleteness,
        hasPreferences: userProfile.tasteProfile?.favoriteCuisines?.length > 0
      } : null
    };
  } catch (error) {
    console.error("Error getting intelligent outlet recommendations:", error);
    // Fallback to basic recommendations
    return await getBasicOutletRecommendations(filters, limit);
  }
};

/**
 * Calculate intelligent outlet score with multiple factors
 * @param {Object} outlet - Outlet object
 * @param {Object} userProfile - User taste profile
 * @param {Array} userOrderHistory - User's order history
 * @param {Object} userLocation - User's location
 * @param {string} userPreferredCity - User's preferred city
 * @returns {Promise<Object>} - Intelligent score breakdown
 */
const calculateIntelligentOutletScore = async (
  outlet,
  userProfile,
  userOrderHistory,
  userLocation,
  userPreferredCity = null
) => {
  let score = 10; // Base score for active outlet
  const reasons = [];
  const matchingPreferences = [];
  let cuisineMatch = 0;
  let priceMatch = 0;
  let distanceKm = null;
  let personalizedCategories = [];

  // If no user profile, use basic scoring
  if (!userProfile || !userProfile.tasteProfile) {
    return calculateBasicOutletScore(outlet, userLocation);
  }

  const { tasteProfile, locationPreferences } = userProfile;

  // 0. City Preference Matching (15% weight) - NEW
  if (userPreferredCity && outlet.city) {
    const outletCity = outlet.city.toLowerCase();
    const preferredCity = userPreferredCity.toLowerCase();

    if (outletCity.includes(preferredCity) || preferredCity.includes(outletCity)) {
      score += 15 * 0.15; // 2.25 points bonus
      reasons.push(`Located in your preferred city: ${outlet.city}`);
      matchingPreferences.push("Preferred city");
    }
  }

  // 1. Cuisine Preference Matching (30% weight)
  if (tasteProfile.favoriteCuisines && tasteProfile.favoriteCuisines.length > 0) {
    const outletCuisines = [...new Set(outlet.dishes.map(dish => dish.cuisine).filter(Boolean))];
    
    for (const favCuisine of tasteProfile.favoriteCuisines) {
      if (outletCuisines.includes(favCuisine.cuisine)) {
        const cuisineScore = (favCuisine.preferenceLevel - 3) * 10; // -20 to +20
        score += cuisineScore * 0.3;
        cuisineMatch += favCuisine.preferenceLevel;
        
        if (favCuisine.preferenceLevel >= 4) {
          reasons.push(`Serves your favorite ${favCuisine.cuisine} cuisine`);
          matchingPreferences.push(`${favCuisine.cuisine} cuisine`);
        }
      }
    }
  }

  // 2. Category Preference Matching (25% weight)
  if (tasteProfile.favoriteCategories && tasteProfile.favoriteCategories.length > 0) {
    const outletCategories = [...new Set(outlet.dishes.map(dish => dish.category?.name).filter(Boolean))];
    
    for (const favCategory of tasteProfile.favoriteCategories) {
      if (outletCategories.includes(favCategory.category)) {
        const categoryScore = (favCategory.preferenceLevel - 3) * 8; // -16 to +16
        score += categoryScore * 0.25;
        
        if (favCategory.preferenceLevel >= 4) {
          reasons.push(`Has great ${favCategory.category} options`);
          matchingPreferences.push(`${favCategory.category} dishes`);
          personalizedCategories.push(favCategory.category);
        }
      }
    }
  }

  // 3. Dietary Preference Matching (20% weight)
  if (tasteProfile.isVegetarian !== null && tasteProfile.isVegetarian !== undefined) {
    const vegDishes = outlet.dishes.filter(dish => dish.isVeg).length;
    const nonVegDishes = outlet.dishes.filter(dish => !dish.isVeg).length;
    const totalDishes = outlet.dishes.length;

    if (tasteProfile.isVegetarian) {
      const vegPercentage = totalDishes > 0 ? (vegDishes / totalDishes) * 100 : 0;
      if (vegPercentage > 70) {
        score += 20 * 0.2;
        reasons.push("Excellent vegetarian options");
        matchingPreferences.push("Vegetarian food");
      } else if (vegPercentage > 40) {
        score += 10 * 0.2;
        reasons.push("Good vegetarian choices");
      }
    } else {
      const nonVegPercentage = totalDishes > 0 ? (nonVegDishes / totalDishes) * 100 : 0;
      if (nonVegPercentage > 50) {
        score += 15 * 0.2;
        reasons.push("Great non-vegetarian options");
        matchingPreferences.push("Non-vegetarian food");
      }
    }
  }

  // 4. Price Range Matching (15% weight)
  if (tasteProfile.priceRange?.preferred && tasteProfile.priceRange.preferred !== 'no_preference') {
    const avgPrice = outlet.dishes.length > 0 
      ? outlet.dishes.reduce((sum, dish) => sum + (dish.price || 0), 0) / outlet.dishes.length 
      : 0;

    let priceCategory = 'mid_range';
    if (avgPrice < 200) priceCategory = 'budget';
    else if (avgPrice > 500) priceCategory = 'premium';

    if (priceCategory === tasteProfile.priceRange.preferred) {
      score += 15 * 0.15;
      priceMatch = 1;
      reasons.push(`Matches your ${tasteProfile.priceRange.preferred.replace('_', ' ')} preference`);
      matchingPreferences.push(`${tasteProfile.priceRange.preferred.replace('_', ' ')} pricing`);
    }
  }

  // 5. Location and Distance (10% weight)
  if (userLocation && outlet.location?.coordinates) {
    distanceKm = calculateDistance(
      userLocation.lat,
      userLocation.lng,
      outlet.location.coordinates[1], // latitude
      outlet.location.coordinates[0]  // longitude
    );

    const maxDistance = locationPreferences?.maxDeliveryDistance || 15;
    
    if (distanceKm <= maxDistance) {
      if (distanceKm <= 2) {
        score += 10 * 0.1;
        reasons.push("Very close to you");
      } else if (distanceKm <= 5) {
        score += 8 * 0.1;
        reasons.push("Nearby location");
      } else if (distanceKm <= 10) {
        score += 5 * 0.1;
        reasons.push("Within delivery range");
      }
    } else {
      score -= 5; // Penalty for being too far
    }
  }

  // 6. Order History Bonus (bonus points)
  if (userOrderHistory.length > 0) {
    const hasOrderedHere = userOrderHistory.some(order => 
      order.outletId?._id?.toString() === outlet._id.toString()
    );
    
    if (hasOrderedHere) {
      score += 10;
      reasons.push("You've ordered here before");
      matchingPreferences.push("Previous orders");
    }
  }

  // 7. Cloud Kitchen Preference
  if (locationPreferences?.preferCloudKitchen !== null && locationPreferences?.preferCloudKitchen !== undefined) {
    if (outlet.isCloudKitchen === locationPreferences.preferCloudKitchen) {
      score += 5;
      if (outlet.isCloudKitchen) {
        reasons.push("Cloud kitchen as you prefer");
      } else {
        reasons.push("Dine-in available as you prefer");
      }
    }
  }

  return {
    total: Math.max(0, score),
    reasons: reasons.slice(0, 4), // Limit to top 4 reasons
    matchingPreferences,
    cuisineMatch,
    priceMatch,
    distanceKm,
    personalizedCategories
  };
};

/**
 * Calculate basic outlet score for non-personalized recommendations
 * @param {Object} outlet - Outlet object
 * @param {Object} userLocation - User's location
 * @returns {Object} - Basic score breakdown
 */
const calculateBasicOutletScore = (outlet, userLocation) => {
  let score = 10;
  const reasons = [];
  let distanceKm = null;

  // Distance scoring
  if (userLocation && outlet.location?.coordinates) {
    distanceKm = calculateDistance(
      userLocation.lat,
      userLocation.lng,
      outlet.location.coordinates[1],
      outlet.location.coordinates[0]
    );

    if (distanceKm <= 2) {
      score += 15;
      reasons.push("Very close to you");
    } else if (distanceKm <= 5) {
      score += 10;
      reasons.push("Nearby location");
    } else if (distanceKm <= 10) {
      score += 5;
      reasons.push("Within delivery range");
    }
  }

  // Dish variety scoring
  const dishCount = outlet.dishes.length;
  if (dishCount > 50) {
    score += 10;
    reasons.push("Wide variety of dishes");
  } else if (dishCount > 20) {
    score += 5;
    reasons.push("Good variety of options");
  }

  // Rating scoring (if available)
  const avgRating = outlet.dishes.length > 0 
    ? outlet.dishes.reduce((sum, dish) => sum + (dish.ratings?.average || 0), 0) / outlet.dishes.length 
    : 0;
  
  if (avgRating > 4) {
    score += 10;
    reasons.push("Highly rated dishes");
  } else if (avgRating > 3.5) {
    score += 5;
    reasons.push("Well-rated options");
  }

  return {
    total: score,
    reasons: reasons.slice(0, 3),
    matchingPreferences: [],
    cuisineMatch: 0,
    priceMatch: 0,
    distanceKm,
    personalizedCategories: []
  };
};

/**
 * Calculate distance between two points using Haversine formula
 * @param {number} lat1 - Latitude 1
 * @param {number} lon1 - Longitude 1
 * @param {number} lat2 - Latitude 2
 * @param {number} lon2 - Longitude 2
 * @returns {number} - Distance in kilometers
 */
const calculateDistance = (lat1, lon1, lat2, lon2) => {
  const R = 6371; // Radius of the Earth in kilometers
  const dLat = (lat2 - lat1) * Math.PI / 180;
  const dLon = (lon2 - lon1) * Math.PI / 180;
  const a = 
    Math.sin(dLat/2) * Math.sin(dLat/2) +
    Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) * 
    Math.sin(dLon/2) * Math.sin(dLon/2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
  return R * c;
};

/**
 * Fallback basic outlet recommendations
 * @param {Object} filters - Search filters
 * @param {number} limit - Number of recommendations
 * @returns {Promise<Object>} - Basic recommendations
 */
const getBasicOutletRecommendations = async (filters, limit) => {
  try {
    const { city, pincode } = filters;
    const query = { status: "active" };

    if (city) {
      query.city = { $regex: city, $options: "i" };
    }

    if (pincode) {
      query.pincode = pincode;
    }

    const outlets = await Outlet.find(query)
      .populate("foodChain", "name theme")
      .populate("dishes", "name category cuisine isVeg price")
      .select(
        "name address city pincode contact isCloudKitchen deliveryRadius dishes"
      )
      .limit(limit);

    return {
      recommendations: outlets.map(outlet => ({
        ...outlet.toObject(),
        intelligentScore: 10,
        recommendationReasons: ["Available in your area"],
        isPersonalized: false
      })),
      isPersonalized: false,
      totalAnalyzed: outlets.length,
      userProfile: null
    };
  } catch (error) {
    console.error("Error getting basic outlet recommendations:", error);
    return {
      recommendations: [],
      isPersonalized: false,
      totalAnalyzed: 0,
      userProfile: null
    };
  }
};
