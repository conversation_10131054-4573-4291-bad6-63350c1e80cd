import { pipeline } from "@xenova/transformers";
import DishEmbedding from "../models/DishEmbedding.js";
import Dish from "../models/Dish.js";

// Initialize the embedding pipeline (using open-source Sentence Transformers)
let embeddingPipeline = null;

/**
 * Initialize the embedding pipeline
 * @returns {Promise<Object>} - The embedding pipeline
 */
const initializeEmbeddingPipeline = async () => {
  // In test mode, do not load heavy pipeline
  if (process.env.NODE_ENV === "test" || process.env.FAST_TEST_EMBEDDINGS === "1") {
    return null;
  }
  if (!embeddingPipeline) {
    try {
      console.log("Initializing embedding pipeline...");
      // Using a lightweight, fast model for embeddings
      embeddingPipeline = await pipeline(
        "feature-extraction",
        "Xenova/all-MiniLM-L6-v2",
        {
          quantized: true, // Use quantized model for better performance
          progress_callback: null, // Disable progress logging
        }
      );
      console.log("Embedding pipeline initialized successfully");
    } catch (error) {
      console.error("Error initializing embedding pipeline:", error);
      throw error;
    }
  }
  return embeddingPipeline;
};

/**
 * Generate embeddings for a text using open-source Sentence Transformers
 * @param {string} text - The text to generate embeddings for
 * @returns {Promise<Array<number>>} - The embedding vector
 */
export const generateEmbedding = async (text) => {
  try {
    const pipe = await initializeEmbeddingPipeline();
    // In fast test mode, or if pipeline unavailable, return a deterministic fake embedding
    if (!pipe) {
      // Simple hash to vector for determinism in tests
      const vec = new Array(64).fill(0);
      let h = 0;
      for (let i = 0; i < (text || "").length; i++) h = (h * 31 + text.charCodeAt(i)) >>> 0;
      for (let i = 0; i < vec.length; i++) vec[i] = ((h >> (i % 24)) & 255) / 255;
      return vec;
    }

    // Generate embeddings
    const output = await pipe(text, {
      pooling: "mean",
      normalize: true,
    });

    // Convert tensor to array
    const embeddings = Array.from(output.data);

    return embeddings;
  } catch (error) {
    console.error("Error generating embedding:", error);
    // Fallback: return a zero vector if embedding fails
    return new Array(384).fill(0); // MiniLM-L6-v2 produces 384-dimensional vectors
  }
};

/**
 * Enhanced dish filtering using vector similarity for AI recommendations
 * @param {string} userQuery - User's query/requirement
 * @param {Array} availableDishes - All available dishes
 * @param {Object} options - Filtering options
 * @returns {Promise<Array>} - Filtered dishes optimized for AI processing
 */
export const getVectorFilteredDishesForAI = async (
  userQuery,
  availableDishes,
  options = {}
) => {
  try {
    const {
      maxDishes = 15, // Limit for AI processing
      minSimilarity = 0.3,
      includePopular = true,
      userPreferences = {},
      contextFilters = {},
    } = options;

    console.log(
      `🎯 Vector filtering ${availableDishes.length} dishes for AI processing...`
    );

    // Generate embedding for user query
    const queryEmbedding = await generateEmbedding(userQuery);

    // Calculate similarity scores for all dishes
    const dishesWithScores = await Promise.all(
      availableDishes.map(async (dish) => {
        try {
          // Create dish text representation
          const dishText = `${dish.name} ${dish.description || ""} ${
            dish.cuisine || ""
          } ${dish.tags?.join(" ") || ""} ${dish.category?.name || ""}`;

          // Generate dish embedding
          const dishEmbedding = await generateEmbedding(dishText);

          // Calculate cosine similarity
          const similarity = calculateCosineSimilarity(
            queryEmbedding,
            dishEmbedding
          );

          return {
            ...dish,
            vectorSimilarity: similarity,
            dishText: dishText.substring(0, 100), // For debugging
          };
        } catch (error) {
          console.error(`Error processing dish ${dish._id}:`, error);
          return {
            ...dish,
            vectorSimilarity: 0,
            dishText: dish.name || "",
          };
        }
      })
    );

    // Apply additional filtering based on context
    let filteredDishes = dishesWithScores.filter((dish) => {
      // Basic similarity threshold
      if (dish.vectorSimilarity < minSimilarity) return false;

      // Apply dietary preferences
      if (contextFilters.isVegetarian && !dish.isVeg) return false;
      if (contextFilters.isNonVegetarian && dish.isVeg) return false;

      // Apply price range
      if (contextFilters.maxPrice && dish.price > contextFilters.maxPrice)
        return false;
      if (contextFilters.minPrice && dish.price < contextFilters.minPrice)
        return false;

      return true;
    });

    // Sort by combined score (similarity + popularity + user history)
    filteredDishes.sort((a, b) => {
      let scoreA = a.vectorSimilarity * 0.7; // 70% weight to similarity
      let scoreB = b.vectorSimilarity * 0.7;

      // Add popularity score (20% weight)
      if (includePopular) {
        const popularityA =
          ((a.ratings?.average || 0) * (a.ratings?.count || 0)) / 100;
        const popularityB =
          ((b.ratings?.average || 0) * (b.ratings?.count || 0)) / 100;
        scoreA += popularityA * 0.2;
        scoreB += popularityB * 0.2;
      }

      // Add user history score (10% weight)
      if (userPreferences.dishFrequency) {
        const historyA = userPreferences.dishFrequency[a._id?.toString()] || 0;
        const historyB = userPreferences.dishFrequency[b._id?.toString()] || 0;
        scoreA += historyA * 0.1;
        scoreB += historyB * 0.1;
      }

      return scoreB - scoreA;
    });

    // Limit to maxDishes for AI processing
    const finalDishes = filteredDishes.slice(0, maxDishes);

    console.log(
      `✅ Vector filtering complete: ${finalDishes.length} dishes selected for AI`
    );
    console.log(
      `📊 Similarity scores: ${finalDishes
        .map((d) => d.vectorSimilarity.toFixed(3))
        .join(", ")}`
    );

    return {
      dishes: finalDishes,
      metadata: {
        originalCount: availableDishes.length,
        filteredCount: finalDishes.length,
        averageSimilarity:
          finalDishes.reduce((sum, d) => sum + d.vectorSimilarity, 0) /
          finalDishes.length,
        reductionPercentage: (
          ((availableDishes.length - finalDishes.length) /
            availableDishes.length) *
          100
        ).toFixed(1),
      },
    };
  } catch (error) {
    console.error("Error in vector filtering for AI:", error);
    // Fallback: return first maxDishes
    return {
      dishes: availableDishes.slice(0, options.maxDishes || 15),
      metadata: { error: true, fallback: true },
    };
  }
};

/**
 * Create or update embeddings for a dish
 * @param {Object} dish - The dish object
 * @returns {Promise<Object>} - The created or updated embedding
 */
export const createOrUpdateDishEmbedding = async (dish) => {
  try {
    // Create text representation of the dish for embedding
    const dishText = `${dish.name} ${dish.description || ""} ${
      dish.cuisine || ""
    } ${dish.tags?.join(" ") || ""}`;

    // Generate embedding
    const embedding = await generateEmbedding(dishText);

    // Check if embedding already exists
    let dishEmbedding = await DishEmbedding.findOne({ dishId: dish._id });

    if (dishEmbedding) {
      // Update existing embedding
      dishEmbedding.embedding = embedding;
      dishEmbedding.metadata = {
        name: dish.name,
        description: dish.description,
        category: dish.category?.name || "",
        price: dish.price,
        tags: dish.tags || [],
        cuisine: dish.cuisine || "",
      };
      dishEmbedding.updatedAt = new Date();
      await dishEmbedding.save();
    } else {
      // Create new embedding
      dishEmbedding = new DishEmbedding({
        dishId: dish._id,
        foodChainId: dish.foodChain,
        outletId: dish.outlets[0], // Assuming the first outlet
        embedding: embedding,
        metadata: {
          name: dish.name,
          description: dish.description,
          category: dish.category?.name || "",
          price: dish.price,
          tags: dish.tags || [],
          cuisine: dish.cuisine || "",
        },
      });
      await dishEmbedding.save();
    }

    return dishEmbedding;
  } catch (error) {
    console.error("Error creating/updating dish embedding:", error);
    throw error;
  }
};

/**
 * Find similar dishes using vector similarity search with fallback
 * @param {string} query - The search query
 * @param {string} foodChainId - The food chain ID
 * @param {string} outletId - The outlet ID
 * @param {number} limit - Maximum number of results to return
 * @returns {Promise<Array<Object>>} - Array of similar dishes
 */
export const findSimilarDishes = async (
  query,
  foodChainId,
  outletId,
  limit = 5
) => {
  try {
    // First, try to use our enhanced menu search service as primary method
    const { searchDishes } = await import("./menu-search-service.js");

    // Get available dishes for this outlet
    const availableDishes = await Dish.find({
      foodChain: foodChainId,
      isAvailable: true,
      outlets: outletId,
    }).populate("category", "name");

    // Use enhanced search as primary method
    const searchResults = await searchDishes(query, availableDishes, {
      limit,
      threshold: 0.1,
      filters: { availableOnly: true },
    });

    if (searchResults.length > 0) {
      // Convert search results to vector search format for compatibility
      return searchResults.map((dish) => ({
        dish: dish,
        similarity: dish.relevanceScore || 0.5,
        metadata: {
          name: dish.name,
          description: dish.description,
          category: dish.category?.name || "",
          price: dish.price,
          tags: dish.tags || [],
          cuisine: dish.cuisine || "",
        },
      }));
    }

    // Fallback to vector search if enhanced search returns no results
    return await findSimilarDishesVector(query, foodChainId, outletId, limit);
  } catch (error) {
    console.error("Error finding similar dishes:", error);
    // Final fallback: return empty array
    return [];
  }
};

/**
 * Find similar dishes using pure vector similarity search (fallback method)
 * @param {string} query - The search query
 * @param {string} foodChainId - The food chain ID
 * @param {string} outletId - The outlet ID
 * @param {number} limit - Maximum number of results to return
 * @returns {Promise<Array<Object>>} - Array of similar dishes
 */
export const findSimilarDishesVector = async (
  query,
  foodChainId,
  outletId,
  limit = 5
) => {
  try {
    // Generate embedding for the query
    const queryEmbedding = await generateEmbedding(query);

    // Find dishes with similar embeddings
    const dishEmbeddings = await DishEmbedding.find({
      foodChainId,
      outletId,
    }).populate("dishId");

    if (dishEmbeddings.length === 0) {
      console.log("No dish embeddings found, returning empty results");
      return [];
    }

    // Calculate cosine similarity
    const results = dishEmbeddings.map((embedding) => {
      const similarity = calculateCosineSimilarity(
        queryEmbedding,
        embedding.embedding
      );
      return {
        dish: embedding.dishId,
        similarity,
        metadata: embedding.metadata,
      };
    });

    // Sort by similarity (highest first) and limit results
    return results.sort((a, b) => b.similarity - a.similarity).slice(0, limit);
  } catch (error) {
    console.error("Error in vector similarity search:", error);
    return [];
  }
};

/**
 * Calculate cosine similarity between two vectors
 * @param {Array<number>} vec1 - First vector
 * @param {Array<number>} vec2 - Second vector
 * @returns {number} - Cosine similarity (between -1 and 1)
 */
export const calculateCosineSimilarity = (vec1, vec2) => {
  if (vec1.length !== vec2.length) {
    throw new Error("Vectors must have the same length");
  }

  let dotProduct = 0;
  let mag1 = 0;
  let mag2 = 0;

  for (let i = 0; i < vec1.length; i++) {
    dotProduct += vec1[i] * vec2[i];
    mag1 += vec1[i] * vec1[i];
    mag2 += vec2[i] * vec2[i];
  }

  mag1 = Math.sqrt(mag1);
  mag2 = Math.sqrt(mag2);

  if (mag1 === 0 || mag2 === 0) {
    return 0;
  }

  return dotProduct / (mag1 * mag2);
};

/**
 * Generate embeddings for all dishes in the database
 * @returns {Promise<number>} - Number of dishes processed
 */
export const generateAllDishEmbeddings = async () => {
  try {
    const dishes = await Dish.find().populate("category", "name");
    let count = 0;

    for (const dish of dishes) {
      await createOrUpdateDishEmbedding(dish);
      count++;
    }

    return count;
  } catch (error) {
    console.error("Error generating all dish embeddings:", error);
    throw error;
  }
};
