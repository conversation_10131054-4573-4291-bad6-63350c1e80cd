/**
 * Recommendation Monitoring Service
 * Tracks recommendation accuracy and identifies common issues
 */

let recommendationMetrics = {
  totalRequests: 0,
  accurateRecommendations: 0,
  mismatchedRecommendations: 0,
  commonIssues: {},
  lastReset: new Date()
};

/**
 * Monitor and log recommendation accuracy
 * @param {string} userQuery - Original user query
 * @param {Object} recommendationResult - The recommendation result
 * @param {Array} availableDishes - All available dishes
 */
export const monitorRecommendationAccuracy = (userQuery, recommendationResult, availableDishes) => {
  try {
    recommendationMetrics.totalRequests++;
    
    const issues = analyzeRecommendationAccuracy(userQuery, recommendationResult, availableDishes);
    
    if (issues.length === 0) {
      recommendationMetrics.accurateRecommendations++;
      console.log("✅ Recommendation accuracy: GOOD");
    } else {
      recommendationMetrics.mismatchedRecommendations++;
      console.log(`⚠️ Recommendation accuracy: ISSUES FOUND (${issues.length})`);
      
      // Track common issues
      issues.forEach(issue => {
        const issueKey = issue.type;
        recommendationMetrics.commonIssues[issueKey] = (recommendationMetrics.commonIssues[issueKey] || 0) + 1;
      });
      
      // Log detailed issue information
      console.log("🔍 Recommendation Issues:", issues.map(i => i.description));
    }
    
    // Log accuracy percentage every 10 requests
    if (recommendationMetrics.totalRequests % 10 === 0) {
      const accuracy = (recommendationMetrics.accurateRecommendations / recommendationMetrics.totalRequests * 100).toFixed(1);
      console.log(`📊 Current recommendation accuracy: ${accuracy}% (${recommendationMetrics.accurateRecommendations}/${recommendationMetrics.totalRequests})`);
    }
    
  } catch (error) {
    console.error("Error in recommendation monitoring:", error);
  }
};

/**
 * Analyze recommendation accuracy and identify issues
 */
const analyzeRecommendationAccuracy = (userQuery, recommendationResult, availableDishes) => {
  const issues = [];
  const { recommendations, aiResponse } = recommendationResult;
  
  if (!recommendations || !aiResponse) {
    return issues;
  }
  
  const userQueryLower = userQuery.toLowerCase();
  const aiMessageLower = aiResponse.aiMessage?.toLowerCase() || "";
  const recommendedDishNames = recommendations.map(dish => dish.name.toLowerCase());
  
  // Issue 1: User asked for specific dish but didn't get it
  const specificDishRequests = [
    "momos", "momo", "noodles", "noodle", "pizza", "burger", "biryani",
    "curry", "dal", "rice", "chicken", "paneer", "mutton", "fish",
    "samosa", "pakora", "dosa", "idli", "vada", "uttapam"
  ];
  
  const requestedDishes = specificDishRequests.filter(dish => userQueryLower.includes(dish));
  
  if (requestedDishes.length > 0) {
    const notFulfilled = requestedDishes.filter(requested => 
      !recommendedDishNames.some(recDish => recDish.includes(requested))
    );
    
    if (notFulfilled.length > 0) {
      // Check if the requested dishes are actually available
      const availableRequestedDishes = availableDishes.filter(dish => {
        const dishName = dish.name.toLowerCase();
        return notFulfilled.some(requested => dishName.includes(requested));
      });
      
      if (availableRequestedDishes.length > 0) {
        issues.push({
          type: "missed_available_dish",
          description: `User requested ${notFulfilled.join(", ")} which are available but not recommended`,
          severity: "high",
          requestedDishes: notFulfilled,
          availableMatches: availableRequestedDishes.map(d => d.name)
        });
      } else {
        issues.push({
          type: "unavailable_dish_request",
          description: `User requested ${notFulfilled.join(", ")} which are not available`,
          severity: "low",
          requestedDishes: notFulfilled
        });
      }
    }
  }
  
  // Issue 2: AI message mentions dishes not in recommendations
  const dishesInMessage = specificDishRequests.filter(dish => aiMessageLower.includes(dish));
  const dishesNotInRecommendations = dishesInMessage.filter(dish => 
    !recommendedDishNames.some(recDish => recDish.includes(dish))
  );
  
  if (dishesNotInRecommendations.length > 0) {
    issues.push({
      type: "message_recommendation_mismatch",
      description: `AI message mentions ${dishesNotInRecommendations.join(", ")} but recommendations don't include these`,
      severity: "medium",
      mentionedDishes: dishesNotInRecommendations
    });
  }
  
  // Issue 3: Dietary preference violation
  const isVegRequest = /\b(veg|vegetarian|veggie)\b/i.test(userQuery);
  const isNonVegRequest = /\b(non-veg|non vegetarian|meat|chicken|fish|mutton)\b/i.test(userQuery);
  
  if (isVegRequest && !isNonVegRequest) {
    const nonVegRecommendations = recommendations.filter(dish => dish.isVeg === false);
    if (nonVegRecommendations.length > 0) {
      issues.push({
        type: "dietary_preference_violation",
        description: `User requested vegetarian food but got non-veg recommendations: ${nonVegRecommendations.map(d => d.name).join(", ")}`,
        severity: "high",
        violatingDishes: nonVegRecommendations.map(d => d.name)
      });
    }
  }
  
  if (isNonVegRequest && !isVegRequest) {
    const vegRecommendations = recommendations.filter(dish => dish.isVeg === true);
    if (vegRecommendations.length === recommendations.length) {
      issues.push({
        type: "dietary_preference_violation",
        description: `User requested non-vegetarian food but got only veg recommendations`,
        severity: "medium",
        violatingDishes: vegRecommendations.map(d => d.name)
      });
    }
  }
  
  return issues;
};

/**
 * Get current recommendation metrics
 */
export const getRecommendationMetrics = () => {
  const accuracy = recommendationMetrics.totalRequests > 0 
    ? (recommendationMetrics.accurateRecommendations / recommendationMetrics.totalRequests * 100).toFixed(1)
    : 0;
    
  return {
    ...recommendationMetrics,
    accuracyPercentage: accuracy,
    topIssues: Object.entries(recommendationMetrics.commonIssues)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 5)
      .map(([issue, count]) => ({ issue, count }))
  };
};

/**
 * Reset recommendation metrics
 */
export const resetRecommendationMetrics = () => {
  recommendationMetrics = {
    totalRequests: 0,
    accurateRecommendations: 0,
    mismatchedRecommendations: 0,
    commonIssues: {},
    lastReset: new Date()
  };
};
