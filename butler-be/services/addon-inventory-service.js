import AddOn from "../models/AddOn.js";
import { InventoryItem, InventoryTransaction } from "../models/Inventory.js";

/**
 * Process add-on inventory reduction for an order
 * @param {Object} order - Order object with items containing add-ons
 * @param {string} userId - User ID for transaction tracking
 * @returns {Promise<Object>} - Result of inventory processing
 */
export const processAddOnInventory = async (order, userId = null) => {
  try {
    const results = [];
    const errors = [];

    // Process each order item
    for (const item of order.items) {
      if (item.addOns && item.addOns.length > 0) {
        // Process each add-on in the item
        for (const addOnItem of item.addOns) {
          try {
            // Find the add-on with inventory tracking
            const addOn = await AddOn.findById(addOnItem.addOnId);
            
            if (!addOn) {
              errors.push(`Add-on not found: ${addOnItem.addOnId}`);
              continue;
            }

            if (!addOn.trackInventory || !addOn.inventoryItem) {
              // Skip add-ons that don't track inventory
              results.push({
                addOnId: addOn._id,
                addOnName: addOn.name,
                message: "No inventory tracking",
                skipped: true
              });
              continue;
            }

            // Calculate total quantity needed (item quantity * add-on quantity)
            const totalAddOnQuantity = item.quantity * (addOnItem.quantity || 1);

            // Check inventory availability
            const availabilityCheck = await addOn.checkInventoryAvailability(totalAddOnQuantity);
            
            if (!availabilityCheck.available) {
              errors.push(`${addOn.name}: ${availabilityCheck.message}`);
              continue;
            }

            // Reduce inventory
            const reductionResult = await addOn.reduceInventory(
              totalAddOnQuantity,
              order._id,
              userId
            );

            if (reductionResult.success) {
              results.push({
                addOnId: addOn._id,
                addOnName: addOn.name,
                quantityReduced: totalAddOnQuantity * addOn.inventoryQuantityPerUnit,
                newInventoryQuantity: reductionResult.newQuantity,
                message: reductionResult.message
              });
            } else {
              errors.push(`${addOn.name}: ${reductionResult.message}`);
            }

          } catch (addOnError) {
            console.error(`Error processing add-on ${addOnItem.addOnId}:`, addOnError);
            errors.push(`Error processing add-on ${addOnItem.addOnId}: ${addOnError.message}`);
          }
        }
      }
    }

    return {
      success: errors.length === 0,
      results,
      errors,
      message: errors.length === 0 
        ? `Successfully processed ${results.length} add-on inventory items`
        : `Processed with ${errors.length} errors`
    };

  } catch (error) {
    console.error("Error in processAddOnInventory:", error);
    return {
      success: false,
      results: [],
      errors: [error.message],
      message: "Failed to process add-on inventory"
    };
  }
};

/**
 * Check add-on inventory availability for an order before creation
 * @param {Array} items - Order items with add-ons
 * @returns {Promise<Object>} - Availability check result
 */
export const checkAddOnInventoryAvailability = async (items) => {
  try {
    const checks = [];
    const unavailableItems = [];

    for (const item of items) {
      if (item.addOns && item.addOns.length > 0) {
        for (const addOnItem of item.addOns) {
          try {
            const addOn = await AddOn.findById(addOnItem.addOnId);
            
            if (!addOn) {
              unavailableItems.push(`Add-on not found: ${addOnItem.addOnId}`);
              continue;
            }

            if (!addOn.trackInventory || !addOn.inventoryItem) {
              // Skip add-ons that don't track inventory
              continue;
            }

            const totalAddOnQuantity = item.quantity * (addOnItem.quantity || 1);
            const availabilityCheck = await addOn.checkInventoryAvailability(totalAddOnQuantity);
            
            checks.push({
              addOnId: addOn._id,
              addOnName: addOn.name,
              available: availabilityCheck.available,
              message: availabilityCheck.message,
              requiredQuantity: totalAddOnQuantity * addOn.inventoryQuantityPerUnit
            });

            if (!availabilityCheck.available) {
              unavailableItems.push(`${addOn.name}: ${availabilityCheck.message}`);
            }

          } catch (addOnError) {
            console.error(`Error checking add-on ${addOnItem.addOnId}:`, addOnError);
            unavailableItems.push(`Error checking add-on ${addOnItem.addOnId}: ${addOnError.message}`);
          }
        }
      }
    }

    return {
      available: unavailableItems.length === 0,
      checks,
      unavailableItems,
      message: unavailableItems.length === 0 
        ? "All add-on inventory is available"
        : `${unavailableItems.length} add-on inventory issues found`
    };

  } catch (error) {
    console.error("Error in checkAddOnInventoryAvailability:", error);
    return {
      available: false,
      checks: [],
      unavailableItems: [error.message],
      message: "Failed to check add-on inventory availability"
    };
  }
};

/**
 * Restore add-on inventory when an order is cancelled
 * @param {Object} order - Order object with items containing add-ons
 * @param {string} userId - User ID for transaction tracking
 * @returns {Promise<Object>} - Result of inventory restoration
 */
export const restoreAddOnInventory = async (order, userId = null) => {
  try {
    const results = [];
    const errors = [];

    // Process each order item
    for (const item of order.items) {
      if (item.addOns && item.addOns.length > 0) {
        // Process each add-on in the item
        for (const addOnItem of item.addOns) {
          try {
            const addOn = await AddOn.findById(addOnItem.addOnId);
            
            if (!addOn || !addOn.trackInventory || !addOn.inventoryItem) {
              continue; // Skip add-ons that don't track inventory
            }

            // Calculate total quantity to restore
            const totalAddOnQuantity = item.quantity * (addOnItem.quantity || 1);
            const quantityToRestore = totalAddOnQuantity * addOn.inventoryQuantityPerUnit;

            // Find and update inventory item
            const inventoryItem = await InventoryItem.findById(addOn.inventoryItem);
            
            if (!inventoryItem) {
              errors.push(`Inventory item not found for add-on: ${addOn.name}`);
              continue;
            }

            const previousQuantity = inventoryItem.quantity;
            inventoryItem.quantity += quantityToRestore;
            inventoryItem.updatedAt = new Date();
            await inventoryItem.save();

            // Create transaction record
            if (userId) {
              const transaction = new InventoryTransaction({
                itemId: inventoryItem._id,
                type: "addition",
                quantity: quantityToRestore,
                previousQuantity,
                newQuantity: inventoryItem.quantity,
                reason: `Restored from cancelled order: ${order.orderNumber}`,
                orderId: order._id,
                userId,
                outletId: inventoryItem.outletId,
                foodChainId: inventoryItem.foodChainId,
              });
              await transaction.save();
            }

            results.push({
              addOnId: addOn._id,
              addOnName: addOn.name,
              quantityRestored: quantityToRestore,
              newInventoryQuantity: inventoryItem.quantity
            });

          } catch (addOnError) {
            console.error(`Error restoring add-on ${addOnItem.addOnId}:`, addOnError);
            errors.push(`Error restoring add-on ${addOnItem.addOnId}: ${addOnError.message}`);
          }
        }
      }
    }

    return {
      success: errors.length === 0,
      results,
      errors,
      message: errors.length === 0 
        ? `Successfully restored ${results.length} add-on inventory items`
        : `Restored with ${errors.length} errors`
    };

  } catch (error) {
    console.error("Error in restoreAddOnInventory:", error);
    return {
      success: false,
      results: [],
      errors: [error.message],
      message: "Failed to restore add-on inventory"
    };
  }
};
