/**
 * Recommendation Validator Service
 * Ensures consistency between AI messages and actual recommended dishes
 * Fixes common recommendation accuracy issues
 */

/**
 * Validate and fix recommendation consistency
 * @param {Object} recommendationResult - The recommendation result to validate
 * @param {string} userQuery - Original user query
 * @param {Array} availableDishes - All available dishes
 * @returns {Object} - Fixed recommendation result
 */
export const validateAndFixRecommendations = (recommendationResult, userQuery, availableDishes) => {
  try {
    const { recommendations, aiResponse } = recommendationResult;
    
    if (!recommendations || !aiResponse) {
      return recommendationResult;
    }

    console.log("🔍 Validating recommendation consistency...");
    
    // 1. Check if AI message mentions dishes that aren't in recommendations
    const issues = detectConsistencyIssues(recommendations, aiResponse, userQuery);
    
    if (issues.length === 0) {
      console.log("✅ No consistency issues found");
      return recommendationResult;
    }

    console.log(`⚠️ Found ${issues.length} consistency issues:`, issues);
    
    // 2. Fix the issues
    const fixedResult = fixConsistencyIssues(recommendationResult, issues, userQuery, availableDishes);
    
    console.log("🔧 Applied consistency fixes");
    return fixedResult;
    
  } catch (error) {
    console.error("Error in recommendation validation:", error);
    return recommendationResult;
  }
};

/**
 * Detect consistency issues between AI message and recommendations
 */
const detectConsistencyIssues = (recommendations, aiResponse, userQuery) => {
  const issues = [];
  const recommendedDishNames = recommendations.map(dish => dish.name.toLowerCase());
  const aiMessage = aiResponse.aiMessage?.toLowerCase() || "";
  
  // Issue 1: AI message mentions dishes not in recommendations
  const commonDishNames = [
    "momos", "momo", "noodles", "noodle", "pizza", "burger", "biryani", 
    "curry", "dal", "rice", "chicken", "paneer", "mutton", "fish"
  ];
  
  const mentionedInMessage = commonDishNames.filter(dish => aiMessage.includes(dish));
  const mentionedButNotRecommended = mentionedInMessage.filter(dish => 
    !recommendedDishNames.some(recDish => recDish.includes(dish))
  );
  
  if (mentionedButNotRecommended.length > 0) {
    issues.push({
      type: "message_dish_mismatch",
      details: `AI message mentions ${mentionedButNotRecommended.join(", ")} but recommendations don't include these`,
      mentionedDishes: mentionedButNotRecommended
    });
  }
  
  // Issue 2: User asked for specific dish but got different recommendations
  const userQueryLower = userQuery.toLowerCase();
  const userRequestedDishes = commonDishNames.filter(dish => userQueryLower.includes(dish));

  if (userRequestedDishes.length > 0) {
    // Check if ALL recommendations match the requested dish type
    const allRecommendationsMatch = recommendations.every(dish => {
      const dishName = dish.name.toLowerCase();
      const dishTags = (dish.tags || []).map(tag => tag.toLowerCase()).join(" ");

      return userRequestedDishes.some(requested =>
        dishName.includes(requested) || dishTags.includes(requested)
      );
    });

    if (!allRecommendationsMatch) {
      // Find which dishes don't match the request
      const nonMatchingDishes = recommendations.filter(dish => {
        const dishName = dish.name.toLowerCase();
        const dishTags = (dish.tags || []).map(tag => tag.toLowerCase()).join(" ");

        return !userRequestedDishes.some(requested =>
          dishName.includes(requested) || dishTags.includes(requested)
        );
      });

      issues.push({
        type: "request_recommendation_mismatch",
        details: `User requested ${userRequestedDishes.join(", ")} but got unrelated dishes: ${nonMatchingDishes.map(d => d.name).join(", ")}`,
        requestedDishes: userRequestedDishes,
        nonMatchingDishes: nonMatchingDishes.map(d => d.name),
        severity: "high"
      });
    }
  }
  
  return issues;
};

/**
 * Fix consistency issues in recommendations
 */
const fixConsistencyIssues = (recommendationResult, issues, userQuery, availableDishes) => {
  let { recommendations, aiResponse } = recommendationResult;
  
  for (const issue of issues) {
    if (issue.type === "message_dish_mismatch") {
      // Fix AI message to match actual recommendations
      const actualDishNames = recommendations.slice(0, 2).map(dish => dish.name);
      aiResponse.aiMessage = `I recommend ${actualDishNames.join(" and ")} based on your request. These are great options for you.`;
      
    } else if (issue.type === "request_recommendation_mismatch") {
      // For specific dish requests, filter out non-matching dishes and find better matches
      const betterMatches = findBetterMatches(issue.requestedDishes, availableDishes);

      if (betterMatches.length > 0) {
        // Replace ALL recommendations with only matching dishes
        recommendations = betterMatches.slice(0, Math.max(3, recommendations.length));
        aiResponse.recommendedDishIds = recommendations.map(dish => dish._id.toString());

        const dishNames = recommendations.slice(0, 2).map(d => d.name);
        if (issue.requestedDishes.length === 1) {
          aiResponse.aiMessage = `Here are our ${issue.requestedDishes[0]} options: ${dishNames.join(" and ")}.`;
        } else {
          aiResponse.aiMessage = `Here are ${issue.requestedDishes.join(" and ")} options: ${dishNames.join(" and ")}.`;
        }
      } else {
        // Filter out non-matching dishes from current recommendations
        const matchingRecommendations = recommendations.filter(dish => {
          const dishName = dish.name.toLowerCase();
          const dishTags = (dish.tags || []).map(tag => tag.toLowerCase()).join(" ");

          return issue.requestedDishes.some(requested =>
            dishName.includes(requested) || dishTags.includes(requested)
          );
        });

        if (matchingRecommendations.length > 0) {
          recommendations = matchingRecommendations;
          aiResponse.recommendedDishIds = recommendations.map(dish => dish._id.toString());
          aiResponse.aiMessage = `Here are our ${issue.requestedDishes.join(" and ")} options: ${recommendations.slice(0, 2).map(d => d.name).join(" and ")}.`;
        } else {
          // No matching dishes available
          aiResponse.aiMessage = `Sorry, ${issue.requestedDishes.join(" and ")} aren't available right now. Here are similar options: ${recommendations.slice(0, 2).map(d => d.name).join(" and ")}.`;
        }
      }
    }
  }
  
  return {
    ...recommendationResult,
    recommendations,
    aiResponse,
    validationApplied: true,
    issuesFixed: issues.length
  };
};

/**
 * Find better matching dishes for user request
 */
const findBetterMatches = (requestedDishes, availableDishes) => {
  const matches = [];
  
  for (const requested of requestedDishes) {
    const dishMatches = availableDishes.filter(dish => {
      const dishName = dish.name.toLowerCase();
      const dishDescription = dish.description?.toLowerCase() || "";
      const dishTags = (dish.tags || []).map(tag => tag.toLowerCase()).join(" ");
      
      return (
        dishName.includes(requested) ||
        dishDescription.includes(requested) ||
        dishTags.includes(requested)
      );
    });
    
    matches.push(...dishMatches);
  }
  
  // Remove duplicates and return top matches
  const uniqueMatches = matches.filter((dish, index, self) => 
    index === self.findIndex(d => d._id.toString() === dish._id.toString())
  );
  
  return uniqueMatches.slice(0, 5);
};
