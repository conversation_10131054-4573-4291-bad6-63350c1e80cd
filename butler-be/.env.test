# Test Environment Variables
NODE_ENV=test

# Database (will be overridden by in-memory MongoDB)
MONGODB_URI=mongodb://localhost:27017/butler-test

# JWT
JWT_SECRET=test-jwt-secret-key-for-testing
JWT_SECRET_SUPER_ADMIN=test-super-admin-jwt-secret

# Email (mocked in tests)
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=test-password

# Razorpay (test keys)
KEY_ID=test_razorpay_key_id
KEY_SECRET=test_razorpay_key_secret

# Groq (mocked in tests)
GROQ_API_KEY=test-groq-api-key

# Campaign Settings
ENABLE_CAMPAIGNS=true

# Test specific settings
FAST_TEST_EMBEDDINGS=1
DISABLE_EXTERNAL_APIS=true
DISABLE_CRON_JOBS=true
