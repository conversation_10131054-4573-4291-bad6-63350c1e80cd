import { config } from "dotenv";
import connectDB from "../config/database.js";
import { generateAllDishEmbeddings } from "../services/vector-service.js";
// Import models to register schemas
import "../models/Dish.js";
import "../models/Category.js";
import "../models/DishEmbedding.js";

// Load environment variables
config();

console.log(
  "🚀 Starting migration from Hugging Face to Groq + Open Source alternatives..."
);

// Connect to database
await connectDB();

async function migrateEmbeddings() {
  console.log(
    "\n📊 Migrating embeddings to open-source Sentence Transformers..."
  );

  try {
    const count = await generateAllDishEmbeddings();
    console.log(
      `✅ Successfully migrated embeddings for ${count} dishes using Xenova Transformers.`
    );
  } catch (error) {
    console.error("❌ Error migrating embeddings:", error);
    console.log(
      "💡 This is optional - the system will work without pre-generated embeddings."
    );
  }
}

async function testGroqIntegration() {
  console.log("\n🧠 Testing Groq integration...");

  try {
    const { generateText } = await import("../services/groq-service.js");

    const testResponse = await generateText(
      "Test message: recommend a popular dish",
      {
        model: "llama-3.3-70b-versatile",
        max_tokens: 50,
        temperature: 0.7,
      }
    );

    console.log("✅ Groq integration working correctly!");
    console.log("📝 Test response:", testResponse.substring(0, 100) + "...");
  } catch (error) {
    console.error("❌ Error testing Groq integration:", error);
    console.log("💡 Please check your GROQ_API_KEY in the .env file.");
  }
}

async function testMenuSearch() {
  console.log("\n🔍 Testing enhanced menu search...");

  try {
    const { extractKeywords } = await import(
      "../services/menu-search-service.js"
    );

    const keywords = await extractKeywords(
      "I want something spicy and vegetarian"
    );
    console.log("✅ Menu search working correctly!");
    console.log("🏷️  Extracted keywords:", keywords);
  } catch (error) {
    console.error("❌ Error testing menu search:", error);
    console.log("💡 Menu search will fall back to simple keyword extraction.");
  }
}

async function cleanupOldReferences() {
  console.log("\n🧹 Checking for old Hugging Face references...");

  // This is informational - the actual cleanup was done manually
  console.log("✅ Hugging Face dependencies removed from package.json");
  console.log("✅ Environment variables updated");
  console.log("✅ Vector service migrated to Xenova Transformers");
  console.log("✅ Groq service enhanced with semantic processing");
}

async function displayMigrationSummary() {
  console.log("\n" + "=".repeat(60));
  console.log("🎉 MIGRATION COMPLETE!");
  console.log("=".repeat(60));
  console.log("\n📋 Migration Summary:");
  console.log("• ❌ Removed: Hugging Face dependencies");
  console.log("• ✅ Added: Xenova Transformers for embeddings");
  console.log("• ✅ Enhanced: Groq service with semantic processing");
  console.log("• ✅ Improved: Menu search with Groq integration");
  console.log("• ✅ Added: Multi-language support");
  console.log("• ✅ Added: Personalized menu ordering");
  console.log("• ✅ Added: Context-aware recommendations");

  console.log("\n🔧 Required Environment Variables:");
  console.log("• GROQ_API_KEY (required)");
  console.log("• MONGO_URI (required)");
  console.log("• JWT_SECRET (required)");

  console.log("\n🚀 New Features Available:");
  console.log("• Dynamic language detection (Hindi/English/Hinglish)");
  console.log("• Enhanced semantic search using Groq");
  console.log("• Personalized menu ordering based on user history");
  console.log("• Context-aware AI recommendations");
  console.log("• Open-source vector embeddings (no external API costs)");

  console.log("\n📖 Usage Examples:");
  console.log("• GET /api/v1/user/dishes?personalize=true&userId=USER_ID");
  console.log("• GET /api/v1/user/recommendations?message=कुछ अच्छा खाना");
  console.log("• GET /api/v1/user/recommendations?message=something spicy");

  console.log("\n💡 Performance Benefits:");
  console.log("• Zero external API costs for embeddings");
  console.log("• Faster response times with local processing");
  console.log("• Better multilingual support");
  console.log("• More accurate food-specific recommendations");
}

// Run migration
async function runMigration() {
  try {
    await testGroqIntegration();
    await testMenuSearch();
    await migrateEmbeddings();
    await cleanupOldReferences();
    await displayMigrationSummary();

    console.log("\n✨ Migration completed successfully!");
    console.log("🔄 You can now restart your server to use the new features.");
  } catch (error) {
    console.error("\n❌ Migration failed:", error);
    console.log("\n🔧 Troubleshooting:");
    console.log("1. Check your GROQ_API_KEY in .env file");
    console.log("2. Ensure MongoDB connection is working");
    console.log(
      "3. Run 'npm install' to ensure all dependencies are installed"
    );
    console.log("4. Check server logs for detailed error messages");
  } finally {
    console.log("\n👋 Migration process completed.");
    process.exit(0);
  }
}

// Start migration
runMigration();
