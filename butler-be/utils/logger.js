import winston from 'winston';
import DailyRotateFile from 'winston-daily-rotate-file';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Define log levels
const logLevels = {
  error: 0,
  warn: 1,
  info: 2,
  http: 3,
  debug: 4,
};

// Define colors for each log level
const logColors = {
  error: 'red',
  warn: 'yellow',
  info: 'green',
  http: 'magenta',
  debug: 'white',
};

// Add colors to winston
winston.addColors(logColors);

// Define log format
const logFormat = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss:ms' }),
  winston.format.errors({ stack: true }),
  winston.format.json(),
  winston.format.prettyPrint()
);

// Console format for development
const consoleFormat = winston.format.combine(
  winston.format.colorize({ all: true }),
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss:ms' }),
  winston.format.errors({ stack: true }),
  winston.format.printf(
    (info) => `${info.timestamp} ${info.level}: ${info.message}`
  )
);

// Create logs directory if it doesn't exist
const logsDir = path.join(__dirname, '../logs');

// Daily rotate file transport for all logs
const allLogsTransport = new DailyRotateFile({
  filename: path.join(logsDir, 'butler-%DATE%.log'),
  datePattern: 'YYYY-MM-DD',
  zippedArchive: true,
  maxSize: '20m',
  maxFiles: '14d',
  format: logFormat,
});

// Daily rotate file transport for error logs
const errorLogsTransport = new DailyRotateFile({
  filename: path.join(logsDir, 'butler-error-%DATE%.log'),
  datePattern: 'YYYY-MM-DD',
  zippedArchive: true,
  maxSize: '20m',
  maxFiles: '30d',
  level: 'error',
  format: logFormat,
});

// Daily rotate file transport for HTTP logs
const httpLogsTransport = new DailyRotateFile({
  filename: path.join(logsDir, 'butler-http-%DATE%.log'),
  datePattern: 'YYYY-MM-DD',
  zippedArchive: true,
  maxSize: '20m',
  maxFiles: '7d',
  level: 'http',
  format: logFormat,
});

// Create the logger
const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  levels: logLevels,
  format: logFormat,
  defaultMeta: { 
    service: 'butler-api',
    environment: process.env.NODE_ENV || 'development'
  },
  transports: [
    allLogsTransport,
    errorLogsTransport,
    httpLogsTransport,
  ],
  exceptionHandlers: [
    new winston.transports.File({ 
      filename: path.join(logsDir, 'exceptions.log'),
      format: logFormat
    })
  ],
  rejectionHandlers: [
    new winston.transports.File({ 
      filename: path.join(logsDir, 'rejections.log'),
      format: logFormat
    })
  ],
  exitOnError: false,
});

// Add console transport for development
if (process.env.NODE_ENV !== 'production') {
  logger.add(new winston.transports.Console({
    format: consoleFormat,
    level: 'debug'
  }));
}

// Create specialized loggers for different purposes
export const httpLogger = winston.createLogger({
  level: 'http',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json()
  ),
  transports: [httpLogsTransport],
});

export const securityLogger = winston.createLogger({
  level: 'warn',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json()
  ),
  transports: [
    new DailyRotateFile({
      filename: path.join(logsDir, 'butler-security-%DATE%.log'),
      datePattern: 'YYYY-MM-DD',
      zippedArchive: true,
      maxSize: '20m',
      maxFiles: '30d',
      format: logFormat,
    })
  ],
});

export const auditLogger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json()
  ),
  transports: [
    new DailyRotateFile({
      filename: path.join(logsDir, 'butler-audit-%DATE%.log'),
      datePattern: 'YYYY-MM-DD',
      zippedArchive: true,
      maxSize: '20m',
      maxFiles: '90d', // Keep audit logs for 90 days
      format: logFormat,
    })
  ],
});

// Helper functions for structured logging
export const logError = (error, context = {}) => {
  logger.error({
    message: error.message,
    stack: error.stack,
    ...context,
    timestamp: new Date().toISOString()
  });
};

export const logWarning = (message, context = {}) => {
  logger.warn({
    message,
    ...context,
    timestamp: new Date().toISOString()
  });
};

export const logInfo = (message, context = {}) => {
  logger.info({
    message,
    ...context,
    timestamp: new Date().toISOString()
  });
};

export const logHttp = (req, res, responseTime) => {
  const logData = {
    method: req.method,
    url: req.originalUrl,
    statusCode: res.statusCode,
    responseTime: `${responseTime}ms`,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    userId: req.user?.id || 'anonymous',
    contentLength: res.get('Content-Length'),
    timestamp: new Date().toISOString()
  };

  httpLogger.http(logData);
};

export const logSecurity = (event, details = {}) => {
  securityLogger.warn({
    event,
    ...details,
    timestamp: new Date().toISOString()
  });
};

export const logAudit = (action, details = {}) => {
  auditLogger.info({
    action,
    ...details,
    timestamp: new Date().toISOString()
  });
};

// Performance monitoring
export const logPerformance = (operation, duration, context = {}) => {
  logger.info({
    message: `Performance: ${operation}`,
    duration: `${duration}ms`,
    ...context,
    timestamp: new Date().toISOString()
  });
};

// Database operation logging
export const logDatabase = (operation, collection, duration, context = {}) => {
  logger.debug({
    message: `Database: ${operation} on ${collection}`,
    operation,
    collection,
    duration: `${duration}ms`,
    ...context,
    timestamp: new Date().toISOString()
  });
};

// API rate limiting logging
export const logRateLimit = (ip, endpoint, limit, current) => {
  securityLogger.warn({
    event: 'rate_limit_exceeded',
    ip,
    endpoint,
    limit,
    current,
    timestamp: new Date().toISOString()
  });
};

// Authentication logging
export const logAuth = (event, userId, ip, userAgent, details = {}) => {
  auditLogger.info({
    event,
    userId,
    ip,
    userAgent,
    ...details,
    timestamp: new Date().toISOString()
  });
};

// Business logic logging
export const logBusiness = (event, details = {}) => {
  logger.info({
    message: `Business: ${event}`,
    event,
    ...details,
    timestamp: new Date().toISOString()
  });
};

// External API logging
export const logExternalAPI = (service, operation, duration, success, details = {}) => {
  logger.info({
    message: `External API: ${service} - ${operation}`,
    service,
    operation,
    duration: `${duration}ms`,
    success,
    ...details,
    timestamp: new Date().toISOString()
  });
};

// Create logs directory on startup
import fs from 'fs';
if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir, { recursive: true });
}

export default logger;
