import mongoose from 'mongoose';
import { logInfo, logError, logDatabase } from './logger.js';

/**
 * Database optimization utilities for production performance
 */

// Database index definitions for optimal query performance
export const databaseIndexes = {
  // User collection indexes
  users: [
    { email: 1 }, // Unique index for login
    { phone: 1 }, // For phone-based authentication
    { role: 1, isActive: 1 }, // For role-based queries
    { createdAt: -1 }, // For sorting by registration date
    { 'location.city': 1, 'location.pincode': 1 }, // For location-based queries
  ],

  // Order collection indexes
  orders: [
    { userId: 1, createdAt: -1 }, // User order history
    { outletId: 1, status: 1, createdAt: -1 }, // Outlet order management
    { foodChainId: 1, status: 1, createdAt: -1 }, // Food chain analytics
    { status: 1, priority: 1, createdAt: 1 }, // Kitchen queue management
    { orderNumber: 1 }, // Unique order lookup
    { paymentStatus: 1, createdAt: -1 }, // Payment tracking
    { 'assignedTo.userId': 1, status: 1 }, // Staff assignment queries
    { createdAt: -1 }, // General sorting
  ],

  // Dish collection indexes
  dishes: [
    { outletId: 1, isAvailable: 1, category: 1 }, // Menu browsing
    { foodChainId: 1, isAvailable: 1 }, // Food chain menu management
    { category: 1, isVeg: 1, price: 1 }, // Filtering and sorting
    { name: 'text', description: 'text' }, // Text search
    { 'ratings.average': -1, 'ratings.count': -1 }, // Popular dishes
    { price: 1 }, // Price range queries
    { isAvailable: 1, createdAt: -1 }, // Available dishes
  ],

  // Outlet collection indexes
  outlets: [
    { foodChainId: 1, status: 1 }, // Food chain outlet management
    { city: 1, pincode: 1, status: 1 }, // Location-based queries
    { status: 1, isCloudKitchen: 1 }, // Outlet type filtering
    { name: 'text', address: 'text', city: 'text' }, // Text search
    { createdAt: -1 }, // Sorting
  ],

  // Cart collection indexes
  carts: [
    { userId: 1, outletId: 1 }, // User cart lookup
    { userId: 1, updatedAt: -1 }, // Recent cart activity
    { outletId: 1, updatedAt: -1 }, // Outlet cart analytics
  ],

  // Payment collection indexes
  payments: [
    { userId: 1, status: 1, createdAt: -1 }, // User payment history
    { orderId: 1 }, // Order payment lookup
    { razorpayPaymentLinkId: 1 }, // Razorpay integration
    { status: 1, createdAt: -1 }, // Payment status tracking
    { foodChainId: 1, status: 1, createdAt: -1 }, // Food chain analytics
  ],

  // Notification collection indexes
  notifications: [
    { userId: 1, isRead: 1, createdAt: -1 }, // User notifications
    { type: 1, createdAt: -1 }, // Notification type queries
    { isRead: 1, createdAt: -1 }, // Unread notifications
  ],

  // Subscription collection indexes
  subscriptions: [
    { foodChainId: 1, status: 1 }, // Food chain subscription status
    { status: 1, nextBillingDate: 1 }, // Billing management
    { planId: 1, status: 1 }, // Plan analytics
  ],

  // Customer feedback indexes
  customerfeedbacks: [
    { outletId: 1, createdAt: -1 }, // Outlet feedback
    { userId: 1, createdAt: -1 }, // User feedback history
    { orderId: 1 }, // Order feedback lookup
    { rating: -1, createdAt: -1 }, // Rating-based queries
  ],

  // Conversation collection indexes
  conversations: [
    { userId: 1, outletId: 1, createdAt: -1 }, // User conversations
    { outletId: 1, createdAt: -1 }, // Outlet conversation history
    { createdAt: -1 }, // Recent conversations
  ],

  // Audit log indexes
  auditlogs: [
    { userId: 1, action: 1, createdAt: -1 }, // User activity tracking
    { action: 1, createdAt: -1 }, // Action-based queries
    { createdAt: -1 }, // Chronological order
    { 'metadata.orderId': 1 }, // Order-related audits
  ],
};

/**
 * Create database indexes for optimal performance
 */
export const createDatabaseIndexes = async () => {
  try {
    logInfo('Starting database index creation...');
    const db = mongoose.connection.db;
    
    for (const [collectionName, indexes] of Object.entries(databaseIndexes)) {
      try {
        const collection = db.collection(collectionName);
        
        for (const index of indexes) {
          const startTime = Date.now();
          
          // Check if index already exists
          const existingIndexes = await collection.indexes();
          const indexKey = JSON.stringify(index);
          const indexExists = existingIndexes.some(existing => 
            JSON.stringify(existing.key) === indexKey
          );
          
          if (!indexExists) {
            await collection.createIndex(index);
            const duration = Date.now() - startTime;
            logDatabase('index_created', collectionName, duration, {
              index: indexKey
            });
          }
        }
        
        logInfo(`Indexes verified for collection: ${collectionName}`);
      } catch (error) {
        logError(error, { 
          context: 'index_creation',
          collection: collectionName 
        });
      }
    }
    
    logInfo('Database index creation completed');
  } catch (error) {
    logError(error, { context: 'database_index_creation' });
    throw error;
  }
};

/**
 * Analyze query performance and suggest optimizations
 */
export const analyzeQueryPerformance = async (collection, query, options = {}) => {
  try {
    const startTime = Date.now();
    const db = mongoose.connection.db;
    const coll = db.collection(collection);
    
    // Explain the query
    const explanation = await coll.find(query).explain('executionStats');
    const duration = Date.now() - startTime;
    
    const stats = {
      collection,
      query,
      executionTimeMs: explanation.executionStats.executionTimeMillis,
      totalDocsExamined: explanation.executionStats.totalDocsExamined,
      totalDocsReturned: explanation.executionStats.totalDocsReturned,
      indexesUsed: explanation.executionStats.executionStages?.indexName || 'COLLSCAN',
      isOptimal: explanation.executionStats.totalDocsExamined === explanation.executionStats.totalDocsReturned,
      analysisTime: duration
    };
    
    // Log performance metrics
    logDatabase('query_analysis', collection, duration, stats);
    
    // Generate recommendations
    const recommendations = generateQueryRecommendations(stats);
    
    return {
      ...stats,
      recommendations
    };
  } catch (error) {
    logError(error, { context: 'query_performance_analysis', collection, query });
    throw error;
  }
};

/**
 * Generate query optimization recommendations
 */
const generateQueryRecommendations = (stats) => {
  const recommendations = [];
  
  if (stats.indexesUsed === 'COLLSCAN') {
    recommendations.push('Consider adding an index for this query pattern');
  }
  
  if (stats.totalDocsExamined > stats.totalDocsReturned * 10) {
    recommendations.push('Query is examining too many documents - consider more selective indexes');
  }
  
  if (stats.executionTimeMs > 100) {
    recommendations.push('Query is slow - consider optimization or caching');
  }
  
  if (stats.totalDocsReturned > 1000) {
    recommendations.push('Consider implementing pagination for large result sets');
  }
  
  return recommendations;
};

/**
 * Database connection optimization
 */
export const optimizeDatabaseConnection = () => {
  // Set optimal connection settings (these should be set during connection, not globally)
  // mongoose.set('bufferMaxEntries', 0);
  // mongoose.set('bufferCommands', false);

  // Enable query optimization
  if (process.env.NODE_ENV === 'production') {
    mongoose.set('autoIndex', false); // Disable in production
    mongoose.set('autoCreate', false); // Disable in production
  }

  logInfo('Database connection optimized');
};

/**
 * Monitor slow queries
 */
export const monitorSlowQueries = () => {
  // Set up slow query monitoring
  mongoose.set('debug', (collectionName, method, query, doc) => {
    const startTime = Date.now();
    
    // Log slow operations
    setTimeout(() => {
      const duration = Date.now() - startTime;
      if (duration > 100) { // Log queries taking more than 100ms
        logDatabase('slow_query', collectionName, duration, {
          method,
          query: JSON.stringify(query),
          document: doc ? JSON.stringify(doc) : null
        });
      }
    }, 0);
  });
};

/**
 * Database health check
 */
export const checkDatabaseHealth = async () => {
  try {
    const db = mongoose.connection.db;
    const admin = db.admin();
    
    // Get server status
    const serverStatus = await admin.serverStatus();
    
    // Get database stats
    const dbStats = await db.stats();
    
    const health = {
      status: 'healthy',
      connection: {
        state: mongoose.connection.readyState,
        host: mongoose.connection.host,
        name: mongoose.connection.name
      },
      server: {
        version: serverStatus.version,
        uptime: serverStatus.uptime,
        connections: serverStatus.connections
      },
      database: {
        collections: dbStats.collections,
        dataSize: Math.round(dbStats.dataSize / 1024 / 1024 * 100) / 100, // MB
        indexSize: Math.round(dbStats.indexSize / 1024 / 1024 * 100) / 100, // MB
        storageSize: Math.round(dbStats.storageSize / 1024 / 1024 * 100) / 100 // MB
      },
      performance: {
        avgObjSize: Math.round(dbStats.avgObjSize),
        indexes: dbStats.indexes
      }
    };
    
    return health;
  } catch (error) {
    logError(error, { context: 'database_health_check' });
    return {
      status: 'unhealthy',
      error: error.message
    };
  }
};

/**
 * Clean up old data for performance
 */
export const cleanupOldData = async () => {
  try {
    logInfo('Starting database cleanup...');
    const db = mongoose.connection.db;
    
    // Clean up old audit logs (keep last 90 days)
    const auditCutoff = new Date(Date.now() - 90 * 24 * 60 * 60 * 1000);
    const auditResult = await db.collection('auditlogs').deleteMany({
      createdAt: { $lt: auditCutoff }
    });
    
    // Clean up old notifications (keep last 30 days)
    const notificationCutoff = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
    const notificationResult = await db.collection('notifications').deleteMany({
      createdAt: { $lt: notificationCutoff },
      isRead: true
    });
    
    // Clean up old cart operations (keep last 7 days)
    const cartOpCutoff = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
    const cartOpResult = await db.collection('cartoperations').deleteMany({
      createdAt: { $lt: cartOpCutoff }
    });
    
    logInfo('Database cleanup completed', {
      auditLogsDeleted: auditResult.deletedCount,
      notificationsDeleted: notificationResult.deletedCount,
      cartOperationsDeleted: cartOpResult.deletedCount
    });
    
    return {
      auditLogsDeleted: auditResult.deletedCount,
      notificationsDeleted: notificationResult.deletedCount,
      cartOperationsDeleted: cartOpResult.deletedCount
    };
  } catch (error) {
    logError(error, { context: 'database_cleanup' });
    throw error;
  }
};

/**
 * Initialize database optimizations
 */
export const initializeDatabaseOptimizations = async () => {
  try {
    logInfo('Initializing database optimizations...');
    
    // Optimize connection settings
    optimizeDatabaseConnection();
    
    // Create indexes
    await createDatabaseIndexes();
    
    // Set up slow query monitoring
    if (process.env.NODE_ENV !== 'production') {
      monitorSlowQueries();
    }
    
    logInfo('Database optimizations initialized successfully');
  } catch (error) {
    logError(error, { context: 'database_optimization_init' });
    throw error;
  }
};

export default {
  createDatabaseIndexes,
  analyzeQueryPerformance,
  optimizeDatabaseConnection,
  monitorSlowQueries,
  checkDatabaseHealth,
  cleanupOldData,
  initializeDatabaseOptimizations
};
