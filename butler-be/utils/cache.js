import Redis from 'ioredis';
import { logInfo, logError, logPerformance } from './logger.js';

/**
 * Redis-based caching system for production performance
 */

class CacheManager {
  constructor() {
    this.redis = null;
    this.isConnected = false;
    this.fallbackCache = new Map(); // In-memory fallback
    this.stats = {
      hits: 0,
      misses: 0,
      errors: 0,
      operations: 0
    };
  }

  /**
   * Initialize Redis connection
   */
  async initialize() {
    try {
      // Use Redis if available, otherwise fall back to in-memory cache
      if (process.env.REDIS_URL) {
        this.redis = new Redis(process.env.REDIS_URL, {
          retryDelayOnFailover: 100,
          maxRetriesPerRequest: 3,
          lazyConnect: true,
          keepAlive: 30000,
          connectTimeout: 10000,
          commandTimeout: 5000,
        });

        this.redis.on('connect', () => {
          this.isConnected = true;
          logInfo('Redis cache connected successfully');
        });

        this.redis.on('error', (error) => {
          this.isConnected = false;
          this.stats.errors++;
          logError(error, { context: 'redis_cache' });
        });

        this.redis.on('close', () => {
          this.isConnected = false;
          logInfo('Redis cache connection closed');
        });

        await this.redis.connect();
      } else {
        logInfo('Redis not configured, using in-memory cache fallback');
      }
    } catch (error) {
      logError(error, { context: 'cache_initialization' });
      logInfo('Falling back to in-memory cache');
    }
  }

  /**
   * Get value from cache
   */
  async get(key) {
    const startTime = Date.now();
    this.stats.operations++;

    try {
      let value = null;

      if (this.isConnected && this.redis) {
        const cached = await this.redis.get(key);
        if (cached) {
          value = JSON.parse(cached);
        }
      } else {
        // Fallback to in-memory cache
        const cached = this.fallbackCache.get(key);
        if (cached && cached.expiry > Date.now()) {
          value = cached.value;
        } else if (cached) {
          this.fallbackCache.delete(key);
        }
      }

      const duration = Date.now() - startTime;
      
      if (value !== null) {
        this.stats.hits++;
        logPerformance('cache_hit', duration, { key });
        return value;
      } else {
        this.stats.misses++;
        logPerformance('cache_miss', duration, { key });
        return null;
      }
    } catch (error) {
      this.stats.errors++;
      logError(error, { context: 'cache_get', key });
      return null;
    }
  }

  /**
   * Set value in cache
   */
  async set(key, value, ttlSeconds = 300) {
    const startTime = Date.now();
    this.stats.operations++;

    try {
      const serialized = JSON.stringify(value);

      if (this.isConnected && this.redis) {
        await this.redis.setex(key, ttlSeconds, serialized);
      } else {
        // Fallback to in-memory cache
        this.fallbackCache.set(key, {
          value,
          expiry: Date.now() + (ttlSeconds * 1000)
        });

        // Clean up expired entries periodically
        if (this.fallbackCache.size > 1000) {
          this.cleanupFallbackCache();
        }
      }

      const duration = Date.now() - startTime;
      logPerformance('cache_set', duration, { key, ttl: ttlSeconds });
      return true;
    } catch (error) {
      this.stats.errors++;
      logError(error, { context: 'cache_set', key });
      return false;
    }
  }

  /**
   * Delete value from cache
   */
  async del(key) {
    const startTime = Date.now();
    this.stats.operations++;

    try {
      if (this.isConnected && this.redis) {
        await this.redis.del(key);
      } else {
        this.fallbackCache.delete(key);
      }

      const duration = Date.now() - startTime;
      logPerformance('cache_delete', duration, { key });
      return true;
    } catch (error) {
      this.stats.errors++;
      logError(error, { context: 'cache_delete', key });
      return false;
    }
  }

  /**
   * Clear all cache
   */
  async clear() {
    try {
      if (this.isConnected && this.redis) {
        await this.redis.flushdb();
      } else {
        this.fallbackCache.clear();
      }
      logInfo('Cache cleared successfully');
      return true;
    } catch (error) {
      this.stats.errors++;
      logError(error, { context: 'cache_clear' });
      return false;
    }
  }

  /**
   * Get cache statistics
   */
  getStats() {
    const hitRate = this.stats.operations > 0 
      ? (this.stats.hits / this.stats.operations * 100).toFixed(2)
      : 0;

    return {
      ...this.stats,
      hitRate: `${hitRate}%`,
      isRedisConnected: this.isConnected,
      fallbackCacheSize: this.fallbackCache.size
    };
  }

  /**
   * Clean up expired entries in fallback cache
   */
  cleanupFallbackCache() {
    const now = Date.now();
    for (const [key, entry] of this.fallbackCache.entries()) {
      if (entry.expiry <= now) {
        this.fallbackCache.delete(key);
      }
    }
  }

  /**
   * Cache wrapper for functions
   */
  async wrap(key, fn, ttlSeconds = 300) {
    const cached = await this.get(key);
    if (cached !== null) {
      return cached;
    }

    const result = await fn();
    await this.set(key, result, ttlSeconds);
    return result;
  }

  /**
   * Batch operations
   */
  async mget(keys) {
    const startTime = Date.now();
    this.stats.operations += keys.length;

    try {
      const results = {};

      if (this.isConnected && this.redis) {
        const values = await this.redis.mget(...keys);
        keys.forEach((key, index) => {
          if (values[index]) {
            results[key] = JSON.parse(values[index]);
            this.stats.hits++;
          } else {
            results[key] = null;
            this.stats.misses++;
          }
        });
      } else {
        // Fallback to in-memory cache
        const now = Date.now();
        keys.forEach(key => {
          const cached = this.fallbackCache.get(key);
          if (cached && cached.expiry > now) {
            results[key] = cached.value;
            this.stats.hits++;
          } else {
            results[key] = null;
            this.stats.misses++;
            if (cached) {
              this.fallbackCache.delete(key);
            }
          }
        });
      }

      const duration = Date.now() - startTime;
      logPerformance('cache_mget', duration, { keys: keys.length });
      return results;
    } catch (error) {
      this.stats.errors++;
      logError(error, { context: 'cache_mget', keys });
      return {};
    }
  }

  /**
   * Set multiple values
   */
  async mset(keyValuePairs, ttlSeconds = 300) {
    const startTime = Date.now();
    this.stats.operations += Object.keys(keyValuePairs).length;

    try {
      if (this.isConnected && this.redis) {
        const pipeline = this.redis.pipeline();
        
        for (const [key, value] of Object.entries(keyValuePairs)) {
          pipeline.setex(key, ttlSeconds, JSON.stringify(value));
        }
        
        await pipeline.exec();
      } else {
        // Fallback to in-memory cache
        const expiry = Date.now() + (ttlSeconds * 1000);
        for (const [key, value] of Object.entries(keyValuePairs)) {
          this.fallbackCache.set(key, { value, expiry });
        }
      }

      const duration = Date.now() - startTime;
      logPerformance('cache_mset', duration, { 
        keys: Object.keys(keyValuePairs).length 
      });
      return true;
    } catch (error) {
      this.stats.errors++;
      logError(error, { context: 'cache_mset', keys: Object.keys(keyValuePairs) });
      return false;
    }
  }

  /**
   * Disconnect from Redis
   */
  async disconnect() {
    try {
      if (this.redis) {
        await this.redis.disconnect();
        this.isConnected = false;
        logInfo('Redis cache disconnected');
      }
    } catch (error) {
      logError(error, { context: 'cache_disconnect' });
    }
  }
}

// Create singleton instance
const cacheManager = new CacheManager();

// Cache key generators
export const generateCacheKey = (prefix, params = {}) => {
  const sortedParams = Object.keys(params)
    .sort()
    .map(key => `${key}:${params[key]}`)
    .join('|');
  
  return `butler:${prefix}:${sortedParams}`;
};

// Predefined cache keys and TTLs
export const CACHE_KEYS = {
  DISHES: 'dishes',
  OUTLETS: 'outlets',
  MENU: 'menu',
  USER_PREFERENCES: 'user_prefs',
  RECOMMENDATIONS: 'recommendations',
  ANALYTICS: 'analytics',
  SEARCH_RESULTS: 'search',
};

export const CACHE_TTL = {
  SHORT: 60,      // 1 minute
  MEDIUM: 300,    // 5 minutes
  LONG: 1800,     // 30 minutes
  VERY_LONG: 3600 // 1 hour
};

// Cached query helpers
export const cachedQuery = {
  /**
   * Get dishes with caching
   */
  async getDishes(query = {}, options = {}) {
    const { page = 1, limit = 20, populate = [], sort = {} } = options;
    const cacheKey = generateCacheKey(CACHE_KEYS.DISHES, {
      query: JSON.stringify(query),
      page,
      limit,
      populate: populate.join(','),
      sort: JSON.stringify(sort)
    });

    return await cacheManager.wrap(cacheKey, async () => {
      const Dish = (await import('../models/Dish.js')).default;

      let queryBuilder = Dish.find(query)
        .limit(limit)
        .skip((page - 1) * limit);

      if (Object.keys(sort).length > 0) {
        queryBuilder = queryBuilder.sort(sort);
      }

      if (populate.length > 0) {
        populate.forEach(field => {
          queryBuilder = queryBuilder.populate(field);
        });
      }

      return await queryBuilder.lean();
    }, CACHE_TTL.MEDIUM);
  },

  /**
   * Get outlets with caching
   */
  async getOutlets(query = {}, options = {}) {
    const { page = 1, limit = 20, populate = [] } = options;
    const cacheKey = generateCacheKey(CACHE_KEYS.OUTLETS, {
      query: JSON.stringify(query),
      page,
      limit,
      populate: populate.join(',')
    });

    return await cacheManager.wrap(cacheKey, async () => {
      const Outlet = (await import('../models/Outlet.js')).default;

      let queryBuilder = Outlet.find(query)
        .limit(limit)
        .skip((page - 1) * limit);

      if (populate.length > 0) {
        populate.forEach(field => {
          queryBuilder = queryBuilder.populate(field);
        });
      }

      return await queryBuilder.lean();
    }, CACHE_TTL.LONG);
  },

  /**
   * Get user preferences with caching
   */
  async getUserPreferences(userId) {
    const cacheKey = generateCacheKey(CACHE_KEYS.USER_PREFERENCES, { userId });

    return await cacheManager.wrap(cacheKey, async () => {
      const User = (await import('../models/User.js')).default;
      const user = await User.findById(userId)
        .select('preferences dietaryRestrictions favoriteCategories')
        .lean();

      return user?.preferences || {};
    }, CACHE_TTL.LONG);
  },

  /**
   * Get menu with caching
   */
  async getMenu(outletId, options = {}) {
    const { category, isVeg, priceRange } = options;
    const cacheKey = generateCacheKey(CACHE_KEYS.MENU, {
      outletId,
      category: category || 'all',
      isVeg: isVeg || 'all',
      priceRange: priceRange || 'all'
    });

    return await cacheManager.wrap(cacheKey, async () => {
      const Dish = (await import('../models/Dish.js')).default;

      const query = { outletId, isAvailable: true };

      if (category) query.category = category;
      if (isVeg !== undefined) query.isVeg = isVeg;
      if (priceRange) {
        const [min, max] = priceRange.split('-').map(Number);
        query.price = { $gte: min, $lte: max };
      }

      return await Dish.find(query)
        .populate('category', 'name')
        .sort({ 'ratings.average': -1, name: 1 })
        .lean();
    }, CACHE_TTL.MEDIUM);
  }
};

// Cache invalidation helpers
export const invalidateCache = {
  async dishes(outletId) {
    const patterns = [
      `butler:${CACHE_KEYS.DISHES}:*`,
      `butler:${CACHE_KEYS.MENU}:*outletId:${outletId}*`,
      `butler:${CACHE_KEYS.RECOMMENDATIONS}:*`
    ];

    for (const pattern of patterns) {
      await cacheManager.del(pattern);
    }
  },

  async outlets(foodChainId) {
    const patterns = [
      `butler:${CACHE_KEYS.OUTLETS}:*`,
      `butler:${CACHE_KEYS.RECOMMENDATIONS}:*`
    ];

    for (const pattern of patterns) {
      await cacheManager.del(pattern);
    }
  },

  async userPreferences(userId) {
    await cacheManager.del(generateCacheKey(CACHE_KEYS.USER_PREFERENCES, { userId }));
  }
};

// Export cache manager instance
export default cacheManager;
