import { logError, logSecurity } from '../utils/logger.js';

// Custom error classes
export class AppError extends Error {
  constructor(message, statusCode, isOperational = true) {
    super(message);
    this.statusCode = statusCode;
    this.isOperational = isOperational;
    this.status = `${statusCode}`.startsWith('4') ? 'fail' : 'error';
    
    Error.captureStackTrace(this, this.constructor);
  }
}

export class ValidationError extends AppError {
  constructor(message, errors = []) {
    super(message, 400);
    this.errors = errors;
  }
}

export class AuthenticationError extends AppError {
  constructor(message = 'Authentication failed') {
    super(message, 401);
  }
}

export class AuthorizationError extends AppError {
  constructor(message = 'Insufficient permissions') {
    super(message, 403);
  }
}

export class NotFoundError extends AppError {
  constructor(message = 'Resource not found') {
    super(message, 404);
  }
}

export class ConflictError extends AppError {
  constructor(message = 'Resource conflict') {
    super(message, 409);
  }
}

export class RateLimitError extends AppError {
  constructor(message = 'Too many requests') {
    super(message, 429);
  }
}

export class DatabaseError extends AppError {
  constructor(message = 'Database operation failed') {
    super(message, 500, false);
  }
}

export class ExternalServiceError extends AppError {
  constructor(service, message = 'External service error') {
    super(`${service}: ${message}`, 503, false);
    this.service = service;
  }
}

// Error handling for different types of errors
const handleCastErrorDB = (err) => {
  const message = `Invalid ${err.path}: ${err.value}`;
  return new ValidationError(message);
};

const handleDuplicateFieldsDB = (err) => {
  const value = err.errmsg.match(/(["'])(\\?.)*?\1/)[0];
  const message = `Duplicate field value: ${value}. Please use another value!`;
  return new ConflictError(message);
};

const handleValidationErrorDB = (err) => {
  const errors = Object.values(err.errors).map(el => el.message);
  const message = `Invalid input data. ${errors.join('. ')}`;
  return new ValidationError(message, errors);
};

const handleJWTError = () =>
  new AuthenticationError('Invalid token. Please log in again!');

const handleJWTExpiredError = () =>
  new AuthenticationError('Your token has expired! Please log in again.');

const handleMongoNetworkError = () =>
  new DatabaseError('Database connection failed. Please try again later.');

const handleMongoTimeoutError = () =>
  new DatabaseError('Database operation timed out. Please try again.');

// Send error response in development
const sendErrorDev = (err, req, res) => {
  // Log the error
  logError(err, {
    url: req.originalUrl,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    userId: req.user?.id || 'anonymous'
  });

  return res.status(err.statusCode).json({
    success: false,
    error: err,
    message: err.message,
    stack: err.stack,
    timestamp: new Date().toISOString()
  });
};

// Send error response in production
const sendErrorProd = (err, req, res) => {
  // Log the error
  logError(err, {
    url: req.originalUrl,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    userId: req.user?.id || 'anonymous'
  });

  // Operational, trusted error: send message to client
  if (err.isOperational) {
    return res.status(err.statusCode).json({
      success: false,
      message: err.message,
      timestamp: new Date().toISOString(),
      ...(err.errors && { errors: err.errors })
    });
  }

  // Programming or other unknown error: don't leak error details
  console.error('ERROR 💥', err);
  
  return res.status(500).json({
    success: false,
    message: 'Something went wrong!',
    timestamp: new Date().toISOString()
  });
};

// Global error handling middleware
export const globalErrorHandler = (err, req, res, next) => {
  err.statusCode = err.statusCode || 500;
  err.status = err.status || 'error';

  if (process.env.NODE_ENV === 'development') {
    sendErrorDev(err, req, res);
  } else {
    let error = { ...err };
    error.message = err.message;

    // Handle specific error types
    if (error.name === 'CastError') error = handleCastErrorDB(error);
    if (error.code === 11000) error = handleDuplicateFieldsDB(error);
    if (error.name === 'ValidationError') error = handleValidationErrorDB(error);
    if (error.name === 'JsonWebTokenError') error = handleJWTError();
    if (error.name === 'TokenExpiredError') error = handleJWTExpiredError();
    if (error.name === 'MongoNetworkError') error = handleMongoNetworkError();
    if (error.name === 'MongoTimeoutError') error = handleMongoTimeoutError();

    sendErrorProd(error, req, res);
  }
};

// Async error wrapper
export const catchAsync = (fn) => {
  return (req, res, next) => {
    fn(req, res, next).catch(next);
  };
};

// 404 handler
export const notFoundHandler = (req, res, next) => {
  const err = new NotFoundError(`Can't find ${req.originalUrl} on this server!`);
  
  // Log security event for potential scanning
  logSecurity('endpoint_not_found', {
    url: req.originalUrl,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent')
  });
  
  next(err);
};

// Rate limit error handler
export const rateLimitHandler = (req, res, next) => {
  const err = new RateLimitError('Too many requests from this IP, please try again later.');
  
  logSecurity('rate_limit_exceeded', {
    ip: req.ip,
    url: req.originalUrl,
    userAgent: req.get('User-Agent')
  });
  
  next(err);
};

// Validation error handler
export const validationErrorHandler = (errors) => {
  const formattedErrors = errors.array().map(error => ({
    field: error.path || error.param,
    message: error.msg,
    value: error.value
  }));
  
  return new ValidationError('Validation failed', formattedErrors);
};

// Database connection error handler
export const handleDatabaseError = (error) => {
  logError(error, { context: 'database_connection' });
  
  if (error.name === 'MongoNetworkError') {
    return new DatabaseError('Unable to connect to database');
  }
  
  if (error.name === 'MongoTimeoutError') {
    return new DatabaseError('Database operation timed out');
  }
  
  return new DatabaseError('Database error occurred');
};

// External service error handler
export const handleExternalServiceError = (service, error) => {
  logError(error, { 
    context: 'external_service',
    service 
  });
  
  return new ExternalServiceError(service, error.message);
};

// Security event handler
export const handleSecurityEvent = (event, details, req) => {
  logSecurity(event, {
    ...details,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    url: req.originalUrl,
    method: req.method
  });
};

// Performance monitoring middleware
export const performanceMonitor = (threshold = 1000) => {
  return (req, res, next) => {
    const start = Date.now();
    
    res.on('finish', () => {
      const duration = Date.now() - start;
      
      if (duration > threshold) {
        logError(new Error('Slow request detected'), {
          url: req.originalUrl,
          method: req.method,
          duration: `${duration}ms`,
          threshold: `${threshold}ms`,
          ip: req.ip,
          userId: req.user?.id || 'anonymous'
        });
      }
    });
    
    next();
  };
};

// Health check middleware
export const healthCheck = (req, res) => {
  const healthData = {
    success: true,
    message: 'Butler API is healthy',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: process.env.NODE_ENV || 'development',
    memory: {
      used: Math.round(process.memoryUsage().heapUsed / 1024 / 1024 * 100) / 100,
      total: Math.round(process.memoryUsage().heapTotal / 1024 / 1024 * 100) / 100,
      external: Math.round(process.memoryUsage().external / 1024 / 1024 * 100) / 100
    },
    cpu: process.cpuUsage()
  };
  
  res.status(200).json(healthData);
};

export default {
  AppError,
  ValidationError,
  AuthenticationError,
  AuthorizationError,
  NotFoundError,
  ConflictError,
  RateLimitError,
  DatabaseError,
  ExternalServiceError,
  globalErrorHandler,
  catchAsync,
  notFoundHandler,
  rateLimitHandler,
  validationErrorHandler,
  handleDatabaseError,
  handleExternalServiceError,
  handleSecurityEvent,
  performanceMonitor,
  healthCheck
};
