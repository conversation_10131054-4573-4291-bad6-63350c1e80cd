import { body, param, query, validationResult } from 'express-validator';
import mongoose from 'mongoose';

// Custom validator for MongoDB ObjectId
const isValidObjectId = (value) => {
  return mongoose.Types.ObjectId.isValid(value);
};

// Custom validator for strong password
const isStrongPassword = (value) => {
  const strongPasswordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/;
  return strongPasswordRegex.test(value);
};

// Custom validator for phone number
const isValidPhone = (value) => {
  const phoneRegex = /^[\+]?[1-9]?\d{9,15}$/;
  return phoneRegex.test(value);
};

// Custom validator for pincode
const isValidPincode = (value) => {
  const pincodeRegex = /^[1-9][0-9]{5}$/;
  return pincodeRegex.test(value);
};

// Validation error handler
export const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      message: 'Validation failed',
      errors: errors.array().map(error => ({
        field: error.path || error.param,
        message: error.msg,
        value: error.value,
        location: error.location
      }))
    });
  }
  next();
};

// User Registration Validation
export const validateUserRegistration = [
  body('name')
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('Name must be between 2 and 50 characters')
    .matches(/^[a-zA-Z\s]+$/)
    .withMessage('Name can only contain letters and spaces'),
  
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Please provide a valid email address')
    .isLength({ max: 100 })
    .withMessage('Email must not exceed 100 characters'),
  
  body('password')
    .custom(isStrongPassword)
    .withMessage('Password must be at least 8 characters long and contain at least one uppercase letter, one lowercase letter, one number, and one special character'),
  
  body('phone')
    .optional()
    .custom(isValidPhone)
    .withMessage('Please provide a valid phone number'),
  
  body('address')
    .optional()
    .trim()
    .isLength({ max: 200 })
    .withMessage('Address must not exceed 200 characters'),
  
  handleValidationErrors
];

// User Login Validation
export const validateUserLogin = [
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Please provide a valid email address'),
  
  body('password')
    .notEmpty()
    .withMessage('Password is required')
    .isLength({ min: 1, max: 100 })
    .withMessage('Password length is invalid'),
  
  handleValidationErrors
];

// Phone Registration/Login Validation
export const validatePhoneAuth = [
  body('phone')
    .custom(isValidPhone)
    .withMessage('Please provide a valid phone number'),
  
  body('name')
    .optional()
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('Name must be between 2 and 50 characters')
    .matches(/^[a-zA-Z\s]+$/)
    .withMessage('Name can only contain letters and spaces'),
  
  handleValidationErrors
];

// Order Creation Validation
export const validateOrderCreation = [
  body('outletId')
    .custom(isValidObjectId)
    .withMessage('Invalid outlet ID'),

  body('items')
    .isArray({ min: 1 })
    .withMessage('Order must contain at least one item'),

  // Frontend sends items with _id field (from cart), not dishId
  body('items.*._id')
    .custom(isValidObjectId)
    .withMessage('Invalid dish ID'),

  body('items.*.quantity')
    .isInt({ min: 1, max: 50 })
    .withMessage('Quantity must be between 1 and 50'),

  body('items.*.price')
    .isFloat({ min: 0 })
    .withMessage('Price must be a positive number'),

  body('specialInstructions')
    .optional()
    .trim()
    .isLength({ max: 500 })
    .withMessage('Special instructions must not exceed 500 characters'),

  // Table number can be empty string or valid number
  body('tableNumber')
    .optional()
    .custom((value) => {
      // Allow empty string or valid number between 1-999
      if (value === '' || value === null || value === undefined) {
        return true;
      }
      const num = parseInt(value);
      return !isNaN(num) && num >= 1 && num <= 999;
    })
    .withMessage('Table number must be empty or between 1 and 999'),

  body('finalAmount')
    .isFloat({ min: 0 })
    .withMessage('Final amount must be a positive number'),

  handleValidationErrors
];

// Cart Operations Validation
export const validateCartOperation = [
  body('dishId')
    .custom(isValidObjectId)
    .withMessage('Invalid dish ID'),
  
  body('quantity')
    .optional()
    .isInt({ min: 1, max: 50 })
    .withMessage('Quantity must be between 1 and 50'),
  
  body('outletId')
    .custom(isValidObjectId)
    .withMessage('Invalid outlet ID'),
  
  handleValidationErrors
];

// Outlet Query Validation
export const validateOutletQuery = [
  query('city')
    .optional()
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('City must be between 2 and 50 characters')
    .matches(/^[a-zA-Z\s]+$/)
    .withMessage('City can only contain letters and spaces'),
  
  query('pincode')
    .optional()
    .custom(isValidPincode)
    .withMessage('Please provide a valid 6-digit pincode'),
  
  query('page')
    .optional()
    .isInt({ min: 1, max: 1000 })
    .withMessage('Page must be between 1 and 1000'),
  
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100'),
  
  handleValidationErrors
];

// Dish Query Validation
export const validateDishQuery = [
  query('outletId')
    .custom(isValidObjectId)
    .withMessage('Invalid outlet ID'),
  
  query('category')
    .optional()
    .trim()
    .isLength({ min: 1, max: 50 })
    .withMessage('Category must be between 1 and 50 characters'),
  
  query('search')
    .optional()
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('Search term must be between 1 and 100 characters'),
  
  query('minPrice')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Minimum price must be a positive number'),
  
  query('maxPrice')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Maximum price must be a positive number'),
  
  handleValidationErrors
];

// Admin/Super Admin Login Validation
export const validateAdminLogin = [
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Please provide a valid email address'),
  
  body('password')
    .notEmpty()
    .withMessage('Password is required')
    .isLength({ min: 1, max: 100 })
    .withMessage('Password length is invalid'),
  
  handleValidationErrors
];

// Password Update Validation
export const validatePasswordUpdate = [
  body('oldPassword')
    .notEmpty()
    .withMessage('Old password is required'),
  
  body('newPassword')
    .custom(isStrongPassword)
    .withMessage('New password must be at least 8 characters long and contain at least one uppercase letter, one lowercase letter, one number, and one special character'),
  
  body('confirmPassword')
    .custom((value, { req }) => {
      if (value !== req.body.newPassword) {
        throw new Error('Password confirmation does not match new password');
      }
      return true;
    }),
  
  handleValidationErrors
];

// User Profile Update Validation
export const validateProfileUpdate = [
  body('name')
    .optional()
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('Name must be between 2 and 50 characters')
    .matches(/^[a-zA-Z\s]+$/)
    .withMessage('Name can only contain letters and spaces'),
  
  body('phone')
    .optional()
    .custom(isValidPhone)
    .withMessage('Please provide a valid phone number'),
  
  body('address')
    .optional()
    .trim()
    .isLength({ max: 200 })
    .withMessage('Address must not exceed 200 characters'),
  
  handleValidationErrors
];

// Coupon Validation
export const validateCouponApplication = [
  body('couponCode')
    .trim()
    .isLength({ min: 3, max: 20 })
    .withMessage('Coupon code must be between 3 and 20 characters')
    .matches(/^[A-Z0-9]+$/)
    .withMessage('Coupon code can only contain uppercase letters and numbers'),
  
  body('orderAmount')
    .isFloat({ min: 0 })
    .withMessage('Order amount must be a positive number'),
  
  handleValidationErrors
];

// Feedback Validation
export const validateFeedback = [
  body('rating')
    .isInt({ min: 1, max: 5 })
    .withMessage('Rating must be between 1 and 5'),
  
  body('comment')
    .optional()
    .trim()
    .isLength({ max: 1000 })
    .withMessage('Comment must not exceed 1000 characters'),
  
  body('orderId')
    .optional()
    .custom(isValidObjectId)
    .withMessage('Invalid order ID'),
  
  handleValidationErrors
];

// Parameter Validation for Routes
export const validateObjectIdParam = (paramName) => [
  param(paramName)
    .custom(isValidObjectId)
    .withMessage(`Invalid ${paramName}`),
  
  handleValidationErrors
];

// File Upload Validation
export const validateFileUpload = (req, res, next) => {
  if (!req.file) {
    return res.status(400).json({
      success: false,
      message: 'No file uploaded'
    });
  }

  // Check file size (25MB limit)
  if (req.file.size > 25 * 1024 * 1024) {
    return res.status(400).json({
      success: false,
      message: 'File size must not exceed 25MB'
    });
  }

  // Check file type for audio uploads
  if (req.route.path.includes('audio') || req.route.path.includes('transcribe')) {
    const allowedMimeTypes = ['audio/mpeg', 'audio/wav', 'audio/webm', 'audio/ogg', 'audio/mp4'];
    if (!allowedMimeTypes.includes(req.file.mimetype)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid file type. Only audio files are allowed.'
      });
    }
  }

  next();
};

export default {
  validateUserRegistration,
  validateUserLogin,
  validatePhoneAuth,
  validateOrderCreation,
  validateCartOperation,
  validateOutletQuery,
  validateDishQuery,
  validateAdminLogin,
  validatePasswordUpdate,
  validateProfileUpdate,
  validateCouponApplication,
  validateFeedback,
  validateObjectIdParam,
  validateFileUpload,
  handleValidationErrors
};
