import jwt from "jsonwebtoken";
import User from "../models/User.js";

// JWT token blacklist (in production, use Redis)
const tokenBlacklist = new Set();

export const authenticateToken = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(" ")[1];

    if (!token) {
      return res.status(401).json({
        success: false,
        message: "No token provided"
      });
    }

    // Check if token is blacklisted
    if (tokenBlacklist.has(token)) {
      return res.status(401).json({
        success: false,
        message: "Token has been revoked"
      });
    }

    // Verify JWT with proper error handling
    let decoded;
    try {
      decoded = jwt.verify(token, process.env.JWT_SECRET);
    } catch (jwtError) {
      if (jwtError.name === 'TokenExpiredError') {
        return res.status(401).json({
          success: false,
          message: "Token has expired"
        });
      } else if (jwtError.name === 'JsonWebTokenError') {
        return res.status(401).json({
          success: false,
          message: "Invalid token"
        });
      }
      throw jwtError;
    }

    // Validate token structure
    if (!decoded.userId || !decoded.iat) {
      return res.status(401).json({
        success: false,
        message: "Invalid token structure"
      });
    }

    const user = await User.findById(decoded.userId);

    if (!user) {
      return res.status(401).json({
        success: false,
        message: "User not found"
      });
    }

    // Check if user is active
    if (user.status !== 'active') {
      return res.status(401).json({
        success: false,
        message: "Account is not active"
      });
    }

    // Check if password was changed after token was issued
    if (user.lastPasswordChange && decoded.iat < Math.floor(user.lastPasswordChange.getTime() / 1000)) {
      return res.status(401).json({
        success: false,
        message: "Token is invalid due to password change"
      });
    }

    // Set user and userId for compatibility with different controller functions
    req.user = user;
    req.user.userId = user._id; // Add userId property for compatibility
    req.token = token; // Store token for potential blacklisting
    next();
  } catch (error) {
    console.error('Authentication error:', error);
    return res.status(401).json({
      success: false,
      message: "Authentication failed"
    });
  }
};

export const authenticateSuperAdminToken = (req, res, next) => {
  const token = req.header("Authorization")?.split(" ")[1]; // Bearer <token>

  if (!token) {
    return res.status(401).json({
      success: false,
      message: "Access denied, no token provided"
    });
  }

  // Check if token is blacklisted
  if (tokenBlacklist.has(token)) {
    return res.status(401).json({
      success: false,
      message: "Token has been revoked"
    });
  }

  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET_SUPER_ADMIN);

    // Validate token structure
    if (!decoded.email || !decoded.iat) {
      return res.status(401).json({
        success: false,
        message: "Invalid token structure"
      });
    }

    req.user = decoded;
    req.token = token; // Store token for potential blacklisting
    next();
  } catch (jwtError) {
    if (jwtError.name === 'TokenExpiredError') {
      return res.status(401).json({
        success: false,
        message: "Token has expired"
      });
    } else if (jwtError.name === 'JsonWebTokenError') {
      return res.status(401).json({
        success: false,
        message: "Invalid token"
      });
    }
    return res.status(403).json({
      success: false,
      message: "Token verification failed"
    });
  }
};

export const verifyToken = (token) => {
  try {
    return jwt.verify(token, process.env.JWT_SECRET);
  } catch (error) {
    throw new Error(`Token verification failed: ${error.message}`);
  }
};

// Token management functions
export const blacklistToken = (token) => {
  tokenBlacklist.add(token);
};

export const isTokenBlacklisted = (token) => {
  return tokenBlacklist.has(token);
};

// Generate secure JWT tokens with expiration
export const generateAccessToken = (payload, expiresIn = '1h') => {
  if (!process.env.JWT_SECRET) {
    throw new Error('JWT_SECRET environment variable is required');
  }

  return jwt.sign(
    payload,
    process.env.JWT_SECRET,
    {
      expiresIn,
      issuer: 'butler-app',
      audience: 'butler-users',
      algorithm: 'HS256'
    }
  );
};

export const generateRefreshToken = (payload, expiresIn = '7d') => {
  if (!process.env.JWT_REFRESH_SECRET) {
    throw new Error('JWT_REFRESH_SECRET environment variable is required');
  }

  return jwt.sign(
    payload,
    process.env.JWT_REFRESH_SECRET,
    {
      expiresIn,
      issuer: 'butler-app',
      audience: 'butler-users',
      algorithm: 'HS256'
    }
  );
};

export const generateSuperAdminToken = (payload, expiresIn = '2h') => {
  if (!process.env.JWT_SECRET_SUPER_ADMIN) {
    throw new Error('JWT_SECRET_SUPER_ADMIN environment variable is required');
  }

  return jwt.sign(
    payload,
    process.env.JWT_SECRET_SUPER_ADMIN,
    {
      expiresIn,
      issuer: 'butler-app',
      audience: 'butler-super-admins',
      algorithm: 'HS256'
    }
  );
};

// Role-based authorization middleware
export const authorizeRoles = (allowedRoles) => {
  return (req, res, next) => {
    try {
      // Check if user is authenticated
      if (!req.user) {
        return res.status(401).json({
          success: false,
          message: "Authentication required",
        });
      }

      // Check if user has required role
      const userRole = req.user.role;

      if (!userRole || !allowedRoles.includes(userRole)) {
        return res.status(403).json({
          success: false,
          message: "Insufficient permissions",
        });
      }

      next();
    } catch (error) {
      return res.status(500).json({
        success: false,
        message: "Error checking authorization",
      });
    }
  };
};

// Logout functionality
export const logout = (req, res) => {
  try {
    const token = req.token;
    if (token) {
      blacklistToken(token);
    }

    res.status(200).json({
      success: true,
      message: "Logged out successfully"
    });
  } catch (error) {
    console.error('Logout error:', error);
    res.status(500).json({
      success: false,
      message: "Error during logout"
    });
  }
};
