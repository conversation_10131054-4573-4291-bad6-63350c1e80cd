{"name": "butler-be", "version": "1.0.0", "description": "backend for butler", "main": "index.js", "type": "module", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "migrate-to-groq": "node scripts/migrate-to-groq.js", "deploy:optimization": "node scripts/deploy-optimization.js", "rollback:optimization": "node scripts/deploy-optimization.js --rollback"}, "author": "v<PERSON><PERSON>han", "license": "ISC", "dependencies": {"@xenova/transformers": "^2.17.2", "axios": "^1.12.2", "bcrypt": "^5.1.0", "body-parser": "^1.20.3", "compression": "^1.8.1", "cors": "^2.8.5", "dotenv": "^16.4.7", "express": "^4.21.2", "express-mongo-sanitize": "^2.2.0", "express-rate-limit": "^8.1.0", "express-session": "^1.18.1", "express-validator": "^7.2.1", "groq-sdk": "^0.21.0", "helmet": "^8.1.0", "html-pdf": "^3.0.1", "ioredis": "^5.7.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.2.0", "multer": "^2.0.2", "node-cache": "^5.1.2", "node-cron": "^3.0.3", "nodemailer": "^6.10.1", "ollama": "^0.5.14", "passport": "^0.7.0", "passport-google-oauth20": "^2.0.0", "qrcode": "^1.5.4", "razorpay": "^2.9.6", "readline": "^1.3.0", "redis": "^5.8.2", "socket.io": "^4.8.1", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0", "ws": "^8.18.3", "xss-clean": "^0.1.4"}, "devDependencies": {"nodemon": "^3.1.9"}}